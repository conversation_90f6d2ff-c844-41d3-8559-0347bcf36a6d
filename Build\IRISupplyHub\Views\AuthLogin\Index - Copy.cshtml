﻿@using iRISupplyWebDeskAuthLogin.Models;
@model LoginViewModel;
@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@{
    ViewData["Title"] = "Login";
    Layout = "~/Views/_Shared/_BlankLayout.cshtml";
    var session = HttpContextAccessor.HttpContext.Session;
    var appurl = session.GetString("BaseUrl");
}
<style>
    .loginimage{
        height: 238px;
        width: 232px;
        text-align: center !important;
        margin-left: 3px;
        border-color: blue;
        border: #4b4bd2 4px solid;

    }
    .logo{
        box-shadow: 0px 0px 0px 9px white;
        background: #d8d6d6;
    }

    .LoginButton::after{
        text-decoration:underline;
    }
    body {
        height: 100vh;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        /* background-image: url('/assets/images/background.jpg');
        background-size:cover;
        background-repeat:no-repeat; */
    }

    #help {
        font-size: 10px;
        border-style: none;
        border-radius: 50%;
        font-weight: 600;
    }

    #barcode {
        width: 600px;
    }
</style>
<div class="account-pages">
    <div class="container">
        <div >
           <div id="LoginUsingCredential">
    <div >
        <div class="">
            <div class="">
               
                
            </div>
            <div class="card overflow-hidden">
                <div class="bg-soft-primary">
                    <div class="row">
                        <div class="col-7">
                            <div class="text-primary p-4">
                                            <h5 class="text-primary">@ViewBag.ProductName</h5>
                                <p>Login</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <div>
                        <a href="#">
                            <div class="avatar-md profile-user-wid mb-4">
                                <span class="avatar-title rounded-circle bg-light" style="display:contents">
                                    <img src="~/assets/images/maicon.png" alt="" class="rounded-circle logo" height="60">
                                </span>
                            </div>
                        </a>
                    </div>
                    
                    <div class="p-2">
                        <form class="form-horizontal" name="LoginForm" method="post" id="LoginOptionScreen" action=@Url.Action("AuthenticateUserNameForLogin","AuthLogin")>
                            @Html.AntiForgeryToken()
                            <div class="form-group">
                                <label for="userpassword">Username</label>
                                <input type="text" class="form-control" id="username" placeholder="Enter username" name="UserName" asp-for="@Model.UserName" required>
                                <div class="mt-1" style="color:red">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="userpassword">Password</label>
                                <input type="password" class="form-control" id="userpassword" placeholder="Enter password" name="Password" asp-for="@Model.Password" required>
                                @*<input type="text" class="form-control" id="ClientDatetime" placeholder="Enter password" asp-for="@User.Claims" hidden>*@
                                <div class="mt-1" style="color:red">
                                </div>
                            </div>

                            <div class="mt-3" style="color:red">
                                <p id="LoginErrorMessage">@TempData["MyValue"]</p>
                            </div>


                            <div class="mt-3">
                                <button id="loginBtn" class="btn btn-primary btn-block waves-effect waves-light" type="submit">Log In</button>
                            </div>
                            <div class="mt-4 text-center">
                            </div>
                        </form>
                        <div class="mt-4 text-center text-danger">
                            @ViewData["LoginStatus"]
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
        </div>
        <div class="row justify-content-center">
           
            <div class="">
                <div class="text-center">
                    @* <strong>New device found, please scan below barcode to pair.</strong><br />
                    <svg id="barcode"></svg><br /> *@
                    <span>Powered by Mobile Aspects. All rights reserved.</span><br />
                    <span>Version @ViewBag.AppVersion</span>
                    <div class="row justify-content-end">
                        <div class="col-2">
                            <button class="btn btn-info" type="button" data-toggle="modal" data-target="#exampleModal" id="help">Help</button>
                        </div>

                    </div>
                </div>

            </div>

        </div>
        <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">USB Barcode Setting</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <img src="~/assets/images/USB_barcode_Setting.png" width="100%" alt="barcodeSetting" class="img-fluid" />
                        </div>
                        <h6 style="font-weight:bold;" class="mt-3">Note :</h6>

                        <p>•	To configure the enter key as a <suffix> for the set, it is necessary to scan tag 1, tag 2, and tag 3 consecutively.</p>
                        <p>•	 Following the scanning of the three tags, the enter key will be configured for each subsequent scan.</p>


                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/barcodes/JsBarcode.code128.min.js"></script>

<script>
    $("#help").click(function () {

        $('#exampleModal').modal(focus)

    })




    //generate barcode for pairing device with default location

    var Department = "D1"
    var barcodeData = '@ViewBag.ClientIPAddress';
    JsBarcode("#barcode", barcodeData, {
        height: 50,
        displayValue: false

    });
    var appbaseurl = '@appurl';
    let ipAddress = '@ViewBag.ClientIPAddress';
    let url = `${appbaseurl}/deviceRegistrationHub?parmSi=${ipAddress}`;
</script>

<script src="~/microsoft/signalr/dist/browser/signalr.js"></script>
<script src="~/js/DeviceRegistration.js"></script>
