﻿@using iRISupplyWebDeskAuthLogin.Models;
@model LoginViewModel;
@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@{
    ViewData["Title"] = "Login";
    Layout = "~/Views/_Shared/_BlankLayout.cshtml";
    var session = HttpContextAccessor.HttpContext.Session;
    var appurl = session.GetString("BaseUrl");
}
<style>
    .loginimage{
        height: 238px;
        width: 232px;
        text-align: center !important;
        margin-left: 3px;
        border-color: blue;
        border: #4b4bd2 4px solid;

    }
    .logo{
        box-shadow: 0px 0px 0px 9px white;
        background: #d8d6d6;
    }

    .LoginButton::after{
        text-decoration:underline;
    }
    body {
        height: 100vh;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        /* background-image: url('/assets/images/background.jpg');
        background-size:cover;
        background-repeat:no-repeat; */
    }

    #help {
        font-size: 10px;
        border-style: none;
        border-radius: 50%;
        font-weight: 600;
    }

    #barcode {
        width: 600px;
    }

    #LoginBarcodeID {
        border: none;
        background: transparent;
        color: transparent;
    }

        #LoginBarcodeID:focus {
            outline: none;
        }
</style>

<script src="~/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/barcodes/JsBarcode.code128.min.js"></script>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")
<div class="account-pages my-3 pt-sm-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card overflow-hidden" style="width:430px;">
                    <div class="bg-soft-primary">
                        <div class="row">
                            <div class="col-12">
                                <div class="text-primary p-4">
                                    <h5 class="text-primary">@ViewBag.ProductName</h5>
                                    <p>Sign in</p>
                                </div>
                            </div>
                            @*<div class="col-5 align-self-end">
                                <img src="~/assets/images/profile-img.png" alt="" class="img-fluid">
                                </div>*@
                        </div>
                    </div>
                    <div class="card-body pt-0">
                        <div>
                            <a href="#">
                                <div class="avatar-md profile-user-wid mb-4">
                                    <span class="avatar-title rounded-circle bg-light" style="display:contents">
                                        <img src="~/assets/images/maicon.png" alt="" class="rounded-circle logo" height="60">
                                    </span>
                                </div>
                            </a>
                        </div>
                        <div class=" text-center">
                            <h5 style="font-weight:bolder">Please scan your badge to login</h5>
                            <img src="~/assets/images/RFIDbadge.png" class="img-thumbnail loginimage" />
                               <br />
                               <br />
                            <a type="button" data-toggle="modal" data-target="#LoginUsingCredential" style="font-weight: bold;cursor:pointer;color:blue;" class="LoginButton">Login using username & password</a>
                            <span id="errorMessage" class="text-danger" style="font-weight:bolder"></span>
                          <div class="mt-4 text-center">
                            @*<a href="@Url.Action("Password","Authlogin")" class="link-primary">Login using password</a>*@
                           
                        </div>
                        </div>

                    </div>
                </div>

                <div class="mt-4 text-center">

                    <div>
                        @* <p>Don't have an account ? <a href=@Url.Action("Index", "AuthRegister") class="font-weight-medium text-primary"> Signup now </a> </p>*@
                        <span>Powered by Mobile Aspects. All rights reserved.</span><br />
                        <span>Version @ViewBag.AppVersion</span><br />
                       @*  <span>Connected IP @ViewBag.ClientIPAddress</span><br />
                        <span>SessionID @Model.UserSessionID</span> *@
                    </div>
                </div>

            </div>
        </div>
        <div>
            <input type="text" id="LoginBarcodeID" spellcheck="false" />
        </div>
    </div>
</div>
<!-- Button trigger modal -->


<!-- Modal for self register -->
<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Self Register</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action=@Url.Action("SelfRegister","AuthLogin")>
            <div class="modal-body">
                
                    <div class="form-group">
                        <label for="exampleInputEmail1">Username</label>
                        <input type="text" class="form-control" id="Username" name="UserName" asp-for="@Model.UserName"  placeholder="Username" required>
                        <label for="exampleInputEmail1">Password</label>
                        <input type="password" class="form-control" id="Password" name="Password" asp-for="@Model.Password" placeholder="Password" required>
                       
                    </div>
                   @*  <div class="form-group">
                        <label for="exampleInputPassword1">Employee ID</label>
                        <input type="text" class="form-control" id="employeid" asp-for="@Model.EmployeeId" name="EmployeeId" placeholder="EmployeeId">
                    </div> *@
                    <input type="hidden" id="userbadge" asp-for="@Model.RFID" name="RFID" />
                   

              
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Submit</button>
            </div>
            </form>
        </div>
    </div>
</div>
<!-- Modal for login using username password-->
<div class="modal fade" id="LoginUsingCredential" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Login</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="card overflow-hidden">
                <div class="bg-soft-primary">
                    <div class="row">
                        <div class="col-12">
                            <div class="text-primary p-4">
                                <h5 class="text-primary">@ViewBag.ProductName</h5>
                                <p>Sign in</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <div>
                        <a href="#">
                            <div class="avatar-md profile-user-wid mb-4">
                                <span class="avatar-title rounded-circle bg-light" style="display:contents">
                                    <img src="~/assets/images/maicon.png" alt="" class="rounded-circle logo" height="60">
                                </span>
                            </div>
                        </a>
                    </div>
                    
                    <div class="p-2">
                        <form class="form-horizontal" name="LoginForm" method="post" id="LoginOptionScreen" action=@Url.Action("AuthenticateUserNameForLogin","AuthLogin")>
                            @Html.AntiForgeryToken()
                            <div class="form-group">
                                <label for="userpassword">Username</label>
                                <input type="text" class="form-control" id="username" placeholder="Enter username" name="UserName" asp-for="@Model.UserName" required>
                                <div class="mt-1" style="color:red">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="userpassword">Password</label>
                                <input type="password" class="form-control" id="userpassword" placeholder="Enter password" name="Password" asp-for="@Model.Password" required>
                                @*<input type="text" class="form-control" id="ClientDatetime" placeholder="Enter password" asp-for="@User.Claims" hidden>*@
                                <div class="mt-1" style="color:red">
                                </div>
                            </div>

                            <div class="mt-3" style="color:red">
                                <p id="LoginErrorMessage">@TempData["MyValue"]</p>
                            </div>


                            <div class="mt-3">
                                <button id="loginBtn" class="btn btn-primary btn-block waves-effect waves-light" type="submit">Log In</button>
                            </div>
                            <div class="mt-4 text-center">
                            </div>
                        </form>
                        <div class="mt-4 text-center text-danger">
                            @ViewData["LoginStatus"]
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<script>
    
    var appbaseurl = '@appurl';
  
    let error = '@TempData["MyValue"]';
    const invalidBadge = '@TempData["InvalidBadge"]';
    $(document).ready(function () {
        var status = "";
        if ('@ViewBag.selfRegister' != null && '@ViewBag.selfRegister' != "") {
            if ('@ViewBag.selfRegister' == "success") status = "Successfully Registered";
            if ('@ViewBag.selfRegister' == "info") status = "Username alerady exists";
            if ('@ViewBag.selfRegister' == "error") status = "Self-Registration failed try after sometime";
          
                Swal.fire({
                    position: 'center',
                    icon: '@ViewBag.selfRegister',
                    title: status,
                    showConfirmButton: false,
                    timer: 3000
                })
            
        }
        
       
    })

    
</script>

<script src="~/js/Login.js"></script>
<script src="~/js/barcode.js"></script>

