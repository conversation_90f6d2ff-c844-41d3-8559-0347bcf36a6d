﻿@model iRISupplyWebDeskManageReports.Models.ReportContentViewModel
@using Kendo.Mvc.UI;
@using System.Data
@using Newtonsoft.Json;
@using Microsoft.AspNetCore.Http
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@{
    var session = HttpContextAccessor.HttpContext.Session;
    ViewData["Title"] = TempData["ReportName"];
    ViewBag.Title = TempData["ReportName"];
    // ViewBag.Title = ViewBag.reportTitle;
    ViewBag.pTitle = ViewBag.reportTitle;
    ViewBag.pTitle = TempData["ReportName"];
    ViewBag.pageTitle = "Mobile Aspects";
    Layout = "~/Views/_Shared/_Layout.cshtml";
    string colorred = "color:red;";

}
<link rel="stylesheet" href="https://kendo.cdn.telerik.com/2022.2.621/styles/kendo.bootstrap-main.min.css" id="Grid-Styling" />
<style id="gridStyling">



    .fa-plus {
        line-height: 25px;
        margin-right: 6px;
    }



    #grid {
        margin: 10px 0px 10px 0px;
    }



    .k-spacer {
        display: none !important;
    }



    .k-grid-search {
        /*  margin-left: auto;*/
        margin-right: 0;
    }



    .k-menu-link input[type="checkbox"] {
        margin-right: 5px;
    }



    .k-command-cell .btn-danger {
        margin-left: 10px;
    }



    .swal2-container .swal2-title {
        font-size: 19px;
    }





    #grid .k-grid-toolbar {
        padding: .6em 1.3em .6em .4em;
    }



    .category-label {
        vertical-align: middle;
        padding-right: .5em;
    }



    #category {
        vertical-align: middle;
    }



    .refreshBtnContainer {
        display: inline-block;
    }



    .k-grid .toolbar {
        margin-left: auto;
        margin-right: 0;
    }

    .dynamicreport {
        margin-left: 10px !important;
        margin-right: 10px !important
    }
</style>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")
<script type="text/javascript">
    var grid = $("#Grid");
    grid.find(".k-grid-toolbar").on("click", ".k-pager-refresh", function (e) {
        e.preventDefault();
        grid.data("kendoGrid").dataSource.read();
    });
</script>
<div class="dynamicreport">
    @(
        Html.Kendo().Grid<dynamic>()
        .Name("Grid")
        .Columns(columns =>
        {
            foreach (System.Data.DataColumn column in Model.reportData.Columns)
            {

                var c = columns.Bound(column.ColumnName);
                //.HeaderHtmlAttributes(new { style = (!string.IsNullOrEmpty(Model.headerstylesnew)) ?Model.headerstylesnew:"" })
                //.HtmlAttributes(new { style = (!string.IsNullOrEmpty( Model.bodystylesnew))?Model.bodystylesnew:""  });


            }
        })
        .Pageable(p =>
        {
            p.PageSizes(new[] { 10, 20, 50, 100 });
        })
        .Sortable()
        .Scrollable(scr => scr.Height(300))
        @*.Editable(ed => ed.Mode(GridEditMode.PopUp))*@
        .Filterable()
        .ColumnMenu(true)
        .Resizable(resize => resize.Columns(true))
        .Groupable()
        .DataSource(dataSource => dataSource
        .Ajax()
        .Model(model =>
        {
            foreach (System.Data.DataColumn column in Model.reportData.Columns)
            {
                var field = model.Field(column.ColumnName, column.DataType);
            }
        })
        .ServerOperation(false)
        .Read(read => read.Action("GetDynamicReport", "KPIReport"))
        )
        //.Events(e=>e.DataBound("OnDataBound"))
        .ToolBar(toolbar =>
        {
            @*toolbar.Search();
    toolbar.Excel();*@
            toolbar.ClientTemplateId("GridToolbarTemplate");

        })
        )
    <script id="GridToolbarTemplate" type="text/x-kendo-template">

        <div class="refreshBtnContainer">
        <a id="RefreshButton" onclick="RefreshGrid()" class="k-pager-refresh k-link k-button k-button-solid-base k-button-solid k-button-md k-rounded-md k-flat k-icon-button" title="Refresh"><span class="k-icon k-i-reload"></span></a>
        </div>


        <div class="">
        <span class="k-searchbox k-input k-input-md k-rounded-md k-input-solid k-grid-search"><span class="k-input-icon k-icon k-i-search"></span><input autocomplete="off" placeholder="Search..." title="Search..." class="k-input-inner"></span>
        </div>
                <div class="refreshBtnContainer">
                <a role='button' class='k-grid-excel k-button k-button-md k-rounded-md k-button-solid k-button-solid-base mx-4' href='\\#'><span class='k-icon k-i-file-excel'></span>Export to Excel</a>
                <a role='button' class='k-grid-pdf k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' href='\\#'><span class='k-icon k-i-file-pdf '></span>Export to PDF</a>
                </div>
        <div class="">
        @*  <a role='button' onclick="saveGridState('Grid')" class='k-grid-save k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' href='\\#'><span class='far fa-save'></span>Save Report</a> *@
        </div>



    </script>
</div>
<script>
    function RefreshGrid() {
        var grid = $("#Grid");
        grid.data("kendoGrid").dataSource.read();
    }
</script>