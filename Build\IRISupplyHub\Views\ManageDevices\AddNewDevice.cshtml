﻿@using iRISupplyWebDeskDeviceRegistration.Models
@model DeviceRegistrationViewModel
@{
    ViewBag.Title = "Edit Device";
    ViewBag.pTitle = "Edit Device";
    ViewBag.pageTitle = "Manage Devices";
    Layout = "~/Views/_Shared/_Layout.cshtml";
    string selected;
    foreach(var department in Model.Departments)
    {
        
    }
}

<style>
    .required {
        color: red;
    }

    .user-img-span {
        background: #65c9f0;
        display: block;
        width: 145px;
        margin: 0 auto;
        text-align: center;
        height: 140px;
        line-height: 138px;
        border-radius: 50%;
        font-size: 40px;
        letter-spacing: 1px;
    }

    .manage-profiles {
        text-align: center;
    }

    .btn-manage-profile {
        height: 40px;
        padding: 0 15px;
        font-size: 15px;
        margin: 0 15px;
    }

    .spn-manage-profile {
        margin: 0 10px;
        letter-spacing: 0.4px;
    }

    .k-i-collapse::before {
        content: "\25AC";
    }

    .k-i-expand::before {
        content: "\271A";
    }

    .k-icon {
        margin-right: 5px;
    }

    .k-group {
        display: inline-block;
    }
        /*
        .k-group li {
            float: right;
        }*/

        .k-group .k-item {
            margin-right: 25px !important;
        }

    @@media screen and (max-width: 680px) {
        .treeview-flex {
            flex: auto !important;
            width: 100%;
        }
    }

    #demo-section-title h3 {
        margin-bottom: 2em;
        text-align: center;
    }

    .treeview-flex h4 {
        color: #656565;
        margin-bottom: 1em;
        text-align: center;
    }

    #demo-section-title {
        width: 100%;
        flex: auto;
    }

    .treeview-flex {
        flex: 1;
        -ms-flex: 1 0 auto;
    }

    .k-treeview {
        max-width: 240px;
        margin: 0 auto;
    }
    .alert {
        display: none;
    }

   
</style>


<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <form class="needs-validation custom-validation" novalidate asp-action="SendMessageToRabbitMQqueue" asp-controller="ManageDevices">
                    @Html.AntiForgeryToken()
                    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="pills-home-tab" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">Primary</a>
                        </li>
                    </ul>
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                           
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="FirstName"><span>Device ID</span></label>
                                        <input type="text" readonly  class="form-control" id="FirstName" name="DeviceID"  asp-for="@Model.DeviceID">
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="basicpill-middlename-input">Serial Number</label>
                                        <input type="text" class="form-control"  readonly id="basicpill-middlename-input" name="SerialNumber"  asp-for="@Model.SerialNumber">
                                    </div>
                                </div>
                                
                            </div>
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="FirstName"><span>MAC Address</span></label>
                                        <input type="text" readonly  class="form-control" id="FirstName" name="McAddress" asp-for="@Model.McAddress">
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="basicpill-middlename-input">Device Name</label>
                                        <input type="text" class="form-control"  readonly id="basicpill-middlename-input" name="DeviceName"  asp-for="@Model.DeviceName">
                                    </div>
                                </div>
                                
                            </div>
                            <div class="row">
                                @*<div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="basicpill-username-input"><span class="required">Location *</span></label>
                                        <input type="text" class="form-control" id="basicpill-usename-input" asp-for="@Model.Location" required >
                                    </div>
                                </div>*@
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="basicpill-middlename-input">Device IP Address</label>
                                        <input type="text" readonly class="form-control" id="basicpill-middlename-input" name="iPAddress" asp-for="@Model.iPAddress">
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                         <label id="" for="ddnDepartment"> <span class="required">Department *</span></label>
                                        <select id="ddlUserGroups" class="custom-select" name="Department" asp-for="@Model.Department" required>
                                            <option value=""  >Select Location</option>
                                            @foreach(var option in Model.Departments)
                                            {
                                                if (option.Department == Model.Department)
                                                {
                                                    <option value="@option.Department" selected>@option.Department</option>
                                                }
                                                else
                                                {
                                                    <option value="@option.Department" >@option.Department</option>
                                                }
                                            }
                                            
                                        </select>
                                        @*<label for="basicpill-lastname-input"><span class="required">Department *</span></label>*@
                                        @*<input type="text" class="form-control" id="basicpill-lastname-input" asp-for="@Model.Department" required>*@
                                    </div>
                                </div>
                                
                               </div>

                            </div>
                            
                        </div>
                       
                     
                    <div class="row input-field-row flex-row justify-content-end text-right">
                        <div>
                            <button type="reset" class="btn btn-secondary waves-effect" onclick="window.history.back();">
                                Cancel
                            </button>
                            <button type="submit" id="DeviceRegbtn" class="btn btn-primary waves-effect waves-light mr-1" id="submitUserBtn">
                                Submit
                            </button>
                        </div>
                    </div>
                </form>
                </div>
            </div>
         
        </div>
    </div>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

   
   

