﻿@addTagHelper *, iRISupplyWebDeskManageDevices
@using iRISupplyWebDeskDeviceRegistration.Models
@model List<DeviceRegistrationViewModel>
@using Kendo.Mvc.UI
@{
    ViewBag.Title = "Manage Devices";
    ViewBag.pTitle = "Manage Devices";
    ViewBag.pageTitle = "Manage Devices";
    Layout = "~/Views/_Shared/_Layout.cshtml";
    var DeviceStatus = TempData["DeviceStatus"];
}
@*<script src="~/assets/libs/jquery/jquery.min.js"></script>*@
<link rel="stylesheet" href="_content/iRISupplyWebDeskDeviceRegistration/Assets/CSS/Deviceregistration.css" />
@*<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>*@
@*<script src="~/assets/kendo/<EMAIL>"></script>*@
<script src="~/assets/libs/parsleyjs/parsley.min.js"></script>
<script src="~/assets/js/pages/form-validation.init.js"></script>

@(Html.Kendo().Grid(Model)
    .Name("devicesgrid")
    .Columns(columns =>
    {
        //columns.Select().Width(25).HtmlAttributes(new { @class = "checkbox-align" }).HeaderHtmlAttributes(new { @class = "checkbox-align" });
        
        columns.Bound(u => u.DeviceID).Title("Device ID").Width(150)
         .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: left" }).Hidden(true);
                 columns.Bound(u => u.Id).Title("ID").Width(150)
         .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: left" }).Hidden(true);
        columns.Bound(u => u.DeviceName).Title("Device Name").Width(150)
         .HeaderHtmlAttributes(new { @style = "text-align: left; justify-content: left"})
                .HtmlAttributes(new { @style = "text-align: left" });
        columns.Bound(u => u.SerialNumber).Title("Serial Number").Width(180)
         .HeaderHtmlAttributes(new { @style = "text-align: left; justify-content: left"})
                .HtmlAttributes(new { @style = "text-align: left" });
        columns.Bound(u => u.McAddress).Title("MAC ID").Width(180)
         .HeaderHtmlAttributes(new { @style = "text-align: left; justify-content: left"})
                .HtmlAttributes(new { @style = "text-align: left" });
        columns.Bound(u => u.Location).Title("Location").Width(150)
         .HeaderHtmlAttributes(new { @style = "text-align: left; justify-content: left"})
                .HtmlAttributes(new { @style = "text-align: left" }).Hidden(true);
        columns.Bound(u => u.Department).Title("Department").Width(150)
         .HeaderHtmlAttributes(new { @style = "text-align: left; justify-content: left"})
                .HtmlAttributes(new { @style = "text-align: left" }).Hidden(false);
        columns.Bound(p => p.DeviceStatus).Title("Status").Width(100).ClientTemplate("# if (DeviceStatus === 'Active') { # <label class='switch' title='Click to change status'><input type='checkbox' checked onclick='toggleButtonClicked(this)'><span class='slider round'></span></label> # }else { # <label class='switch'><input type='checkbox'onclick='toggleButtonClicked(this)' ><span class='slider round'></span></label> # } #")
        .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: center" });
        columns.Bound("").Title("Reboot").Width(120).ClientTemplate("<i title='Click to reboot' class='fa fa-power-off' onClick='RebootFunction(this)' style='font-size:20px;color:red;cursor:pointer;''></i>")
         .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: center" }).Hidden(true);
                columns.Bound("").Title("Reset").Width(120).ClientTemplate("<i title='Click to reset' class='bx bx-reset'onClick='ResetFunction(this)' style='font-size:23px;color:red;cursor:pointer;''></i>")
         .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: center" }).Hidden(true);
               columns.Bound("").Title("Edit").Width(120).ClientTemplate("<a href='ManageDevices/AddNewDevice?id=#=Id#' title='Click to modify device pairing' class='bx bx-edit' style='font-size:26px;color:blue;cursor:pointer;''></a>")
         .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: center" });
               @* columns.Command(command =>
        {
            command.Custom("Edit").Template("<i class='fas fa-edit editbtn' title='Click to rerigester' role='button' onclick ='SelfRegister(this)'></i>");
        }).Title("Action").Width(80).Hidden(false).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: center" });*@

    })
    
    .Pageable(p =>
    {
        p.PageSizes(new[] { 5, 10, 20 });
    })
    .Selectable(selectable => selectable
            .Mode(GridSelectionMode.Multiple)
            .Type(GridSelectionType.Row))
    .Sortable()
    .Groupable()
    .Scrollable(scr=>scr.Height(300))
    .ToolBar( t =>
    { t.Search();
       
            @*t.Custom().Text("New Device").HtmlAttributes(new { onclick = "AddNewReport();" });*@
         t.Excel();
        t.Pdf();
        
    })
    .Filterable()
    .ColumnMenu(true)
    .Resizable(resize => resize.Columns(true))
    .DataSource(dataSource => dataSource
        .Ajax()
        .GroupPaging(true)
        .PageSize(10)
        .ServerOperation(false)
        @*.Update(u => u.Action("UpdateReport", "ManageReports")).Model(m => { m.Id(p => p.DeviceID); })*@
     )
)
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")
<script src="_content/iRISupplyWebDeskDeviceRegistration/Assets/JS/ManageDevices.js"></script>
<script>
var DeviceStatus='@DeviceStatus';
</script>