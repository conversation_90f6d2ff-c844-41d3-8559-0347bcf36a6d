﻿@using Kendo.Mvc.UI.Fluent
@using Kendo.Mvc.UI;
@using iRSupplyWebDeskManageFeatures.Models;
@{
    ViewData["Title"] = "Manage Features";
    ViewBag.Title = "Manage Features";
    ViewBag.pTitle = "Manage Features";
    var jsonItems = Newtonsoft.Json.JsonConvert.SerializeObject(Model);
    Layout = "~/Views/_Shared/_Layout.cshtml";
}
<style>
    .required {
        color: red;
    }

    .user-img-span {
        background: #65c9f0;
        display: block;
        width: 145px;
        margin: 0 auto;
        text-align: center;
        height: 140px;
        line-height: 138px;
        border-radius: 50%;
        font-size: 40px;
        letter-spacing: 1px;
    }

    .manage-profiles {
        text-align: center;
    }

    .btn-manage-profile {
        height: 40px;
        padding: 0 15px;
        font-size: 15px;
        margin: 0 15px;
    }

    .spn-manage-profile {
        margin: 0 10px;
        letter-spacing: 0.4px;
    }

    .k-i-collapse::before {
        content: "\25AC";
    }

    .k-i-expand::before {
        content: "\271A";
    }

    .k-icon {
        margin-right: 5px;
    }

    .k-group {
        display: inline-block;
    }
        /*
                    .k-group li {
                        float: right;
                    }*/

        .k-group .k-item {
            margin-right: 25px !important;
        }
</style>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<div class="card mt-4">
    <div class="card-body">
        <div class="row">
            <form method="post" id="user" asp-action="UpdateUserPermission" asp-controller="ManageFeatures" class="col-md-12">
                <div class="card">
                    <div class="card-body">

                        <div class="treeview-flex">
                            <div class="demo-section k-content" id="basicpill-treeview-input">
                                @(Html.Kendo().TreeView()
                                    .Name("TreeViewTemplateBiding")
                                    .TemplateId("TreeViewTemplate")
                                    .Checkboxes(c => c.CheckChildren(true)
                                    .Template("<input type='checkbox'  name='#:item.text#' #if(item.checked){# checked #}#/>"))
                                    .Events(events => events.Check("OnCheck"))
                                    .BindTo((IEnumerable<NodeViewModel>)ViewBag.Tree, (NavigationBindingFactory<TreeViewItem> mappings) =>
                                    {
                                        mappings.For<NodeViewModel>(binding => binding.ItemDataBound((item, node) =>
                                        {
                                            item.Id = node.Id.ToString();
                                            item.Text = node.Title.ToUpper();
                                            item.Expanded = node.Expanded;
                                            item.Checked = node.isChecked;

                                        })
                                        .Children(node => node.Children));
                                    })
                                    )
                            </div>
                        </div>
                    </div>
                </div>




                <div class="row input-field-row flex-row justify-content-end text-right">
                    <div class="col-6">
                        <button type="button" class="btn btn-secondary waves-effect" onclick="window.location='@Url.Action("index","ManageFeatures")'">
                            <span>Cancel</span>
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <span>Submit</span>
                        </button>
                    </div>
                </div>
            </form>

        </div>

    </div>





</div>

<script>
    $(document).ready(function () {
        if ('@ViewBag.success' == "true") {
            debugger;
            ToastAlert()

        }

        function ToastAlert() {

            Toast.fire({
                icon: 'success',
                title: "Updated Successfully!"
            })
        }
    })




    if ('@TempData["UserID"]' != '') {

        var UserID = '@TempData["UserID"]';
        localStorage.setItem("UserID", UserID);

    }


    function checkedNodeIds(nodes, checkedNodes, unCheckedNodes) {

        for (var i = 0; i < nodes.length; i++) {
            if (nodes[i].checked) {
                checkedNodes.push(nodes[i].text);
                console.log('father' + checkedNodes);
            }
            if (!nodes[i].checked) {
                unCheckedNodes.push(nodes[i].text);
            }


            if (nodes[i].hasChildren) {

                checkedNodeIds(nodes[i].children.view(), checkedNodes, unCheckedNodes);

                console.log('child' + checkedNodes);
            }
        }
    }
    function OnCheck(e) {
        var ActiveTab = $(".FeatureItem").find('a.active').text();

        var checkedNodes = [],
            treeView = $("#TreeViewTemplateBiding").data("kendoTreeView"),
            message, unCheckedNodes = [];

        checkedNodeIds(treeView.dataSource.view(), checkedNodes, unCheckedNodes);
        if (checkedNodes.length > 0) { var clu = checkedNodes.join("|"); }
        else { var clu = "None" }
        if (unCheckedNodes.length > 0) { var clu1 = unCheckedNodes.join("|"); }
        else { var clu1 = "None" }

        var model = {
            ModuleName: clu,
            UnCheckedModuleName: clu1,
            AllModules: "ALL",
            ApplicationType: ActiveTab
        };

        var url = "@Url.Action("CheckedModels", "ManageFeatures")";
        $.get(url, model, function (res) {
            console.log("Here");
        });
    }

    function checkedReportNodeIds(nodesR, checkedReportNodes) {
        for (var i = 0; i < nodesR.length; i++) {
            if (nodesR[i].checked) {
                checkedReportNodes.push(nodesR[i].id);
            }
            if (nodesR[i].hasChildren) {
                checkedReportNodeIds(nodesR[i].children.view(), checkedReportNodes);
            }
        }
    }


    function checkedReportNodeIds(nodesR, checkedReportNodes) {
        for (var i = 0; i < nodesR.length; i++) {
            if (nodesR[i].checked) {
                checkedReportNodes.push(nodesR[i].id);
            }
            if (nodesR[i].hasChildren) {
                checkedReportNodeIds(nodesR[i].children.view(), checkedReportNodes);
            }
        }
    }

    $(function () {
        $("#chkLeadAlert").click(function () {
            if ($(this).is(":checked")) {
                $("#divLeadAlert").show();
            } else {
                $("#divLeadAlert").hide();
            }
        });

        $("#chkEmailAlert").click(function () {
            if ($(this).is(":checked")) {
                $("#divEmailAlert").show();
            } else {
                $("#divEmailAlert").hide();
            }
        });
    });

</script>







