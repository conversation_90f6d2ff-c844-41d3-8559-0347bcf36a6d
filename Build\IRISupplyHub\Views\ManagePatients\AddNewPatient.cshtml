﻿@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@using iRISupplyWebDeskManagePatients.Models
@model PatientsViewModel
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Kendo.Mvc.UI
@{
    ViewBag.Title = "Add New Patient";
    ViewBag.pTitle = "Add New Patient";
    ViewBag.pageTitle = "Add New Patient";
    Layout = "~/Views/_Shared/_Layout.cshtml";
    
}
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")
<style>
    #required {
        color: red;
    }

    .fa-plus {
        line-height: 25px;
        margin-right: 6px;
    }

    #grid {
        margin: 10px 0px 10px 0px;
    }

    .k-spacer {
        display: none !important;
    }

    .k-grid-search {
        margin-left: auto;
        margin-right: 0;
    }

    .k-grid-content {
        height: 360px !important;
    }
</style>

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <form class="needs-validation custom-validation" novalidate action="@Url.Action("AddNewPatientToDB", "ManagePatients")">
                    @Html.AntiForgeryToken()
                    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                        @* <li class="nav-item">
                            <a class="nav-link active" id="pills-home-tab" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">Primary</a>
                        </li> *@
                    </ul>
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">

                            <div class="row">
                                <div class="col-sm-6">
                                 
                                    <div class="form-group">
                                        <label for="FirstName">First Name <span id="required">*</span></label>
                                        <input type="text" class="form-control" name="FirstName" asp-for="@Model.FirstName" placeholder="Enter First name" required>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="MiddleName">Middle Name </label>
                                        <input type="text" class="form-control" name="MiddleName" asp-for="@Model.MiddleName" placeholder="Enter Middle name">
                                    </div>
                                </div>

                            </div>
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="LastName">Last Name <span id="required">*</span></label>
                                        <input type="text" class="form-control" name="LastName" asp-for="@Model.LastName" placeholder="Enter Last name" required>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="PatientId">Patient Id <span id="required">*</span></label>
                                        <input type="text" class="form-control" name="PatientId" asp-for="@Model.PatientID" placeholder="Enter Patient Id" required>
                                    </div>
                                </div>

                            </div>
                            <div class="row">
                               
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="AppointmentID">Appointment ID <span id="required">*</span></label>
                                        <input type="text" class="form-control" name="AppointmentID" asp-for="@Model.AppointmentID" placeholder="Enter Appointment ID" required>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="AdmitDate">Admit Date <span id="required">*</span></label>
                                        <input type="date" class="form-control" name="AdmitDate" asp-for="@Model.AdmitDate" required>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="ProcedureDate">Procedure Date <span id="required">*</span></label>
                                        <input type="date" class="form-control" name="ProcedureDate" asp-for="@Model.ProcedureDate" required>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label for="AccountNumber">Account Number</label>
                                        <input type="text" class="form-control" name="AccountNo" data-parsley-type="number"
                                               oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                               placeholder="Enter Account Number" asp-for="@Model.AccountNo">
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>


                    <div class="row input-field-row flex-row justify-content-end text-right">
                        <div>
                            <button type="reset" class="btn btn-secondary waves-effect" onclick="window.history.back();">
                                Cancel
                            </button>
                            <button type="submit" id="DeviceRegbtn" class="btn btn-primary waves-effect waves-light mr-1" id="submitUserBtn">
                                Submit
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

    </div>
</div>
