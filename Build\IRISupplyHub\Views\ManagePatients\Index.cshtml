﻿@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@*@addTagHelper *, iRISupplyWebDeskDeviceRegistration*@
@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@using iRISupplyWebDeskManagePatients.Models
@model PatientPageData

@using Kendo.Mvc.UI
@{
    ViewBag.Title = "Remove Item";
    ViewBag.pTitle = "Patient List";
    ViewBag.pageTitle = "Remove Item";
    Layout = "~/Views/_Shared/_Layout.cshtml";
    var UserID = HttpContextAccessor.HttpContext.Session.GetInt32("UserID").ToString();
    string patientStatus = HttpContextAccessor.HttpContext.Session.GetString("AddNewPatient");
    HttpContextAccessor.HttpContext.Session.Remove("AddNewPatient");
    var session = HttpContextAccessor.HttpContext.Session;
    var appurl = session.GetString("BaseUrl");

}

<style>
    .fa-plus {
        line-height: 25px;
        margin-right: 6px;
    }

    #grid {
        margin: 10px 0px 10px 0px;
    }

    .k-spacer {
        display: none !important;
    }

    .k-grid-search {
        margin-left: auto;
        margin-right: 0;
    }



    .k-menu-link input[type="checkbox"] {
        margin-right: 5px;
    }

    .k-command-cell .btn-danger {
        margin-left: 10px;
    }



    .fas.fa-edit {
        color: white;
    }

    .k-grid-header .k-header {
        /*background-color: #556ee6;*/
        color: black;
    }

    .colored-text {
        color: #556ee6;
        text-decoration: underline;
    }

    .k-grid tr {
        height: 50px;
    }

    .patient-selection-class {
        padding-bottom: 50px;
    }
</style>
<style>
    /*.k-grid-header .k-header {
                    height: 20px;
                    padding: 0;
                }*/

    .k-grid tbody tr {
        line-height: 14px;
    }

    .containerss {
        margin-right: 10px;
        margin-left: 10px;
    }

    .k-grid .toolbar {
        margin-left: auto;
        margin-right: 0;
    }

    .btn_disabled {
        pointer-events: none;
    }

    .btn_case {
        background: #3452e1 !important;
        color: white !important;
    }

    .btn_other {
        width: 115.900px;
    }

    #modal-main {
        height: 70%;
    }

    #modal-main-content {
        height: 70%;
        background: #eaeaf1;
    }

    .Reason-container {
        /* background-image: linear-gradient(45deg, #0000005e, #050aff); */
        background: #2a3042;
        color: white;
    }

        .Reason-container:hover {
            cursor: pointer;
            transition: all 1s ease;
        }

    #modal_body_content {
        overflow: scroll;
    }

    .modal-popup-title {
        font-weight: bolder;
        color: #0b0b1c;
    }

    .newReason {
        font-weight: bolder;
        color: #0b0b1c;
    }

    .Reason-container:hover {
        background-image: linear-gradient(45deg, #0000005e, #160685);
    }

    #required {
        color: red;
    }
    /*.k-grid tbody td {
                    padding: 0;
                }*/
</style>

<!-- JAVASCRIPT -->
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")
<script>var UserID = '@UserID'</script>

<div class="containerss">

    @(
        Html.Kendo().Grid(Model.patients)
        .Name("patientsgrid")
        // .ClientRowTemplate(rows =>rows.Height="1050px")
        //.ClientAltRowTemplateHandler(row => row.he)
        .Columns(columns =>
        {
            columns.Bound(u => u.MAPatientID).Title("MA Patient ID").Hidden(true);
            columns.Bound(u => u.PhysicianName).Title("Physician Name").Hidden(true);
            columns.Bound(u => u.Procedure).Title("Procedure").Hidden(true);
            columns.Bound(u => u.VisitID).Title("VisitID").Hidden(true);
            columns.Bound(u => u.MAScheduleID).Title("MAScheduleID").Hidden(true);
            columns.Bound(u => u.PatientName)
            .Title("Patient Name").Width(300)
            .ClientTemplate("<pname style='text-align: center !important'><b>#=PatientName#</b></pname>").HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center;padding-left:25px !important;" });
            @*columns.Bound(u => u.PatientRoom).Title("Patient Room").Width(100).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" }).Hidden(true);*@
            @*.HtmlAttributes(new { @style = "text-align: center"  })*@;

            columns.Bound(u => u.PatientMRN)
            .Title("MRN / CSN").Width(220)
            .ClientTemplate("<pname style='text-align: center'><b>#=PatientMRN#</b></pname><br> </br> +<b>#=CSN#</b><br> </br>").HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center;padding-left:25px !important;" });
            //.ClientTemplate("Patient Name:+<pname style='text-align: center'><b>#=PatientName#</b></pname><br> </br> MRN:+<b>#=PatientID#</b><br> </br>").Width(120).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" });

            // columns.Select().Width(25).HtmlAttributes(new { @class = "checkbox-align" }).HeaderHtmlAttributes(new { @class = "checkbox-align" });
            @*columns.Bound(u => u.PatientID).Title("Patient ID").Width(150).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
    .HtmlAttributes(new { @style = "text-align: center" }) ;
    columns.Bound(u => u.PatientName).Title("Patient Name").Width(150).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
    .HtmlAttributes(new { @style = "text-align: center" });
    *@
            columns.Bound(u => u.CaseID)
    .Title("Case ID / Procedure Description").Width(280)
    .ClientTemplate("<b>#=CaseID#</b><br> </br> +<b>#=Procedure#</b><br> </br>").HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center;padding-left:25px !important;" });
            @* columns.Bound(u => u.AdmitDate).Title("Admit Date").Width(150).Hidden(true).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
    .HtmlAttributes(new { @style = "text-align: center" });
    columns.Bound(u => u.ReferringDoc).Title("Referring Doctor").Width(150).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
    .HtmlAttributes(new { @style = "text-align: center" });
    columns.Bound(u => u.procdate).Title("Procedure Date").Format("{0:MM/dd/yy HH:mm:ss}").Width(150).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
    .HtmlAttributes(new { @style = "text-align: center" });*@
            //columns.Bound(u => u.Status).Title("Status").EditorTemplateName("StatusEditor").Width(90);
            //columns.Bound("").Title("Remove Item").Width(60).ClientTemplate("<a href='/RemoveItem/Index?PatientID=#=MAPatientID#' title='Click to remove item' onclick='return IfPatientIsAlreadySelected(#=MAPatientID#)' class='fas fa-edit' style='font-size:26px;color:blue;cursor:pointer;''></a>")
            columns.Bound("").Title("Use Item").Width(100).ClientTemplate("<a onclick='RemoveItemsAgainstPatient(this)'  title='Click to remove item' class='fas fa-arrow-circle-right fa-2x' style='color:blue;cursor:pointer;''></a>")
            .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
            .HtmlAttributes(new { @style = "text-align: center" });
            @*columns.Bound("").Title("Remove Item").Width(60).ClientTemplate("<a href='/RemoveItem/Index?PatientID=#=MAPatientID#' title='Click to remove item' class='fas fa-edit' style='font-size:26px;color:blue;cursor:pointer;''></a>")
    .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
    .HtmlAttributes(new { @style = "text-align: center" });*@
            @*columns.Command(command =>
    {
    //  command.Custom("Edit").Template("<a role='button'  href='/RemoveItem/index?Id='+#=PatientID# class='btn btn-primary'><i class='fas fa-edit'></i></a>");

    command.Custom("Edit").Template("<button role='button' onclick ='RemoveItems(this)' class='btn btn-primary'><i class='fas fa-edit'></i></button>");

    }).Title("Remove Item").Width(80).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
    .HtmlAttributes(new { @style = "text-align: center" });*@
        })
        //.Editable(editable => editable.Mode(GridEditMode.PopUp))
        //.Editable(editable => editable.Mode(GridEditMode.InCell))
        .Pageable(p =>
        {
            p.PageSizes(new[] { 5, 10, 20 });
            p.Messages(m => m.Display("Count{5}"));
        })
        .Selectable(selectable => selectable
        .Mode(GridSelectionMode.Single)
        .Type(GridSelectionType.Row))
        .Sortable()
        .Groupable()
        .Scrollable(scr => scr.Height(300))
        .ToolBar(t =>
        {
            // t.Search();
            t.ClientTemplateId("GridToolbarTemplate");

            // t.Custom().Text("Add New Patient").HtmlAttributes(new { onclick = "AddNewPatient();" });
            @*t.Excel();*@

        })
        .Events(e => e.Change("onRowSelection"))
        .Filterable()
        .ColumnMenu(true)
        .Resizable(resize => resize.Columns(true))
        .DataSource(dataSource => dataSource
        .Ajax()
        .GroupPaging(true)
        .PageSize(10)
        .ServerOperation(false)
        @*.Update(u => u.Action("UpdateReport", "ManageReports")).Model(m => { m.Id(p => p.DeviceID); })*@
        )
        )
    <script id="GridToolbarTemplate" type="text/x-kendo-template">
          <div>
          <span class="k-searchbox k-input k-input-md k-rounded-md k-input-solid k-grid-search"><span
              class="k-input-icon k-icon k-i-search"></span><input autocomplete="off" placeholder="Search..." title="Search..."
              class="k-input-inner"></span>

        </div>
                <div class="toolbar">
          <div class="mr-4">
            <button id='button_openCase' disabled role='button' type='button'
              class='btn btn-primary'
              onclick='RemoveOpenCase()'><span class='h5 text-light'><i class="fas fa-lock-open"></i> Open Case</span></button>
          </div>
          <div class="">
            <button role='button'
              class='btn btn-primary'
              data-toggle='modal' onclick='ShowOtherOptions()'><span class='h5 text-light'><i class="fas fa-trash"></i> Waste Items</span></button>
          </div>
        </div>
    </script>
</div>



<!-- Modal for other option-->
<div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" id="modal-main" role="document">
        <div class="modal-content" id="modal-main-content">
            <div class="modal-header">
                <h5 class="modal-title modal-popup-title" id="exampleModalLongTitle">Other option selected, please provide reason for removing item</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal_body_content">
                <div class="container-fluid">
                    <div class="row">
                        @foreach (var item in Model.overrideNotes)
                        {
                            <div class="col-md-4">
                                <div class="Reason-container card mini-stats-wid">
                                    <div class="card-body">
                                        <div class="d-flex">
                                            <div class="flex-grow-1">
                                                <h4 class="text-white text-center mb-0 overridereasontext text-truncate" sytle="width:150px;">@item.OverrideNotes</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>

                </div>
            </div>
            <div class="modal-footer">
                <div class="col-sm-10 col-md-12">
                    <div class="row d-flex justify-content-center align-items-center">

                        <div class="col-sm-2">
                            <label for="newReason" class="newReason">New reason:</label>
                        </div>
                        <div class="col-sm-6 col-md-8">
                            <input type="text" id="newReason" class="form-control newRemovalReason">
                        </div>
                        <div class="col-sm-1">
                            <button type="button" class="btn btn-primary newReasonBtn" onclick='AddOtherReasonCase()' disabled>
                                <span>
                                    Save
                                </span>
                            </button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- modal for new patient-->

<script>
    
    var base_url = '@appurl'
  
    $(".newRemovalReason").on("input", function () {
        if ($(this).val().trim().length > 0) {
            $(".newReasonBtn").attr('disabled', false);
        }
        else {
            $(".newReasonBtn").attr('disabled', true);
        }
    })

    $(".Reason-container").click(function () {
        // Get the inner text of the h3 element inside the clicked code block
        const clickedReason = $(this).find(".overridereasontext").text();
        var url = "@Url.Action("SetPateintDetails", "RemoveItem")";
        model = {};
        model.isopenCase = 'false';
        model.OverrideNote = clickedReason;
       
        $.get(url, model, function (res) {

            window.location.href = `${base_url}/RemoveItem/Index`;
        });
    });


    function ShowOtherOptions() {
        $('#exampleModalCenter').modal('toggle');
        $('body').css('pointer-events', 'none');
    }
     $('#exampleModalCenter').on('hidden.bs.modal', function () {
        $('body').css('pointer-events', 'auto');
    });

    let PatientDetails = {};
    let model = {};
    function onRowSelection(e) {
        debugger;
        PatientDetails = this.dataItem(this.select());
        PreparePatientDetailsModel()

        if (PatientDetails.length != 0) {
            $('#button_openCase').attr("disabled", false);
            $('#usagebtn').attr("disabled", false);
        }
        else {
            $('#button_openCase').attr("disabled", true);
            $('#usagebtn').attr("disabled", false);
        }
    }
    function RemoveOpenCase() {
        var url = "@Url.Action("SetPateintDetails", "RemoveItem")";
        model.isopenCase = 'true';
        $.get(url, model, function (res) {
            console.log(model);
            window.location.href = `${base_url}/RemoveItem/Index`;
        });
    }
    function AddOtherReasonCase() {
        let newRemovalReason = $(".newRemovalReason").val();
        var url = "@Url.Action("SetPateintDetails", "RemoveItem")";
        model.isopenCase = 'false';
        model.NewRemovalReason = newRemovalReason;
        model.OverrideNote = newRemovalReason;
        $.get(url, model, function (res) {
            console.log(model);
            window.location.href = `${base_url}/RemoveItem/Index`;
        });
    }
    //let PatientDetails;
    function RemoveItemsAgainstPatient(e) {
        var grid = $("#patientsgrid").data("kendoGrid");
        PatientDetails = grid.dataItem(e.closest('tr'));
        PreparePatientDetailsModel();
        // let isAlreadySelected = IfPatientIsAlreadySelected(model.MAPatientID);
        let isAlreadySelected = false;
        // if (isAlreadySelected) {
        //     var UserName;
        //     var PatientName;
        //     var Room;
        //     let roomDetailsArray = RoomSelectionDetails.split(', ');
        //     let roomInfoString = roomDetailsArray.find(info => info.includes(`PatientId=${model.MAPatientID}`));
        //     if (roomInfoString) {
        //         UserName = roomInfoString.match(/UserName=(.*?(?= Room))/)[1].trim();
        //         PatientName = roomInfoString.match(/PatientName=(.*?(?= UserId))/)[1].trim();
        //         Room = roomInfoString.match(/Room=(.*)$/)[1];

        //         Swal.fire({
        //             //title: 'Are you sure?',
        //             html: `<p>${UserName} is currently Removing items for ${PatientName} in ${Room}.</p>
        //                     <h3>Do you still want to continue to remove items.</h3>`,
        //             icon: 'warning',
        //             showCancelButton: true,
        //             confirmButtonColor: '#3085d6',
        //             cancelButtonColor: '#d33',
        //             confirmButtonText: 'Continue'
        //         }).then((result) => {
        //             if (result.isConfirmed) {
        //                 var url = "@Url.Action("SetPateintDetails", "RemoveItem")";
        //                 model.isopenCase = 'false';
        //                 $.get(url, model, function (res) {
        //                     window.location.href = '/RemoveItem/Index';
        //                 });
        //             }
        //         })
        //     }

        // }
        // else {
        var url = "@Url.Action("SetPateintDetails", "RemoveItem")";
        model.isopenCase = 'false';
        $.get(url, model, function (res) {
            window.location.href = `${base_url}/RemoveItem/Index`;
        });
        

       
    }
    //function for removal against unknown patient

    function otherButtonEvent() {
        alert("other button clicked");
    }


    function PreparePatientDetailsModel() {
        model = {
            MAPatientID: PatientDetails.MAPatientID,
            PatientName: PatientDetails.PatientName,
            PatientMRN: PatientDetails.PatientMRN,
            CASEID: PatientDetails.CaseID,
            PatientCSN: PatientDetails.CSN,
            ProcedureDescription: PatientDetails.Procedure,
            PhysicianName: PatientDetails.PhysicianName,
            MAVisitID: PatientDetails.VisitID,
            MAScheduleID: PatientDetails.MAScheduleID

        };
    }

    // $(document).ready(function () {
    //     $.ajax({
    //         type: 'get',
    //         url: '@Url.Action("RemoveSelectedPatientsForUser", "ManagePatients")'
    //     })
    // })
    function IfPatientIsAlreadySelected(patientId) {

        //var SelectedPatientsList=InvokeUpdatedPatientList();

        let currentPatientsSelected = SelectedPatients;

        // let currentPatientsSelected ='@ViewBag.SelectedPatients';
        let currentPatientsArray = currentPatientsSelected.split(",");
        let indexOfPatientID = currentPatientsArray.indexOf(patientId.toString());
        //let isSelected = currentPatientSelected==patientId?true:false;
        if (indexOfPatientID >= 0) {

            return true;
        }
        else {
            return false;
        }
    }
    function GetPateintUsageDetails() {
        debugger;



        var grid = $("#patientsgrid").data("kendoGrid");
        var selectedRow = grid.select();

        if (selectedRow.length > 0) {
            var dataItem = grid.dataItem(selectedRow);
            PatientDetails = dataItem;
            PreparePatientDetailsModel();

            var url = "@Url.Action("SetPateintDetails", "RemoveItem")";



            $.get(url, model, function (res) {

                console.log("Patient Details set successfully")

            });

            $.ajax({
                type: 'get',
                url: '@Url.Action("GetPatientUsage", "RemoveItem")',
                data: { MAScheduleID: dataItem.MAScheduleID }

            })
                .done(function (response) {
                    $(".containerss").html(response);
                    $(".PatienUsage").addClass("active_Module");
                    $(".PatientList").removeClass("active_Module");
                    localStorage.setItem("view", "partial");
                })
        } else {
            // $("#usagebtn").attr("disabled", true);
            Swal.fire({
                position: 'center',
                icon: 'error',
                title: 'Please select a patient.',
                showConfirmButton: false,
                timer: 3000,
                customClass: {
                    popup: 'Remove-selection-class'
                }
            })
        }
        return false;
    }

    function RemovePatientDetails() {
        debugger;
        var grid = $("#patientsgrid").data("kendoGrid");
        var selectedRow = grid.select();

        if (selectedRow.length == 1) {
            var dataItem = grid.dataItem(selectedRow);
            RemoveItemsAgainstPatient(selectedRow)

        } else if (selectedRow.length > 1) {
            Swal.fire({
                position: 'center',
                icon: 'info',
                title: 'Please select one patient.',
                showConfirmButton: false,
                timer: 3000,
                customClass: {
                    popup: 'Remove-selection-class'
                }
            })
        }
        else {
            // $("#usagebtn").attr("disabled", true);
            Swal.fire({
                position: 'center',
                icon: 'error',
                title: 'Please select a patient.',
                showConfirmButton: false,
                timer: 3000,
                customClass: {
                    popup: 'Remove-selection-class'
                }
            })
        }
        return false;
    }

    //add new patients
    function AddNewPatients() {
        $('#addnewpatient').modal('toggle');
    }


    const PatientStatus = '@patientStatus'
    if (PatientStatus != null && PatientStatus != "") {
        Swal.fire({
            position: 'center',
            icon: 'success',
            title: PatientStatus,
            showConfirmButton: false,
            timer: 3000
        })

    }

    localStorage.setItem("view", "main");
    function changeRowBgColor() {

            changeBgColor();
       }
    function changeBgColor() {
             debugger;
             var grid = $("#UsageGrid").data("kendoGrid");
             var dataSource = grid.dataSource;
             grid.tbody.find("tr").each(function () {
                 var dataItem = grid.dataItem(this);
                 var row = $(this);

                 // Check the value of the property you want to use for conditional formatting
                 var propertyValue = dataItem.RFID; // Change to your property name

                 // Define the condition and set the row's background color
                 if (!(propertyValue.startsWith('E00') || propertyValue.startsWith('MA'))) {
                     row.css("background-color", "#2fba41"); // Change to your desired background color
                     row.css("color", "white");
                     row.css("font-weight", 600);
                 }
                 else {
                     row.css("background-color", "#1F4788");
                     row.css("color", "white");
                     row.css("font-weight", 600);
                 }
             });
         }


</script>

