﻿@using iRISupplyWebDeskManageReports.Models
    @model NewReport

@{
    ViewData["Title"] = "Add New Report";
    ViewBag.Title = "Add New Report";
    ViewBag.pTitle = "Add New Report";
    
    
    Layout = "~/Views/_Shared/_Layout.cshtml";
}

<style>
    .user-img-span {
        background: #65c9f0;
        display: block;
        width: 145px;
        margin: 0 auto;
        text-align: center;
        height: 140px;
        line-height: 138px;
        border-radius: 50%;
        font-size: 40px;
        letter-spacing: 1px;
    }

    .manage-profiles {
        text-align: center;
    }

    .btn-manage-profile {
        height: 40px;
        padding: 0 15px;
        font-size: 15px;
        margin: 0 15px;
    }

    .spn-manage-profile {
        margin: 0 10px;
        letter-spacing: 0.4px;
    }
    .required {
        color: red;
    }
</style>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

<form style="margin: 10px 0px 10px 0px;" class="needs-validation custom-validation" novalidate method="post" asp-controller="ManageReports" asp-action="AddNewReportToDB">
    @Html.AntiForgeryToken()
    <div class="form-group">
        <label for="ReportName"><span class="required">Report Name *</span></label>
        <input class="form-control" id="ReportName" asp-for="@Model.ReportName" required>
    </div>
    <div class="form-group">
        <label for="Description"><span class="required">Description *</span></label>
        <input class="form-control" id="Description" asp-for="@Model.Description" required>
    </div>
    <div class="form-group">
        <label for="ReportPath"><span class="required">Report Path *</span></label>
        <input class="form-control" id="ReportPath" asp-for="@Model.ReportPath" required>
    </div>
    <div class="form-group">
        <label for="ReportPath">Report Status</label>
        <div class="custom-control custom-switch">
          <input type="checkbox" class="custom-control-input" id="userStatus" asp-for="@Model.IsActive" checked>
          <label class="custom-control-label " for="userStatus" ></label>
          <strong id="statusid">Active</strong>
         </div>
    </div>
    <div class="row input-field-row flex-row justify-content-end text-right">
        <div class="col-md-6">
            <button type="button" class="btn btn-secondary waves-effect" onclick="window.history.back();">
                <span>Cancel</span>
            </button>
            <button type="submit" class="btn btn-primary" >Submit</button>
        </div>
    </div>
</form>

<script>


    $("#userStatus").click(function(){
       if ($('#userStatus').is(":checked")==true)
{
     var val=$('#userStatus').is(":checked");
    $("#userStatus").val(val);
    $("#statusid").text("Active");
    
   
}if($('#userStatus').is(":checked")==false){
     var val=$('#userStatus').is(":checked");
     $("#userStatus").val(val);
      $("#statusid").text("Inactive");
      
}
    }) ;

</script>