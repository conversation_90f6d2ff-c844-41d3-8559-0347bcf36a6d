﻿@using Kendo.Mvc.UI
@using iRISupplyWebDeskManageReports.Models
@model UserLogTransactionDetailsViewModel
@{
    ViewData["Title"] = "Users with no transaction";
    ViewBag.Title = "Users with no transaction";
    ViewBag.pTitle = "Users with no transaction";
    Layout = "~/Views/_Shared/_Layout.cshtml";
}


@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

<link rel="stylesheet" href="https://kendo.cdn.telerik.com/2022.2.621/styles/kendo.bootstrap-main.min.css" id="Grid-Styling" />
<style>
    .fa-plus {
        line-height: 25px;
        margin-right: 6px;
    }

    #grid {
        margin: 10px 0px 10px 0px;
    }

    .k-spacer {
        display: none !important;
    }

    .k-grid-search {
        /*  margin-left: auto;*/
        margin-right: 0;
    }

    .k-menu-link input[type="checkbox"] {
        margin-right: 5px;
    }

    .k-command-cell .btn-danger {
        margin-left: 10px;
    }

    .swal2-container .swal2-title {
        font-size: 19px;
    }



    #grid .k-grid-toolbar {
        padding: .6em 1.3em .6em .4em;
    }

    .category-label {
        vertical-align: middle;
        padding-right: .5em;
    }

    #category {
        vertical-align: middle;
    }

    .refreshBtnContainer {
        display: inline-block;
    }

    .k-grid .toolbar {
        margin-left: auto;
        margin-right: 0;
    }
</style>
<script type="text/javascript">
    var grid = $("#AppLogGrid");
    grid.find(".k-grid-toolbar").on("click", ".k-pager-refresh", function (e) {
        
        e.preventDefault();
        grid.data("kendoGrid").dataSource.read();
    });
</script>
@(Html.Kendo().Grid<iRISupplyWebDeskManageReports.Models.UserLogTransactionDetailsViewModel>()
                         .Name("AppLogGrid")
                         .Columns(columns => {
                         columns.Bound(p => p.SessionID).Visible(false);
                         columns.Bound(p => p.AppEventLogID).Visible(false);
                         columns.Bound(p => p.Date).Title("Date").Visible(false);
                         //columns.Bound(p => p.DateTime).Title("Date Time").ClientTemplate("#= kendo.toString(kendo.parseDate(DateTime), 'MM/dd/yyyy HH:mm') #").Width(180);
                         columns.Bound(p => p.DateTime).Title("Date Time").Width(180);
                         columns.Bound(p => p.ModuleName).Title("Action").Width(180);
                         columns.Bound(p => p.LogMessage).Title("Remarks").Width(180);
                         columns.Bound(p => p.Department).Title("Department").Width(180);
                         columns.Bound(p => p.UserName).Title("Name").Width(180).Hidden(true);
    @* columns.Bound(p => p.RFID).Title("RFID");*@
    @*columns.Bound(p => p.LogMessage).Title("Event");*@
                         columns.Bound(p => p.ComputerName).Title("Computer Name").Width(180).Hidden(true);
                         columns.Bound(p=>p.Room).Title("Room").Width(180);
                         })
                         .ToolBar(toolbar => {
    @*toolbar.Search();
        toolbar.Excel();*@
                         toolbar.ClientTemplateId("GridToolbarTemplate");
                         })
                         
                          .Scrollable(scr => scr.Height(300))
                         .Filterable()
                         .Sortable()
                          .Pageable(p =>
                            {
                            p.PageSizes(new[] { 150, 200, 250 });
                            })
                        .Groupable()
                        .Filterable()
                        .Selectable()
                        //.Events(events=> events.DataBound("onDataBound"))
                        .ColumnMenu(true)
                        .Reorderable(reorder => reorder.Columns(true))
                        .Resizable(resize => resize.Columns(true))
    @*.ClientDetailTemplateId("template")*@
                         .DataSource(dataSource => dataSource
                         .Ajax()
                         .PageSize(150)
                         .ServerOperation(false)
                         .Model(model => model.Id(p => p.Department))
                         .Read("GetNoTransactions", "ManageReports")
                         .Group(groups => groups.AddDescending(p => p.Date))
                         .Group(groups => groups.Add(p => p.UserName))
                         .Group(groups=>groups.AddDescending(p=>p.SessionID))
                         .Sort(sort => sort.Add(model => model.UserName))
                         //.Filter(f => f.Add(a => a.AppEventLogID).Contains("1116"))
                         )
                         )
<script id="GridToolbarTemplate" type="text/x-kendo-template">
    <div class="refreshBtnContainer">
    <a id="RefreshButton" onclick="RefreshGrid()" class="k-pager-refresh k-link k-button k-button-solid-base k-button-solid k-button-md k-rounded-md k-flat k-icon-button" title="Refresh"><span class="k-icon k-i-reload"></span></a>
    </div>
    <div>
   
    </div>
    <div class="refreshBtnContainer">
    <a role='button' class='k-grid-excel k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' href='\\#'><span class='k-icon k-i-file-excel'></span>Export to Excel</a>
     <a role='button' class='k-grid-pdf k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' href='\\#'><span class='k-icon k-i-file-pdf '></span>Export to PDF</a>
    </div>
    <div>
    <span class="k-searchbox k-input k-input-md k-rounded-md k-input-solid k-grid-search"><span class="k-input-icon k-icon k-i-search"></span><input autocomplete="off" placeholder="Search..." title="Search..." class="k-input-inner"></span>
    </div>


    <div class="toolbar">
    <label class="category-label" for="category">Select Room:</label>
     <select class="k-picker k-dropdownlist k-picker-solid k-picker-md k-rounded-md" style="width:200px;" id="RoomDropDown">
        <option class="selected disabled">Select Option</option>

    </select>
    </div>
</script>



<script>
    $(document).ready(function () {

        const DepartmentList = @Html.Raw(Json.Serialize(Model.DepartmentList));
        const RoomList = @Html.Raw(Json.Serialize(Model.RoomListViewModel));
        var $select = $('#RoomDropDown');

        for (var i = 0; i < DepartmentList.length; i++) {
            var group = DepartmentList[i];
            var $optgroup = $('<optgroup>').attr('label', group);

            // Iterate over the options in the current group
            for (var j = 0; j < RoomList.length; j++) {
                var Department = RoomList[j]["Department"];
                var option = RoomList[j]["Room"];
                if (Department === group) {
                    var $option = $('<option>').attr('value', option).text(option);
                    $optgroup.append($option);

                }

            }

            $select.append($optgroup);
        }



        // Filter user activity log



        $('#RoomDropDown').change(function () {
            var selectElement = document.getElementById("RoomDropDown");
            var selectedOption = this.options[this.selectedIndex];
            var value = selectedOption.value;
            grid = $("#AppLogGrid").data("kendoGrid");

            if (value) {
                grid.dataSource.filter({ field: "Room", operator: "eq", value: value });
            } else {
                grid.dataSource.filter({});
            }
        });

    });




    function RefreshGrid(){
               var grid = $("#AppLogGrid");
               grid.data("kendoGrid").dataSource.read();
      }
          



</script>






       


