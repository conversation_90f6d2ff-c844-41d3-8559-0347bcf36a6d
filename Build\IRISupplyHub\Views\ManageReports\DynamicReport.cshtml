﻿@model System.Data.DataTable
@using Kendo.Mvc.UI;
@using System.Data
@{
    // if (ViewBag.reportTitle == "uspWebItemsRemovedToday")
    // {
    //     ViewBag.reportTitle = "Items Removed Today";
    // }
    // else if (ViewBag.reportTitle == "uspWebItemsReturnedToday")
    // {
    //     ViewBag.reportTitle = "Items Returned Today";
    // }
    // else
    // {
    //     ViewBag.reportTitle = "Default";
    // }
    //ViewData["Title"] = ViewBag.reportTitle;
    //ViewBag.Title = ViewBag.reportTitle;
    ViewBag.pTitle = ViewBag.reportTitle;
    ViewBag.pageTitle = "Mobile Aspects";
    Layout = "~/Views/_Shared/_Layout.cshtml";
    // int pagesize = ViewBag.ItemsCount;
    //Layout = null;
}
<link rel="stylesheet" href="https://kendo.cdn.telerik.com/2022.2.621/styles/kendo.bootstrap-main.min.css" id="Grid-Styling" />
<style>
    .fa-plus {
        line-height: 25px;
        margin-right: 6px;
    }

 

    #grid {
        margin: 10px 0px 10px 0px;
    }

 

    .k-spacer {
        display: none !important;
    }

 

    .k-grid-search {
        /*  margin-left: auto;*/
        margin-right: 0;
    }

 

    .k-menu-link input[type="checkbox"] {
        margin-right: 5px;
    }

 

    .k-command-cell .btn-danger {
        margin-left: 10px;
    }

 

    .swal2-container .swal2-title {
        font-size: 19px;
    }

 

 

    #grid .k-grid-toolbar {
        padding: .6em 1.3em .6em .4em;
    }

 

    .category-label {
        vertical-align: middle;
        padding-right: .5em;
    }

 

    #category {
        vertical-align: middle;
    }

 

    .refreshBtnContainer {
        display: inline-block;
    }

 

    .k-grid .toolbar {
        margin-left: auto;
        margin-right: 0;
    }

    .dynamicreport{
        margin-left:10px !important;
        margin-right:10px !important    }
</style>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")
<script type="text/javascript">
    var grid = $("#Grid");
    grid.find(".k-grid-toolbar").on("click", ".k-pager-refresh", function (e) {

 

        e.preventDefault();
        grid.data("kendoGrid").dataSource.read();
    });
</script>
<div class="dynamicreport">
@(Html.Kendo().Grid<dynamic>()
    .Name("Grid")
    .Columns(columns =>
    {
        foreach (System.Data.DataColumn column in Model.Columns)
        {
            var c = columns.Bound(column.ColumnName)
            .Width(150);
        }
    })
    .Pageable()
    .Sortable()
    .Scrollable(s=>s.Enabled(true))
    @*.Editable(ed => ed.Mode(GridEditMode.PopUp))*@
    .Filterable()
    .ColumnMenu(true)
    .Resizable(resize => resize.Columns(true))
    .Groupable()
    .DataSource(dataSource => dataSource
        .Ajax()
        .Model(model =>
        {
            foreach (System.Data.DataColumn column in Model.Columns)
            {
                var field = model.Field(column.ColumnName, column.DataType);
            }
        })
        .ServerOperation(false)
        .Read(read => read.Action("GetDynamicReport", "ManageReports"))
    )
    .ToolBar(toolbar => {
    @*toolbar.Search();
        toolbar.Excel();*@
      toolbar.ClientTemplateId("GridToolbarTemplate");

      })
)
<script id="GridToolbarTemplate" type="text/x-kendo-template">
<div class="refreshBtnContainer">
<a id="RefreshButton" onclick="RefreshGrid()" class="k-pager-refresh k-link k-button k-button-solid-base k-button-solid k-button-md k-rounded-md k-flat k-icon-button" title="Refresh"><span class="k-icon k-i-reload"></span></a>
</div>
<div>

</div>
<div class="refreshBtnContainer">
<a role='button' class='k-grid-excel k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' href='\\#'><span class='k-icon k-i-file-excel'></span>Export to Excel</a>
</div>
<div>
<span class="k-searchbox k-input k-input-md k-rounded-md k-input-solid k-grid-search"><span class="k-input-icon k-icon k-i-search"></span><input autocomplete="off" placeholder="Search..." title="Search..." class="k-input-inner"></span>
</div>
<div class="">
<a role='button' onclick="saveGridLayout()" class='k-grid-save k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' href='\\#'><span class='far fa-save'></span>Save</a>
</div>

 

    <div class="toolbar">
<label class="category-label" for="category">Layout:</label>
    @(Html.Kendo().DropDownList()
            .Name("applications")
            .OptionLabel("DefaultLayout")
            .DataTextField("LayoutName")
            .DataValueField("LayoutName")
            .AutoBind(false)
            .Events(e => e.Change("applicationChange"))
            .HtmlAttributes(new { style = "width: 150px;" })
            .DataSource(ds =>
            {
                ds.Read("GetUserLayouts", "ManageReports");
            })
            .HtmlAttributes(new { style = "width: 200px;" })

            .ToClientTemplate()
        )
</div>
        </div>
</script>
<script>
    function RefreshGrid(){
               var grid = $("#Grid");
               grid.data("kendoGrid").dataSource.read();
      }
           function applicationChange () {

              var value = this.value(),
                  grid = $("#Grid").data("kendoGrid");

 

 

              //if (value) {
              //    grid.dataSource.filter({ field: "Department", operator: "eq", value: value });
              //} else {
              //    grid.dataSource.filter({});
              //}
          }
          function saveGridLayout(){
              let InitialName = 'Default Name';

 

Swal.fire({
  title: 'Custom Report Name',
  input: 'text',
  inputPlaceholder: 'Enter report name',
  showCancelButton: true,
  showDenyButton: true,
  returnInputValueOnDeny: true,
  confirmButtonText: 'save',
  denyButtonText: 'save as default layout',
  denyButtonColor: "#FF4633",
  inputValue: InitialName,
  didOpen: function (e) {
    Swal.getInput().addEventListener('keyup', function (e) {
      if (e.target.value === '') {
        $(".swal2-confirm").attr('disabled', true);
        $(".swal2-deny").attr('disabled', true);
      } else {
        $(".swal2-confirm").attr("disabled", false);
        $(".swal2-deny").attr("disabled", false);
      }
    })
  },
  inputValidator: (value) => {
    if (!value) {
      return 'the value is required'
    }
  }
})
  .then((result, reportName = result.value) => {
    if (result.isConfirmed) {
      saveGridState(result.value, false);
    }
    if (result.isDenied) {
      saveGridState(result.value, true);
    }
  });

 

          }

 

 

</script>
