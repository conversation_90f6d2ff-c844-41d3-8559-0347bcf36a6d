﻿@using iRISupplyWebDeskManageReports.Models
@model List<ReportsViewModel>
@using Kendo.Mvc.UI
@using Microsoft.AspNetCore.Http
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor

@{
    var session = HttpContextAccessor.HttpContext.Session;
    var add = session.GetString("AddReport");
    var update = session.GetString("UpdateReport");
    var appurl = session.GetString("BaseUrl");
    
    session.Remove("AddReport");
    session.Remove("UpdateReport");
}
@{
    ViewData["Title"] = "Reports";
    ViewBag.Title = "Reports";
    ViewBag.pTitle = "Reports";
    ViewBag.pageTitle = "Mobile Aspects";
    Layout = "~/Views/_Shared/_Layout.cshtml";
   
}
<style>
    .fa-plus {
        line-height: 25px;
        margin-right: 6px;
    }

    #grid {
        margin: 10px 0px 10px 0px;
    }
     .k-spacer{
            display:none !important;
        }
    .k-grid-search {
       /* margin-left: auto;*/
        margin-right: 0;
    }

    .k-grid-content {
        height: 360px !important;
    }

    .k-menu-link input[type="checkbox"] {
        margin-right: 5px;
    }

    .k-command-cell .btn-danger {
        margin-left: 10px;
    }



    .fas.fa-edit {
        color: white;
    }

    .k-grid-header .k-header {
       
        color: black;
    }
    .colored-text {
        color: #556ee6;
        text-decoration: underline;
    }
</style>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"> 


@(Html.Kendo().Grid(Model)
    .Name("grid")
    .Columns(columns =>
    {
        columns.Select().Width(25).HtmlAttributes(new { @class = "checkbox-align" }).HeaderHtmlAttributes(new { @class = "checkbox-align" });
         columns.Bound(u => u.ReportID).Title("ReportID").Width(200).Hidden(true);
        columns.Bound(u => u.ReportName).Title("Report Name").Width(180).ClientTemplate("<a href='#=ReportPath#'>#=ReportName#</a>").HtmlAttributes(new { @class = "colored-text" });
        columns.Bound(u => u.Description).Title("Description").Width(250);
        columns.Bound(u => u.Status).Title("Status").Width(70);
        columns.Bound("").Title("Action").Width(150).ClientTemplate($@"<a role='button' href='{appurl}/ManageReports/Edit?u=#=ReportID#' title='Click to edit' class='btn btn-primary mr-1' style='cursor:pointer;''><i class='fas fa-edit text-white'></i></a>");
      
    })
  
    .Pageable(p =>
    {
        p.PageSizes(new[] { 5, 10, 20 });
    })
    .Selectable(selectable => selectable
            .Mode(GridSelectionMode.Multiple)
            .Type(GridSelectionType.Row))
    .Sortable()
    .Scrollable(scr=>scr.Height(300))
    .ToolBar( t =>
    { t.Search();
        if (true)
        {
           
            t.Custom().Text("Add New Report").IconClass("fa fa-plus").HtmlAttributes(new { onclick = "AddNewReport();" });
            t.Excel();
            t.Pdf();
        }
        else{
            t.Excel();
            t.Pdf();
        }
    })
    .Filterable()
    .ColumnMenu(true)
    .Resizable(resize => resize.Columns(true))
    .DataSource(dataSource => dataSource
        .Ajax()
        .GroupPaging(true)
        .PageSize(10)
        .ServerOperation(false)
        .Update(u => u.Action("UpdateReport", "ManageReports")).Model(m => { m.Id(p => p.ReportID); })
     )
)

<script>


function ToastAlert(msg){
               
                           Toast.fire({
                            icon: 'success',
                            title: msg
                            })
        }
     $(document).ready(function () {
                
                const update='@update';
                const add='@add';
                
               if(update!=null && update!="")ToastAlert(update);
               if(add!=null && add!="")ToastAlert(add);
               
     });

    function AddNewReport() {
        window.location.href = '@Url.Action("AddNewReport", "ManageReports")';
    }
</script>

