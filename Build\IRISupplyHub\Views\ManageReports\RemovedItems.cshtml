﻿@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@using iRISupplyWebDeskManageReports.Models
@model List<InventorySummaryReporViewModel>
@using Kendo.Mvc.UI
@{
    ViewBag.Title = "Reports";
    ViewData["Title"] = "Removed Items Report";
    ViewBag.Title = "Removed Items Report";
    ViewBag.pTitle = "Removed Items Report";
    
  
     Layout = "~/Views/_Shared/_Layout.cshtml";
}
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")
<link rel="stylesheet" href="https://kendo.cdn.telerik.com/2022.2.621/styles/kendo.bootstrap-main.min.css" id="Grid-Styling" />
<style>
    .fa-plus {
        line-height: 25px;
        margin-right: 6px;
    }

    #grid {
        margin: 10px 0px 10px 0px;
    }
     .k-spacer{
            display:none !important;
        }
    .k-grid-search {
       /* margin-left: auto;*/
        margin-right: 0;
    }

    .k-grid-content {
        height: 360px !important;
    }

    .k-menu-link input[type="checkbox"] {
        margin-right: 5px;
    }

    .k-command-cell .btn-danger {
        margin-left: 10px;
    }



    .fas.fa-edit {
        color: white;
    }

    .k-grid-header .k-header {
        /*background-color: #556ee6;*/
        color: black;
    }
    .colored-text {
        color: #556ee6;
        text-decoration: underline;
    }
</style>


<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"> 


 



 @(Html.Kendo().Grid(Model)
    .Name("grid")
    .Columns(columns =>
    {columns.Select().Width(40).HtmlAttributes(new { @class = "checkbox-align" }).HeaderHtmlAttributes(new { @class = "checkbox-align"});
        columns.Bound(u => u.RFID).Title("RFID").Width(120);
        columns.Bound(u => u.CatNo).Title("Cat #").Width(100);
        columns.Bound(u => u.Description).Title("Description").Width(100);
        columns.Bound(u => u.PatientName).Title("Patient Name").Width(100);
        columns.Bound(u => u.RemovedBy).Title("Removed By").Width(100);
        columns.Bound(u => u.DateRemoved).Title("Removed Date").Width(100);
        columns.Bound(u => u.ItemStatus).Title("Status").Width(100);
        columns.Bound(u => u.Location).Title("Location").Width(80);
       
        
    })
    
    .Pageable(p =>
    {
        p.PageSizes(new[] { 5, 10, 20 });
    })
    .Selectable(selectable => selectable
            .Mode(GridSelectionMode.Multiple)
            .Type(GridSelectionType.Row))
    .Sortable()
    .Scrollable(scr=>scr.Height(300))
    .ToolBar( t =>
    { t.Search();
       
     t.Excel();
        t.Pdf();
        
    })
    .Filterable()
    .Groupable(true)
    .ColumnMenu(true)
    .Resizable(resize => resize.Columns(true))
    .DataSource(dataSource => dataSource
        .Ajax()
        .GroupPaging(true)
        .Group(g=>g.AddDescending(x=>x.RemovedDate))
        .PageSize(10)
        .ServerOperation(false)
       
     )
)

 



