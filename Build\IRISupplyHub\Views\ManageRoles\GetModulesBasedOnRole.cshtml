﻿@using Kendo.Mvc.UI.Fluent  
@using iRISupplyWebDeskRBAC.Models

@(Html.Kendo().TreeView()
               .Name("TreeViewTemplateBiding")
               .TemplateId("TreeViewTemplate")
               .Checkboxes(c => c.<PERSON>n(true)
              .Template("<input type='checkbox'  name='#:item.text#' #if(item.checked){# checked #}#/>"))
              .Events(events=>events.Check("OnCheck"))
               .BindTo((IEnumerable<NodeViewModel>)ViewBag.Tree, (NavigationBindingFactory<TreeViewItem> mappings) =>
               {
                   mappings.For<NodeViewModel>(binding => binding.ItemDataBound((item, node) =>
                   {
                       item.Id = node.Id.ToString();
                       item.Text = node.Title.ToUpper();
                       item.Expanded = node.Expanded;
                       item.Checked=node.isChecked;
                   })
               .Children(node => node.Children));
               })
            )


