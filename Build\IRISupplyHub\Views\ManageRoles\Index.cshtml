﻿@using Kendo.Mvc.UI.Fluent
@using Kendo.Mvc.UI;
@using iRISupplyWebDeskRBAC.Models
@{
    ViewData["Title"] = "Manage Roles";
    ViewBag.Title = "Manage Roles";
    ViewBag.pTitle = "Manage Roles";
    var jsonItems = Newtonsoft.Json.JsonConvert.SerializeObject(Model);
    Layout = "~/Views/_Shared/_Layout.cshtml";
}
<style>
    .required {
        color: red;
    }

    .user-img-span {
        background: #65c9f0;
        display: block;
        width: 145px;
        margin: 0 auto;
        text-align: center;
        height: 140px;
        line-height: 138px;
        border-radius: 50%;
        font-size: 40px;
        letter-spacing: 1px;
    }

    .manage-profiles {
        text-align: center;
    }

    .btn-manage-profile {
        height: 40px;
        padding: 0 15px;
        font-size: 15px;
        margin: 0 15px;
    }

    .spn-manage-profile {
        margin: 0 10px;
        letter-spacing: 0.4px;
    }

    .k-i-collapse::before {
        content: "\25AC";
    }

    .k-i-expand::before {
        content: "\271A";
    }

    .k-icon {
        margin-right: 5px;
    }

    .k-group {
        display: inline-block;
    }
        /*
            .k-group li {
                float: right;
            }*/

        .k-group .k-item {
            margin-right: 25px !important;
        }
</style>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

<div class="container-fluid">
    <div class="row">
        <form method="post" id="user" asp-action="UpdateUserPermission" asp-controller="ManageRoles" class="col-md-12">
           
            <div class="mt-4">
                 <label for="shipTo">Select Role</label><br>

                                    @(Html.Kendo().DropDownList()
                                    .Name("shipTo")
                                    .HtmlAttributes(new { style = "width:30%" })
                                    .OptionLabel("Select Role...")
                                    .DataTextField("Group")
                                    .DataValueField("GroupID")
                                    .Events(events=>events.Select("SelectRole"))
                                    .Height(290)
                                    .DataSource(source =>
                                    {
                                    source.Custom()
                                    .ServerFiltering(true)
                                    .ServerPaging(true)
                                    .PageSize(40)
                                    .Type("aspnetmvc-ajax") //Set this type if you want to use DataSourceRequest and ToDataSourceResult instances
                                    .Transport(transport =>
                                    {
                                    transport.Read("GetUserGroups", "ManageRoles");
                                    });

                                    })

                                    )
            </div>
            <div class="RolesContainer">
                <div class="card">
                    <div class="card-body">



                        @* <h4 class="card-title mb-4">Web App</h4>*@
                        <div class="treeview-flex">
                            <div class="demo-section k-content" id="basicpill-treeview-input">
                                <div id=UserRolesContainer>
                                    @(Html.Kendo().TreeView()
                                        .Name("TreeViewTemplateBiding")
                                        .TemplateId("TreeViewTemplate")

                                        @* .Checkboxes(c => c.CheckChildren(true)
                                    .Template("<input type='checkbox'  name='#:item.text#' #if(item.checked){# checked #}#/>"))*@
                                        @* .Events(events=>events.Check("OnCheck"))*@
                                        .BindTo((IEnumerable<NodeViewModel>)ViewBag.Tree, (NavigationBindingFactory<TreeViewItem> mappings) =>
                                        {
                                            mappings.For<NodeViewModel>(binding => binding.ItemDataBound((item, node) =>
                                            {
                                                item.Id = node.Id.ToString();
                                                item.Text = node.Title.ToUpper();
                                                item.Expanded = node.Expanded;
                                            })

                                            .Children(node => node.Children));
                                        })
                                        )
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row input-field-row flex-row justify-content-end text-right">
                <div class="col-6">
                    <button type="button" class="btn btn-secondary waves-effect" onclick="window.location='@Url.Action("index","ManageRoles")'">
                        <span>Cancel</span>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <span>Submit</span>
                    </button>
                </div>
            </div>
        </form>

    </div>
</div>


<script>
    $(document).ready(function () {
        if ('@ViewBag.success' == "true") {
            debugger;
            ToastAlert()

        }

        function ToastAlert() {

            Toast.fire({
                icon: 'success',
                title: "Updated Successfully!"
            })
        }
    })




    var selectedRole='';

    function SelectRole(e){

        
         selectedRole=this.dataItem(e.item);
        console.log( selectedRole.GroupID);
        selectedRole=selectedRole.GroupID;
       
        $.ajax({
            type:'get',
            url:'@Url.Action("GetModulesBasedOnRole","ManageRoles")',
            data:{RoleID:selectedRole}
        }).done(function(response){
            
            $("#UserRolesContainer").html(response)
           
        })
    }

    function checkedNodeIds(nodes, checkedNodes,unCheckedNodes) {
            for (var i = 0; i < nodes.length; i++) {
                if (nodes[i].checked) {
                    checkedNodes.push(nodes[i].text);
                }
                if(!nodes[i].checked){
                    unCheckedNodes.push(nodes[i].text);
                }
                
                
                if (nodes[i].hasChildren) {
                    checkedNodeIds(nodes[i].children.view(), checkedNodes,unCheckedNodes);
                }
            }
        }
        function OnCheck(e) {
            
          
            var checkedNodes = [],
                treeView = $("#TreeViewTemplateBiding").data("kendoTreeView"),
                message,unCheckedNodes=[];

            checkedNodeIds(treeView.dataSource.view(), checkedNodes,unCheckedNodes);
            if (checkedNodes.length > 0) { var clu = checkedNodes.join("|");  }
             else { var clu = "None" }
            if (unCheckedNodes.length > 0) { var clu1 = unCheckedNodes.join("|");  }
            else { var clu1 = "None" }
            var model = {
                ModuleName: clu,
                UnCheckedModuleName:clu1,
                AllModules: "ALL",
                UserGroupID:selectedRole
            };
            var url = "@Url.Action("CheckedModels", "ManageRoles")";
            $.get(url, model, function (res) {
                console.log(res);
            });
             
        }

       

      
</script>

