﻿@using iRISupplyWebDeskManageUsers.Models
@model NewUser
@using Kendo.Mvc.UI
@using Kendo.Mvc.UI.Fluent
@{
    ViewBag.Title = "Add New User";
    ViewBag.pTitle = "Add New User";
    ViewBag.pageTitle = "Mobile Aspects";
    Layout = "~/Views/_Shared/_Layout.cshtml";
  
}

<style>
    .required {
        color: red;
    }

    .user-img-span {
        background: #65c9f0;
        display: block;
        width: 145px;
        margin: 0 auto;
        text-align: center;
        height: 140px;
        line-height: 138px;
        border-radius: 50%;
        font-size: 40px;
        letter-spacing: 1px;
    }

    .manage-profiles {
        text-align: center;
    }

    .btn-manage-profile {
        height: 40px;
        padding: 0 15px;
        font-size: 15px;
        margin: 0 15px;
    }

    .spn-manage-profile {
        margin: 0 10px;
        letter-spacing: 0.4px;
    }

    .k-i-collapse::before {
        content: "\25AC";
    }

    .k-i-expand::before {
        content: "\271A";
    }

    .k-icon {
        margin-right: 5px;
    }

    .k-group {
        display: inline-block;
    }
        /*
        .k-group li {
            float: right;
        }*/

        .k-group .k-item {
            margin-right: 25px !important;
        }

    @@media screen and (max-width: 680px) {
        .treeview-flex {
            flex: auto !important;
            width: 100%;
        }
    }

    #demo-section-title h3 {
        margin-bottom: 2em;
        text-align: center;
    }

    .treeview-flex h4 {
        color: #656565;
        margin-bottom: 1em;
        text-align: center;
    }

    #demo-section-title {
        width: 100%;
        flex: auto;
    }

    .treeview-flex {
        flex: 1;
        -ms-flex: 1 0 auto;
    }

    .k-treeview {
        max-width: 240px;
        margin: 0 auto;
    }
    .alert {
        display: none;
    }

   
</style>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <form class="needs-validation custom-validation" novalidate asp-action="AddNewUserRecordToDB" asp-controller="ManageUsers">
                    @Html.AntiForgeryToken()
                    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="pills-home-tab" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">Primary</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="pills-profile-tab" data-toggle="pill" href="#pills-profile" role="tab" aria-controls="pills-profile" aria-selected="false">Account</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="pills-office-tab" data-toggle="pill" href="#pills-office" role="tab" aria-controls="pills-office" aria-selected="false">Office</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="pills-address-tab" data-toggle="pill" href="#pills-address" role="tab" aria-controls="pills-address" aria-selected="false">Address</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="pills-contact-tab" data-toggle="pill" href="#pills-contact" role="tab" aria-controls="pills-contact" aria-selected="false">Contact</a>
                        </li>
                       
                    </ul>
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label id="lblUserGroups" for="ddnUserGroups"> <span class="required">User Groups *</span></label>
                                        <select id="ddlUserGroups" class="form-control" asp-for="@Model.UserGroupID" required>
                                            <option  selected disabled>Selct User Group</option>
                                            @foreach (var group in Model.UserGroups)
                                            {
                                                <option value=@group.GroupID>@group.Group</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label for="userstatus">Status</label>
                                        <div class="custom-control custom-switch">
                                         <input type="checkbox" class="custom-control-input" id="userStatus" asp-for="@Model.IsActive" checked>
                                         <label class="custom-control-label " for="userStatus" ></label>
                                         <strong id="statusid">Active</strong>
                                         </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label for="basicpill-Suffix-input">Suffix</label>
                                        <input type="text" class="form-control" id="basicpill-Suffix-input" asp-for="@Model.Suffix">
                                    </div>
                                </div>
                                @*  <div class="col-sm-4">
                                        <div class="form-group">
                                            <label for="basicpill-department-input"><span class="required">Department *</span></label>
                                             @(Html.Kendo().DropDownList()
                                          .Name("room")
                                           .OptionLabel("Select Room")
                                          .DataTextField("Room")
                                          .DataValueField("Room")
                                          
                                          .Height(400)
                                          .HtmlAttributes(new { style = "width:100%", @class = "form-control", @required = "required" })
                                          .DataSource(source => source
                                              .Custom()
                                              .Group(g => g.Add("Department", typeof(string)))
                                              .Transport(transport => transport
                                                .Read(read =>
                                                {
                                                    read.Action("Grouping_RoomList", "ManageDevices");
                                                })))
                                                .Events(e =>
                                                {
                                                    e.Select("onSelect");
                                                })
                                    )
                                        </div>
                                        <input type="hidden" asp-for="@Model.Department" id="Department"/>
                                    </div> *@
                            </div>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label for="FirstName"><span class="required">First Name *</span></label>
                                        <input type="text" class="form-control" id="FirstName" name="FirstName" asp-for="@Model.FirstName" required>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label for="basicpill-middlename-input">Middle Name</label>
                                        <input type="text" class="form-control" id="basicpill-middlename-input" asp-for="@Model.MiddleName">
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label for="basicpill-lastname-input"><span class="required">Last Name *</span></label>
                                        <input type="text" class="form-control" id="basicpill-lastname-input" asp-for="@Model.LastName" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label for="basicpill-username-input"><span class="required">User Name *</span></label>
                                        <input type="text" class="form-control" id="basicpill-usename-input" asp-for="@Model.Username" required>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <div>
                                            <label for="basicpill-password-input"><span class="">Password </span></label>
                                            <button type="button" class="mdi mdi-information-outline" style="border: none;background: none;" data-toggle="popover" data-placement="top" title="Password rule" data-content="Password must contain minimum 8 characters,atleast one number,one special character,one uppercase and lowercase letters">
                                            </button>
                                            <input type="password" id="password" class="form-control"
                                                   name="Password"
                                                   pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@@$!%*?&])[A-Za-z\d@@$!%*?&]{8,16}$"
                                                   placeholder="Type Password"
                                                   asp-for="@Model.Pwd" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">

                                        <label for="basicpill-confirmpassword-input"><span class="">Confirm Password </span></label>
                                        <input type="password" class="form-control" id="confirm_password"
                                               pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@@$!%*?&])[A-Za-z\d@@$!%*?&]{8,16}$"
                                                name="ConfirmPassword"
                                               placeholder="Re-Type Password" asp-for="@Model.ConfirmPassword" />

                                    </div>
                                    <span id='message'></span>

                                </div>

                            </div>
                             <div class="row"><div class="col-sm-4" id="statusField"></div></div>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label for="emailid"><span class="">Email</span></label>
                                        <input type="email" class="form-control" id="emailid" name="emailid"
                                               asp-for="@Model.EmailID" >
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                            <div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-title-input">Title</label>
                                            <input type="text" class="form-control" id="basicpill-title-input" asp-for="@Model.Title">
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-EmployeeID-input">Employee ID</label>
                                            <input type="text" class="form-control" id="basicpill-EmployeeID-input" asp-for="@Model.EmployeeID">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-rfid-input">Badge ID</label>
                                            <input type="text" class="form-control" id="basicpill-rfid-input" asp-for="@Model.RFID">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                   

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-designation-input">Designation</label>
                                            <input type="text" class="form-control" id="basicpill-designation-input" asp-for="@Model.Designation">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="tab-pane fade" id="pills-office" role="tabpanel" aria-labelledby="pills-office-tab">
                            <div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-Company-input">Company</label>
                                            <input type="text" class="form-control" id="basicpill-Company-input" asp-for="@Model.Company">
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-Office-input">Office</label>
                                            <input type="text" class="form-control" id="basicpill-Office-input" asp-for="@Model.Office">
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-OfficeStreetAddress-input">Office Street Address</label>
                                            <input type="text" class="form-control" id="basicpill-OfficeStreetAddress-input" asp-for="@Model.OfficeStreetAddress">
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-OfficeCity-input">Office City</label>
                                            <input type="text" class="form-control" id="basicpill-OfficeCity-input" asp-for="@Model.OfficeCity">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-OfficeState-input">Office State</label>
                                            <select id="ddlOfficeState" class="custom-select" asp-for="@Model.OfficeState">
                                              <option  selected disabled>Select State</option>
                                            @foreach (var state in Model.UserOfficeStates)
                                            {
                                                <option value=@state.StateName>@state.StateName</option>
                                            }
                                            </select>
                                        </div>

                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-OfficeCountry-input">Office Country</label>
                                            <select id="ddlOfficeCountry" class="custom-select" asp-for="@Model.OfficeCountry">
                                                <option selected disabled>Select Country</option>  
                                            @foreach (var country in Model.UserCountrys)
                                            {
                                                <option value=@country.CountryName>@country.CountryName</option>
                                            }
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-OfficeZip-input">Office Zip Code</label>
                                            <input type="text" class="form-control" id="basicpill-OfficeZip-input" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                   asp-for="@Model.OfficeZip">
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-address" role="tabpanel" aria-labelledby="pills-address-tab">
                            <div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-department-input">Home Street Address</label>
                                            <input type="text" class="form-control" id="basicpill-HomeStreetAddress-input" asp-for="@Model.HomeStreetAddress">
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-HomeCity-input">Home City</label>
                                            <input type="text" class="form-control" id="basicpill-HomeCity-input" asp-for="@Model.HomeCity">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-HomeState-input">Home State</label>
                                            <select id="ddlHomeState" class="custom-select" asp-for="@Model.OfficeState">
                                               <option selected disabled>Select State</option>
                                            @foreach (var HomeState in Model.UserOfficeStates)
                                            {
                                                <option value=@HomeState.StateName>@HomeState.StateName</option>
                                            }
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-HomeZip-input">Home Zip Code</label>
                                            <input type="text" class="form-control" id="basicpill-HomeZip-input" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                   asp-for="@Model.HomeZip">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-expiration-input">Home Country</label>
                                            <select id="ddlHomeCountry" class="custom-select" asp-for="@Model.HomeCountry">
                                                <option selected disabled>Select Country</option>
                                            @foreach (var HomeCountry in Model.UserCountrys)
                                            {
                                                <option value=@HomeCountry.CountryName>@HomeCountry.CountryName</option>
                                            }
                                            </select>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-contact" role="tabpanel" aria-labelledby="pills-contact-tab">
                            <div>
                                @*<form>*@
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-HomePhoneNumber-input">Home Phone #</label>
                                            <input type="text" class="form-control" id="basicpill-HomePhoneNumber-input" data-parsley-type="number"
                                                   oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                   asp-for="@Model.HomePhoneNumber">
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-OfficePhoneNumber-input">Office Phone #</label>
                                            <input type="text" class="form-control" id="basicpill-OfficePhoneNumber-input" data-parsley-type="number"
                                                   oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                   asp-for="@Model.OfficePhoneNumber">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-MobilePhoneNumber-input">Mobile Phone #</label>
                                            <input type="text" class="form-control" id="basicpill-MobilePhoneNumber-input" data-parsley-type="number"
                                                   oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                   asp-for="@Model.MobilePhoneNumber">
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-FaxNumber-input">Fax #</label>
                                            <input type="text" class="form-control" id="basicpill-FaxNumber-input" data-parsley-type="number"
                                                   oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                   asp-for="@Model.FaxNumber">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label for="basicpill-PagerNumber-input">Pager #</label>
                                            <input type="text" class="form-control" id="basicpill-PagerNumber-input" data-parsley-type="number"
                                                   oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                   asp-for="@Model.PagerNumber">
                                        </div>
                                    </div>

                                </div>

                            </div>

                        </div>
                                            </div>
                    <div class="row input-field-row flex-row justify-content-end text-right">
                        <div>
                            <button type="reset" class="btn btn-secondary waves-effect" onclick="window.history.back();">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary waves-effect waves-light mr-1" id="submitUserBtn">
                                Submit
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<script>
    // onchange location dropdown value set in department 
         function onSelect(e) {
           $("#Department").val(e.dataItem["Department"]);
    }
    
    //Check Active or inactive username start
         $("#userStatus").click(function(){
       if ($('#userStatus').is(":checked")==true)
{
     var val=$('#userStatus').is(":checked");
    $("#userStatus").val(val);
    $("#statusid").text("Active");
    
   
}if($('#userStatus').is(":checked")==false){
     var val=$('#userStatus').is(":checked");
     $("#userStatus").val(val);
      $("#statusid").text("Inactive");
      
}
    }) ;

    //Check active or inactive username end



    //Duplicate username validation start
    
     $("#basicpill-usename-input").keydown(function(){
        $("#submitUserBtn").attr('disabled', true);
        $("#statusField").hide();
    })

     $("#basicpill-usename-input").blur(function(){
        if(this.value==""){
             $("#statusField").hide();
        }else{
       $("#statusField").show();
       $.ajax({
            type: 'get',
            url: '@Url.Action("UserNameValidation","ManageUsers")',
            data:{UserName:this.value,OldUserName:null}
        })
            .done(function (response) {
                //$("#ValidateStatus").show();
                if(response=="Username already Exists"){
                    $("#submitUserBtn").attr('disabled', true);
                  $("#statusField").text(response).css({'color':'red','margin-top':'-15px'});

                }else{

                     $("#submitUserBtn").attr('disabled', false);
                    $("#statusField").text("Available").css({'color':'green','margin-top':'-15px'});
                }
                
            })
            }
    })
    
    $('#password, #confirm_password').on('keyup', function () {
           const userNameStaus=$("#statusField").text();
        if ($('#password').val() == $('#confirm_password').val() && userNameStaus=="Available") {
            $('#message').html(' ');
            $("#submitUserBtn").attr('disabled', false);
        } else {
            if($('#password').val() == $('#confirm_password').val() && userNameStaus=="Username already Exists"){
                $('#message').html('');
            }else{
                 $('#message').html('Password and confirm Password dont match').css('color', 'red');
            }
           
            $("#submitUserBtn").attr('disabled', true);
        }
    });
    
     function OnCheck(e)
    {
        var parent = this.parent(e.node);
        console.log(parent);
        var parentText = this.text(parent);


        var child = this.text(e.node);
        console.log(child);

        var model = {
            cluster: parentText,
            cabinet: child
        };

        var url = "@Url.Action("SaveCabinetAccess", "ManageUsers")";
        $.get(url, model, function (res) {
            console.log("Here");
        });
    }
   
    function OnReportCheck(e)
    {
        var parent = this.parent(e.node);
        console.log(parent);
        var parentText = this.text(parent);


        var child = this.text(e.node);
        console.log(child);

        var model = {
            cluster: parentText,
            cabinet: child
        };

        var url = "@Url.Action("SaveCabinetAccess", "ManageUsers")";
        $.get(url, model, function (res) {
            console.log("Here");
        });
    }
    
  
</script>