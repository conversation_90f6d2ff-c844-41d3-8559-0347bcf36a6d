﻿@using iRISupplyWebDeskManageUsers.Models
@model ReportModule
@using Kendo.Mvc.UI.Fluent
@{
    ViewData["Title"] = "Add Permission";
    ViewBag.Title = "Add Permission";
    ViewBag.pTitle = "Add Permission";
    ViewBag.pageTitle = "Mobile Aspects";
    var jsonItems = Newtonsoft.Json.JsonConvert.SerializeObject(Model);
    Layout = "~/Views/_Shared/_Layout.cshtml";
}
<style>
    .required {
        color: red;
    }

    .user-img-span {
        background: #65c9f0;
        display: block;
        width: 145px;
        margin: 0 auto;
        text-align: center;
        height: 140px;
        line-height: 138px;
        border-radius: 50%;
        font-size: 40px;
        letter-spacing: 1px;
    }

    .manage-profiles {
        text-align: center;
    }

    .btn-manage-profile {
        height: 40px;
        padding: 0 15px;
        font-size: 15px;
        margin: 0 15px;
    }

    .spn-manage-profile {
        margin: 0 10px;
        letter-spacing: 0.4px;
    }

    .k-i-collapse::before {
        content: "\25AC";
    }

    .k-i-expand::before {
        content: "\271A";
    }

    .k-icon {
        margin-right: 5px;
    }

    .k-group {
        display: inline-block;
    }
        /*
        .k-group li {
            float: right;
        }*/

        .k-group .k-item {
            margin-right: 25px !important;
        }
</style>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="https://kendo.cdn.telerik.com/2022.2.621/styles/kendo.bootstrap-main.min.css" id="Grid-Styling" />
<script src="~/assets/libs/sweetalert/sweetalert.js"></script>


<form method="post" id="user" asp-action="UpdateUserPermission" asp-controller="ManageUsers">
    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
       @*  <li class="nav-item">
            <a class="nav-link active" id="pills-home-tab" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">Freezer</a>
        </li> *@
        <li class="nav-item">
            <a class="nav-link" id="pills-profile-tab" data-toggle="pill" href="#pills-profile" role="tab" aria-controls="pills-profile" aria-selected="false">Reports</a>
        </li>
       
    </ul>
    <div class="tab-content" id="pills-tabContent">
        @* <div class="tab-pane fade " id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            
                            <div class="treeview-flex">
                                <div class="demo-section k-content" id="basicpill-treeview-input">
                                    @(Html.Kendo().TreeView()
                                                .Name("TreeViewTemplateBiding")
                                                .TemplateId("TreeViewTemplate")
                                                .Checkboxes(c => c.CheckChildren(true)
                                                .Template("<input type='checkbox'  name='#:item.text#' #if(item.checked){# checked #}#/>"))
                                                .Events(events=>events.Check("OnCheck"))
                                                .BindTo((IEnumerable<NodeViewModel>)ViewBag.Tree, (NavigationBindingFactory<TreeViewItem> mappings) =>
                                                {
                                                    mappings.For<NodeViewModel>(binding => binding.ItemDataBound((item, node) =>
                                                    {
                                                        item.Id = node.Id.ToString();
                                                        item.Text = node.Title;
                                                        item.Expanded = node.Expanded;
                                                        item.Checked = node.isChecked;

                                                    })
                                                .Children(node => node.Children));
                                                })
                                            )
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> *@
        <div class="tab-pane fade show active" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">Report Permission</h4>
                            <div class="treeview-flex">
                                <div class="demo-section k-content" id="basicpill-treeviewreport-input">
                                    @(Html.Kendo().TreeView()
                                                .Name("TreeViewTemplateReportBiding")
                                                .TemplateId("TreeViewTemplateReport")
                                                .Checkboxes(c => c.CheckChildren(true)
                                                .Template("<input type='checkbox'  name='#:item.text#' #if(item.checked){# checked #}#/>"))
                                                .Events(events=>events.Check("OnReportCheck"))
                                                .BindTo((IEnumerable<ReportsViewModel>)ViewBag.Report, (NavigationBindingFactory<TreeViewItem> mappings) =>
                                                {
                                                    mappings.For<ReportsViewModel>(binding => binding.ItemDataBound((item, node) =>
                                                    {
                                                        item.Id = node.ReportID.ToString();
                                                        item.Text = node.ReportName;
                                                        item.Checked = node.isChecked;
                                                        item.Expanded = node.Expanded;
                                                    })
                                                .Children(node=>node.Children));

                                                })
                                            )
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
       
    </div>
    <input type="hidden" asp-for="@Model.UserID"/>
    <input type="hidden" asp-for="@Model.ReportsList" id="ReportAccess" />
   @*  <input type="hidden" asp-for="@Model.CabinetAccessList" id="CabinetAccess"/> *@
    <div class="row input-field-row flex-row justify-content-end text-right">
        <div class="col-6">
            <button type="button" class="btn btn-secondary waves-effect" onclick="window.location='@Url.Action("index","ManageUsers")'">
                <span>Cancel</span>
            </button>
            <button type="submit" class="btn btn-primary">
                <span>Submit</span>
            </button>
        </div>
    </div>
</form>

<script>
    $("#ReportAccess").val('@Model.ReportsList')
    // $("#CabinetAccess").val('@Model.CabinetAccessList')
    if ('@TempData["UserID"]' != '') {

        var UserID = '@TempData["UserID"]';
        localStorage.setItem("UserID", UserID);

    }



    // function checkedNodeIds(nodes, checkedNodes) {
    //     for (var i = 0; i < nodes.length; i++) {
    //         if (nodes[i].checked) {
    //             checkedNodes.push(nodes[i].text);
    //         }

    //         if (nodes[i].hasChildren) {
    //             checkedNodeIds(nodes[i].children.view(), checkedNodes);
    //         }
    //     }
    // }
    // function OnCheck(e) {
    //     debugger;
    //     var checkedNodes = [],
    //         treeView = $("#TreeViewTemplateBiding").data("kendoTreeView"),
    //         message;

    //     checkedNodeIds(treeView.dataSource.view(), checkedNodes);
    //     if (checkedNodes.length > 0) {
    //         var clu = checkedNodes.join("|"); console.log(clu)
    //         $("#CabinetAccess").val(clu)
    //     }
    //     else { $("#CabinetAccess").val("") }


    // }

    function checkedReportNodeIds(nodesR, checkedReportNodes) {
        for (var i = 0; i < nodesR.length; i++) {
            if (nodesR[i].checked) {
                checkedReportNodes.push(nodesR[i].id);
            }
            if (nodesR[i].hasChildren) {
                checkedReportNodeIds(nodesR[i].children.view(), checkedReportNodes);
            }
        }
    }
    function OnReportCheck(e) {
        debugger;
        var checkedReportNodes = [],
            treeView = $("#TreeViewTemplateReportBiding").data("kendoTreeView"),
            message;
        checkedReportNodeIds(treeView.dataSource.view(), checkedReportNodes);
        if (checkedReportNodes.length > 0) {
            var repName = checkedReportNodes.join("|");
            $("#ReportAccess").val(repName)
        }
        else { $("#ReportAccess").val("") }

    }

        $(function () {
            $("#chkLeadAlert").click(function () {
                if ($(this).is(":checked")) {
                    $("#divLeadAlert").show();
                } else {
                    $("#divLeadAlert").hide();
                }
            });

            $("#chkEmailAlert").click(function () {
                if ($(this).is(":checked")) {
                    $("#divEmailAlert").show();
                } else {
                    $("#divEmailAlert").hide();
                }
            });
        });

</script>


@if (TempData["message"] != null)
{
    
    <script>
        

                    Swal.fire({
  title: 'Success',
  text: "User Added Successfully",
  icon: 'success',
  showCancelButton: true,
  confirmButtonColor: '#3085d6',
  cancelButtonColor: '#d33',
  confirmButtonText: 'Add Permission'
}).then((result) => {
  if (result.isConfirmed) {
  
  }else{
       window.location = '@Url.Action("index","ManageUsers")'
  }
})





    </script>
}




