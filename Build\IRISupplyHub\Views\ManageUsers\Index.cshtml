﻿@model List<iRISupplyWebDeskManageUsers.Models.UserViewModel>
@using Kendo.Mvc.UI
@using Microsoft.AspNetCore.Http
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor

@{
    var session = HttpContextAccessor.HttpContext.Session;
    var appurl = session.GetString("BaseUrl");
    var update = session.GetString("Update");
    var added = session.GetString("Added");
    var permission = session.GetString("Permisson");
    if(added!=null && permission != null)permission = "";
    session.Remove("Added");
    session.Remove("Permisson");
    session.Remove("Update");
}

@{
    ViewData["Title"] = "Manage Users";
    ViewBag.Title = "Manage Users";
    ViewBag.pTitle = "Manage Users";
    ViewBag.pageTitle = "Manage Users";
    Layout = "~/Views/_Shared/_Layout.cshtml";
    //Layout = null;
}

<style>
    .fa-plus {
        line-height: 25px;
        margin-right: 6px;
    }

    #grid {
        margin: 10px 0px 10px 0px;
    }

    .k-spacer {
        display: none !important;
    }

    .k-grid-search {
        /* margin-left: auto;*/
        margin-right: 0;
    }

    .k-grid-content {
        height: 300px !important;
    }

    .k-menu-link input[type="checkbox"] {
        margin-right: 5px;
    }

    .k-command-cell .btn-danger {
        margin-left: 10px;
    }

    .tooltip {
        position: relative;
        display: inline-block;
        border-bottom: 1px dotted black; /* If you want dots under the hoverable text */
    }
</style>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

@(Html.Kendo().Grid(Model)
    .Name("UserGrid")
    .Groupable()
    .Columns(columns =>
    {
        columns.Select().Width(50).HtmlAttributes(new { @class = "checkbox-align" }).HeaderHtmlAttributes(new { @class = "checkbox-align" });
        columns.Bound(u => u.UserID).Title("UserID").Width(200).Hidden();//Hidden field
        columns.Bound(u => u.Name).Title("Name").Width(200);
        columns.Bound(u => u.UserName).Title("User Name").Width(150);
        columns.Bound(u => u.Domain).Title("User Details").Width(120);
        columns.Bound(u => u.UserGroup).Title("User Group").Width(150);
        columns.Bound(u => u.Designation).Title("Designation").Width(150);
        columns.Bound(u => u.Department).Title("Department").Width(150);
        columns.Bound(u => u.DateAdded).Title("Date Added").Width(150);
        columns.Bound("").Title("Action").Width(150).ClientTemplate($@"<a role='button' href='{appurl}/ManageUsers/Edit?u=#=UserID#' title='Click to reregister' class='btn btn-primary mr-1' style='cursor:pointer;''><i class='fas fa-edit text-white'></i></a><a role='button' href='{appurl}/ManageUsers/AddPermission?UserID=#=UserID#' title='Add To Permission' class='btn btn-primary mr-1' style='cursor:pointer;''><i class='fas fa-key text-white'></i></a>");
        columns.Bound(u => u.EmployeeID).Title("Employee ID").Width(150);
        @*columns.Command(command => {
            command.Custom("Edit").Template("<button role='button' onclick='EditUser(this)' class='btn btn-primary mr-1'><i class='fas fa-edit'></i></button>");
            command.Custom("EditPermission").Template("<button role='button' onclick ='EditPermission(this)' class='btn btn-primary'><i class='fas fa-key'></i></button>");
            // command.Custom("Delete").Template("<button role='button' onclick ='DeleteUser(this)' class='btn btn-danger'><i class='fas fa-trash-alt'></i></button>");
        }).Title("Edit").Width(150);*@
    })
    // .Pageable()
    // .Selectable(selectable => selectable
    //         .Mode(GridSelectionMode.Multiple)
    //         .Type(GridSelectionType.Row))

    .Pageable(p =>
    {
        p.PageSizes(new[] { 20, 40, 60, 80, 100 });
        p.Info(true);
    })
    .Sortable()
    .Scrollable(scr => scr.Height(300))
    .ToolBar(t =>
    {
        t.Search();
        t.Custom().Text("Add New User").IconClass("fa fa-plus").HtmlAttributes(new { onclick = "AddNewUser();" });
        t.Excel();
        t.Pdf();
    }
    )
    .Filterable()
    .ColumnMenu(true)
    .Resizable(resize => resize.Columns(true))
    .DataSource(dataSource => dataSource
        .Ajax()
        .GroupPaging(true)
        .PageSize(10)
        .ServerOperation(false)
     )
      .Events(events => events
            
            .DataBound("onDataBound")
            )


)

<script>
    var userToEdit;
      function ToastAlert(msg){
               
                           Toast.fire({
                            icon: 'success',
                            title: msg
                            })
        }

    function onDataBound(arg) {
        
        var element = $('[data-role="groupspager"]');
        if (element.length > 0) {

            element[0]['className']= 'k-pager-wrap k-grid-pager k-widget k-wrap';
            // element.removeClass('k-pager')
            // element.removeClass('k-pager-md')
            // element.addClass('k-pager-wrap');
            // element.addClass('k-widget');
            // element.addClass('k-wrap);
        }
    }
    $(document).ready(function () {
                
                const update='@update';
                const add='@added';
                const permission='@permission';
                const passwordUdpate = '@TempData["PasswordUpdate"]';
               if(update!=null && update!="")ToastAlert(update);
               if(add!=null && add!="")ToastAlert(add);
               if(permission!=null && permission!="")ToastAlert(permission);
               if (passwordUdpate != "") ToastAlert(passwordUdpate);
           

            var UserId = localStorage.getItem("UserID");
            localStorage.clear();
            var userID = '@TempData["UserID"]';
            if (userID != '') {


                var grid = $("#UserGrid").data("kendoGrid");
                var dataSource = grid.dataSource;
                var filters = dataSource.filter() || {};
                var sort = dataSource.sort() || {};
                var models = dataSource.data();
                var query = new kendo.data.Query(models);
                var rowNum = 0;
                var modelToSelect = null;
                models = query.filter(filters).sort(sort).data;
                for (var i = 0; i < models.length; ++i) {
                    var model = models[i];
                    if (model.UserID == userID) {
                        modelToSelect = model;
                        rowNum = i;
                        break;
                    }
                }
                var currentPageSize = grid.dataSource.pageSize();
                var pageWithRow = parseInt((rowNum / currentPageSize)) + 1; // pages are one-based
                grid.dataSource.page(pageWithRow);
                var row = $("#UserGrid").find("tr[data-uid='" + modelToSelect.uid + "']");
                if (row.length > 0) {
                    row.addClass("k-master-row k-state-selected");
                    //$("#grid").select(row).porper;
                    grid.content.scrollTop(grid.select().position().top);
                }
            }
            else {
                var grid = $("#UserGrid").data("kendoGrid");
                var dataSource = grid.dataSource;
                var filters = dataSource.filter() || {};
                var sort = dataSource.sort() || {};
                var models = dataSource.data();
                var query = new kendo.data.Query(models);
                var rowNum = 0;
                var modelToSelect = null;
                models = query.filter(filters).sort(sort).data;
                for (var i = 0; i < models.length; ++i) {
                    var model = models[i];
                    if (model.UserID == UserId) {
                        modelToSelect = model;
                        rowNum = i;
                        break;
                    }
                }
                // var currentPageSize = grid.dataSource.pageSize();
                // var pageWithRow = parseInt((rowNum / currentPageSize)) + 1; // pages are one-based
                // grid.dataSource.page(pageWithRow);
                // var row = $("#UserGrid").find("tr[data-uid='" + modelToSelect.uid + "']");
                // if (row.length > 0) {
                //     row.addClass("k-master-row k-state-selected");
                //     //$("#grid").select(row).porper;
                //     grid.content.scrollTop(grid.select().position().top);
                // }
            }
        


            });

    function AddNewUser() {
        window.location.href = '@Url.Action("AddNewUser", "ManageUsers")';
    }

</script>

