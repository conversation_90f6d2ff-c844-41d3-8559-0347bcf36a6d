﻿@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@using iRISupplyWebDeskManagePatients.Models
@model PatientRecord
@using Kendo.Mvc.UI
@{
    ViewBag.Title = "Remove Item";
    ViewBag.pTitle = "Please scan the RFID tag to remove item against patient";
    ViewBag.pageTitle = "Use Items";
    Layout = "~/Views/_Shared/_Layout.cshtml";
    //Layout = null;
    var session = HttpContextAccessor.HttpContext.Session;
    var appurl = session.GetString("BaseUrl");

}
<style>
    .custom-popup {
        height: 550px;
        width: 900px; /* Increased width to 1000px */
        max-width: 100%; /* Ensure it doesn't exceed the viewport */
        background-color: #f5f5f5 !important;
        color: #495057;
        margin-left: 100px;
    }

    /* Custom styling for SweetAlert buttons */
    .swal2-actions {
        display: flex;
        justify-content: space-between; /* Space between buttons, one aligned left, the other right */
    }

    .swal2-title.left-aligned-title {
        margin-left: 30px !important;
        text-align: left; /* Align the title to the left */
    }

    /* Container to hold all the rows */
    .item-info-container {
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 800px; /* Increased the max-width to 800px */
        margin: 0 auto;
        box-sizing: border-box; /* Include padding in width calculation */
    }

    /* Styling for each row */
    .item-info-row {
        display: flex;
        align-items: center;
        gap: 10px; /* Adds space between the label and the value */
    }

        /* Styling for the labels */
        .item-info-row label {
            width: 200px;
            text-align: left;
            margin-right: 10px; /* Adds space between the label and the value/input */
            font-size: 14px; /* Ensure font size is applied here */
            font-weight: bold;
        }

        /* Styling for the values (span) and input elements */
        .item-info-row span {
            font-size: 12px;
            padding: 8px;
            display: inline-block; /* Make span behave like inline-block to maintain alignment */
            border: none; /* No border for span */
            background-color: transparent; /* No background color */
            width: auto; /* Span takes only as much width as needed */
        }

        /* Style for input elements (for editable fields like textboxes) */
        .item-info-row input {
            font-size: 14px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: transparent;
            width: 1000px;
            margin-left: -19px;
        }

            /* Optional: Style for date input */
            .item-info-row input[type="date"] {
                padding: 8px;
                font-size: 14px;
                background-color: transparent;
            }

        /* Target only Catalog Number and Brand Name to be in the same row */
        .item-info-row.catalog-brand-row {
            display: flex;
            align-items: center; /* Align items vertically in the center */
            gap: 10px;
        }

    /* Optional: Ensure the label and span for Catalog Number and Brand Name have specific styles */
    .gap-btwn {
        margin-left: 250px;
    }

    /* Focus and hover effects */
    .item-info-row input:focus,
    .item-info-row select:focus,
    .item-info-row textarea:focus {
        outline: none;
        border-color: #4CAF50;
        box-shadow: 0 0 5px rgba(76, 175, 80, 0.2);
    }

    .item-info-row input:hover,
    .item-info-row select:hover {
        border-color: #888;
    }
    /* Ensure gap between label and span/input */
    .gap-btwn {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .item-info-container .item-info-row span {
        margin-left: -105px;
    }

    .item-info-container .item-info-row label {
        margin-top: 5px;
    }
    .form-control{
        border-bottom-right-radius: 18px;
        border-bottom-left-radius: 18px;
        border-top-left-radius: 18px;
        width: 50% !important;
        display: inline-block;
        border-top-right-radius: 18px;
    }

</style>
<style>
    .k-spacer {
        display: none !important;
    }

    .main-grid-container {
        margin-left: 10px;
        margin-right: 10px;
    }

    .k-expander-title {
        color: black !important;
    }

    .k-expander-sub-title {
        color: darkblue !important;
        font-weight: bolder !important;
        display: none !important;
    }

    #RemoveRFID {
        border: none;
        background: transparent;
        color: transparent;
    }

        #RemoveRFID:focus {
            outline: none;
        }

    .ManualEdit {
        text-align: end;
        color: black;
        padding: 20px;
        cursor: pointer;
    }

    #ManualInputRFIDAndBarcode {
        border: none;
        padding: 0px;
        background: transparent;
    }

    .modal-content {
        transform: translateY(100%);
        opacity: 0;
        transition: transform 0.5s ease-out, opacity 0.5s ease-out;
    }

    .modal.show .modal-content {
        transform: translateY(0);
        opacity: 1;
    }

    .modal.hide .modal-content {
        transform: translateY(100%);
        opacity: 0;
    }

    #searchBox::placeholder {
        color: grey;
        font-style: italic;
    }

    .swal2-input {
        border: 1px solid #ccc;
    }

    /* Red border for mandatory fields */
    .error-border {
        border: 2px solid red !important;
    }

    /* Add some spacing for the mandatory '*' icon */
    #serialNumberMandatory, #lotNumberMandatory {
        margin-left: 5px;
    }
</style>
<style>
    .k-submit-buttonn {
        background-color: #007bff; /* Change to your preferred color */
        color: white;
        border: none;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin-left: 5px;
        padding: 0;
        margin-left: 373px;
        position: relative;
        top: -36px;
    }

        .k-submit-buttonn:hover {
            background-color: #0056b3; /* Change to a darker shade for hover */
        }

        .k-submit-buttonn .k-icon {
            font-size: 18px;
        }
        #expiryDateMandatory{
            margin-left: 7px;
        }
</style>

@*<script src="_content/iRIsupplyWebDeskManagePatients/microsoft/signalr/dist/browser/signalr.js"></script>*@
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- JAVASCRIPT -->
<div class="main-grid-container">
    @if (string.IsNullOrEmpty(Model.Patientdetail.OverrideNote))
    {
        @(
            Html.Kendo().ExpansionPanel()
            //.Name(Model.Patientdetail.PatientName)
            .Name("PatientDeatil")
            .Title(Model.Patientdetail.PatientName)
            .SubTitle(Model.Patientdetail.ProcedureDescription)
            .Expanded(false)
            .Content(
    @<text>
        <div class="container">
            <div class="main-body">
                <div class="row gutters-sm">
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex flex-column align-items-center text-center">
                                            @*<img src="https://bootdey.com/img/Content/avatar/avatar7.png" alt="Admin" class="rounded-circle" width="50">*@
                                    <img src="~/assets/images/users/avatar-9.png" alt="Admin" class="rounded-circle" width="50">
                                    <div class="mt-3">
                                        <h10><b>@Model.Patientdetail.PatientName</b></h10>
                                                @*<p class="text-secondary mb-1"><span>Room:</span>TJUH ENDO 90</p>*@
                                                @*                                        <p class="text-muted font-size-sm">@Model.Patientdetail.PatientRoom</p>
*@                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="row" style="height:5px">
                                    <div class="col-sm-4 text-secondary">
                                                @*<h6 class="mb-0">Physician Name:</h6>*@
                                        <b>Physician Name :</b>
                                    </div>
                                    <div class="col-sm-8 text-secondary">
                                        <b> @Model.Patientdetail.PhysicianName</b>
                                    </div>
                                </div>
                                <hr>
                                <div class="row" style="height:5px">
                                    <div class="col-sm-4 text-secondary">
                                        <b>MRN    :</b>
                                    </div>
                                    <div class="col-sm-8 text-secondary">
                                        <b> @Model.Patientdetail.PatientMRN.Substring(5)</b>
                                    </div>
                                </div>
                                <hr>
                                <div class="row" style="height:5px">
                                    <div class="col-sm-4 text-secondary">
                                        <b>Case Id :</b>
                                    </div>
                                    <div class="col-sm-8 text-secondary">
                                        <b> @Model.Patientdetail.CASEID.Substring(8)</b>
                                    </div>
                                </div>
                                <hr>
                                <div class="row" style="height:5px">
                                    <div class="col-sm-4 text-secondary">
                                        <b>CSN :</b>
                                    </div>
                                    <div class="col-sm-8 text-secondary">
                                        <b> @Model.Patientdetail.PatientCSN.Substring(5)</b>
                                    </div>
                                </div>
                                        @*<hr>*@
                                        @* <div class="row">
                                    <div class="col-sm-2">
                                        <h6 class="mb-0">Address</h6>
                                    </div>
                                    <div class="col-sm-9 text-secondary">
                                        Bay Area, San Francisco, CA
                                    </div>
                                </div>*@
                                <hr>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </text>
            )
            )
    }
    @if (Model.itemsRemoved.Count > 0)
    {
        @(
            Html.Kendo().Grid(Model.patientusage)
            .Name("patientsgrid")
            // .ClientRowTemplate(rows =>rows.Height="1050px")
            //.ClientAltRowTemplateHandler(row => row.he)
            .Columns(columns =>
            {

                columns.Bound(u => u.PatientRoom).Title("Patient Room").Width(100).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
                .HtmlAttributes(new { @style = "text-align: center" });

                columns.Bound(u => u.PatientName)
                .Title("Patient Name / MRN")
                .ClientTemplate("<pname style='text-align: center'><b>#=PatientName#</b></pname><br> </br> MRN:+<b>#=PatientID#</b><br> </br>").Width(120).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" });
                //.ClientTemplate("Patient Name:+<pname style='text-align: center'><b>#=PatientName#</b></pname><br> </br> MRN:+<b>#=PatientID#</b><br> </br>").Width(120).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" });

                // columns.Select().Width(25).HtmlAttributes(new { @class = "checkbox-align" }).HeaderHtmlAttributes(new { @class = "checkbox-align" });

                @*columns.Bound(u => u.PatientID).Title("Patient ID").Width(150).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
                .HtmlAttributes(new { @style = "text-align: center" }) ;
        columns.Bound(u => u.PatientName).Title("Patient Name").Width(150).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: center" });
       *@
                columns.Bound(u => u.ReferringDoc)
    .Title("Attending / Admit Date")
    .ClientTemplate("<b>#=ReferringDoc#</b><br> </br> Admit Date:+<b>#=AdmitDate#</b><br> </br>").Width(120).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" });

                @* columns.Bound(u => u.AdmitDate).Title("Admit Date").Width(150).Hidden(true).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: center" });
        columns.Bound(u => u.ReferringDoc).Title("Referring Doctor").Width(150).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
                .HtmlAttributes(new { @style = "text-align: center" });
        columns.Bound(u => u.procdate).Title("Procedure Date").Format("{0:MM/dd/yy HH:mm:ss}").Width(150).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
                .HtmlAttributes(new { @style = "text-align: center" });*@
                //columns.Bound(u => u.Status).Title("Status").EditorTemplateName("StatusEditor").Width(90);
                columns.Command(command =>
                {
                    command.Custom("Edit").Template("<button  role='button' onclick ='RemoveItems(this)' class='btn btn-primary'><i class='fas fa-edit'></i></button>");

                }).Title("Return Item").Width(80).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
                .HtmlAttributes(new { @style = "text-align: center" });
            })
            //.Editable(editable => editable.Mode(GridEditMode.PopUp))
            //.Editable(editable => editable.Mode(GridEditMode.InCell))
            .Pageable(p =>
            {
                p.PageSizes(new[] { 5, 10, 20 });
            })
            .Selectable(selectable => selectable
            .Mode(GridSelectionMode.Multiple)
            .Type(GridSelectionType.Row))
            .Sortable()
            .Groupable()
            .Scrollable(scr => scr.Height(430))
            .ToolBar(t =>
            {
                t.Search();




                @*t.Custom().Text("New Device").HtmlAttributes(new { onclick = "AddNewReport();" });*@
                @*t.Excel();*@

            })
            .Filterable()
            .ColumnMenu(true)
            .Resizable(resize => resize.Columns(true))
            .DataSource(dataSource => dataSource
            .Ajax()
            .GroupPaging(true)
            .PageSize(10)
            .ServerOperation(false)

            @*.Update(u => u.Action("UpdateReport", "ManageReports")).Model(m => { m.Id(p => p.DeviceID); })*@
            )
            )
    }
    else
    {
        <div id="UsageDetailsGrid">
            <div class="container">
                <div class="row">
                    <div class="col-md-3"></div>
                    <div class="col-md-6 text-center" style="padding-top:20px">
                        <!-- Search Input Box -->
                            <input type="text" class="form-control" id="searchBox" placeholder="Product Catalog Search..." style="width: 60%; display: inline-block;" onkeydown="handleKeyPress(event)">
                        <!-- Search Button -->
                        <button class="k-submit-buttonn" id="searchButton" onclick="handleSearchClick();">
                            <span class="k-icon k-i-search"></span>
                        </button>
                        <div id="searchError" class="error-message" style="display:none; color: red; padding-top: 5px;"></div>
                    </div>
                    <div class="col-md-3 ManualEdit">
                        <button class="text-dark fas fa-edit fa-2x" data-bs-toggle="modal" data-bs-target="#ManualInput" id="ManualInputRFIDAndBarcode"></button>

                    </div>
                    <div class="col-md-3"></div>
                </div>
                <div class="row">
                    <div class="col-md-3"></div>
                    <div class="col-md-6 text-center" mt style=" padding-top:20px">
                        <img src="~/assets/images/RFIDTAG.png" class="mx-auto" alt="Admin">
                    </div>
                    
                </div>
            </div>
            <div style="text-align: center;padding-top:5px">
                <span><b class="text-dark" style="font-size:20px;">Please scan the RFID or Barcode.</b></span>

            </div>
        </div>  
    }
    <div id="partialContainer"></div>
</div>
<input type="text" id="RemoveRFID" spellcheck="false" />
<div class="modal fade" id="ManualInput" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title font-weight-bold" id="exampleModalLabel">Search by Catalog Number</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body">

                <div class="form-group">
                    <label for="exampleInputEmail1">Enter Catalog Number/Ref Number:</label>
                    <input type="text" class="form-control" id="tagno" required>

                </div>

            </div>
            <div class="modal-footer">
                <div style="margin-right: auto; display: none;">
                    <label class="h2">Quantity :</label>
                    <i class="fas fa-minus-circle fa-2x" id="Minus" style="cursor: pointer;"></i>
                    <strong class="h2 font-weight-bold mx-2" id="BarcodeCount">1</strong>
                    <i class="fas fa-plus-circle fa-2x" id="Plus" style="cursor: pointer;"></i>
                </div>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="Enter">Enter</button>
            </div>

        </div>
    </div>
</div>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")


<script>
     let ipAddress = '@Model.Patientdetail.UserIPAddress';
     var appbaseurl = '@appurl';
     let count = 1;

    // let url = `${appbaseurl}/removeItemHub?parmSi=${ipAddress}`;

</script>

<script>
    debugger;
        $(".Remove").click(function(){
            localStorage.setItem("view", "main");
            location.reload();
        });
        $(".PatienUsage").click(function(){
            localStorage.setItem("view", "partial");
            location.reload();

        });
        function GetPatientUsage() {


            $.ajax({
                type: 'get',
                url: '@Url.Action("GetPatientUsage", "RemoveItem")'

            })
            .done(function(response){
                $("#UsageDetailsGrid").html(response);
                    $(".PatienUsage").addClass("active_Module");
                    $(".Remove").removeClass("active_Module");


            })
            return false;
        }
        function validateDate(dateString) {
            if (dateString === "No expiration date") {
                return false;
            }else {
                var inputDate = new Date(dateString);
                var today = new Date();
                today.setHours(0, 0, 0, 0);
                return inputDate >= today;
            }
        }
        function addRowToGrid(newData) {
            debugger;
            let grid = $("#UsageGrid").data("kendoGrid");
            let dataSource = grid.dataSource;
            for (let i = 0; i < newData.length; i++) {
                if (newData[i].Status != "Success") {
                    Swal.fire({
                        position: 'center',
                        icon: 'error',
                        title: newData[i].Message,
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        timer: 3000,
                        customClass: {
                            popup: 'Remove-selection-class'
                        }
                    })
                }
                else {
                    dataSource.insert(0, {
                        RFID: newData[i].RFID, Description: newData[i].Description,
                        CatNo: newData[i].CatNo, ProductType: newData[i].ProductType,
                        SerialNo: newData[i].SerialNo, LotNo: newData[i].LotNo,
                        DateUsed: newData[i].DateUsed,
                        QTY:newData[i].QTY
                    });
                    function calculateTotalQTY(dataSource) {
                        let totalQTY = 0;
                        const dataItems = dataSource.data();
                        dataItems.forEach(function(item) {
                            totalQTY += parseFloat(item.QTY) || 0;
                        });
                        return totalQTY.toFixed(2);
    }
                    $("#removalcount").text(calculateTotalQTY(dataSource));
                    Toast.fire({
                        icon: 'success',
                        title: 'Item has been wasted successfully.'
                    })

                }

            }
            changeBgColor();
            //let topRow = grid.tbody.find(">tr:first");
            //topRow.addClass("highlighted-row k-selected");

        }
        function changeBgColor() {
            UpdateQuantity();
            debugger;
            var grid = $("#UsageGrid").data("kendoGrid");
            var dataSource = grid.dataSource;
            grid.tbody.find("tr").each(function () {
                var dataItem = grid.dataItem(this);
                var row = $(this);

                // Check the value of the property you want to use for conditional formatting
                var propertyValue = dataItem.RFID; // Change to your property name

                // Define the condition and set the row's background color
                if (!(propertyValue.startsWith('E00') || propertyValue.startsWith('MA'))) {
                    row.css("background-color", "#2fba41"); // Change to your desired background color
                    row.css("color", "white");
                    row.css("font-weight", 600);
                }
                else {
                    row.css("background-color", "#1F4788");
                    row.css("color", "white");
                    row.css("font-weight", 600);
                }
            });
        }
        function changeBgColorforIDG() {
            debugger;
            var grid = $("#ItemDetailsGrid").data("kendoGrid");
            var dataSource = grid.dataSource;
            grid.tbody.find("tr").each(function () {
                var dataItem = grid.dataItem(this);
                var row = $(this);

                // Check the value of the property you want to use for conditional formatting
                var propertyValue = dataItem.RFID; // Change to your property name

                // Define the condition and set the row's background color
                if (!(propertyValue.startsWith('E00') || propertyValue.startsWith('MA'))) {
                    row.css("background-color", "#2fba41"); // Change to your desired background color
                    row.css("color", "white");
                    row.css("font-weight", 600);
                }
                else {
                    row.css("background-color", "#1F4788");
                    row.css("color", "white");
                    row.css("font-weight", 600);
                }
            });
        }
        $("#ManualInputRFIDAndBarcode").click(function () {
            $("#ManualInput").modal(focus);
             $('body').css('pointer-events', 'none');
        });
         $('#ManualInput').on('hidden.bs.modal', function () {
        $('body').css('pointer-events', 'auto');
        });
        function EnterRFIDAndBarcode() {
            $("#ManualInput").modal(focus);
        };
        $("#Enter").click(function () {
            const itemNo = $("#tagno").val();
            if (itemNo !== "") {
                const RFID = itemNo
                $("#tagno").val("");
                // RemoveItemDetails(RFID,count);
                removeItemDetails(RFID, count);
            }
        });
        $("#tagno").keydown(function () {
            $('#BarcodeCount').text("1");
        })
        $(document).ready(function () {

            const view = localStorage.getItem("view");

            if(view=="partial"){

                GetPatientUsage();
            }

            // count of barcode increase and decrease

        $('#BarcodeCount').text(count);

        // Handle plus icon click
        $('#Plus').on('click', function() {
            const itemNo = $("#tagno").val();
             if (itemNo !== "" && itemNo.substring(0, 3).toLowerCase() !== "e00") {
                count += 1;
                $('#BarcodeCount').text(count);
            } else if (itemNo === "") {
                    alert("Scan the items");
            }
            else {
                alert("You can not increase/decrease qty for RFID item");
            }

        });

        // Handle minus icon click
        $('#Minus').on('click', function() {
                const itemNo = $("#tagno").val();
            if (itemNo !== "" && itemNo.substring(0, 3).toLowerCase() !== "e00") {
                if (count > 1) {
                    count -= 1;
                    $('#BarcodeCount').text(count);
                }
            }else if (itemNo === "") {
                    alert("Scan the items");
            } else {
                alert("You can not increase/decrease qty for RFID item!");
            }
        });

            var barcode = "";
            document.body.addEventListener("keypress", function (evt) {
                if (evt.key === 'Enter') {
                    if (barcode !== null && barcode !== "") {
                        var RFID = barcode;
                        barcode = "";
                        $("#tagno").val("");
                        // RemoveItemDetails(RFID,count);
                        removeItemDetails(RFID, count);
                    }
                }
                else {
                    barcode += evt.key;
                }
            })
        })
</script>

<script>
        function RemoveOneTimeItemDetails(RFID,Qty){
                debugger;
                const RFIDIttemStaus='@Model.OverrideNote';
                $.ajax({
                    type: 'get',
                    url: `${appbaseurl}/RemoveItem/ProcessRFID`,
                    data: { RFID: RFID, Qty: Qty,OvrrideNote:RFIDIttemStaus }
                })
                    .done(function (response) {
                        debugger;
                        $('#BarcodeCount').text("1");
                        count = 1;
                         $("#ManualInput").modal('hide');
                         $("#ManualInputRFIDAndBarcode").modal('hide');
                        // Check if the function exists before calling it
                        if (typeof resetTimer === 'function') {
                            resetTimer(); // Call the function
                        }
                        if (response == "Item already removed against patient.") {
                            Swal.fire({
                                position: 'center',
                                icon: 'error',
                                title: response,
                                allowOutsideClick: false,
                                showConfirmButton: false,
                                timer: 5000,
                                customClass: {
                                    popup: 'Remove-selection-class'
                                }
                            })
                        }
                        else if (response == "RFID not associated to any item.") {
                            Swal.fire({
                                position: 'center',
                                icon: 'error',
                                title: response,
                                showConfirmButton: false,
                                allowOutsideClick: false,
                                timer: 3000,
                                customClass: {
                                    popup: 'Remove-selection-class'
                                }
                            })
                        }
                        else if (response == "Success") {
                            $("#UsageGrid").data("kendoGrid").dataSource.read();
                            if (Status == "Success") {
                                Toast.fire({
                                    icon: 'success',
                                    title: 'Item Removed Successfully.'
                                })
                            }

                        }
                        else if (response == "other") {
                            Swal.fire({
                                position: 'center',
                                icon: 'error',
                                title: 'Something went wrong please try again.',
                                showConfirmButton: false,
                                allowOutsideClick: false,
                                timer: 3000,
                                customClass: {
                                    popup: 'Remove-selection-class'
                                }
                            })
                        }
                       else if (response[0].Status == "Fail") {
                            Swal.fire({
                                position: 'center',
                                icon: 'error',
                                title: 'Something went wrong, please try again.',
                                showConfirmButton: false,
                                allowOutsideClick: false,
                                timer: 3000,
                                customClass: {
                                    popup: 'Remove-selection-class'
                                }
                            })
                        }
                        else {
                             if(RFIDIttemStaus!=="" && RFIDIttemStaus!=="RemoveItem" && RFID.startsWith("E00")){
                                  ItemDetails = response;

                                    var divElement = $('#UsageDetailsGrid');
                                    if (divElement.find('.k-grid').length > 0) {
                                        addRowToGrid(ItemDetails);
                                    }else {
                                        $.ajax({
                                            type: 'get',
                                            url: `${appbaseurl}/RemoveItem/DisplayGrid`
                                        }).done(function (response) {
                                            $("#UsageDetailsGrid").html(response);
                                            addRowToGrid(ItemDetails);
                                        })
                                    }
                             }else{
                                    Toast.fire({
                                        icon: 'success',
                                        title: 'Item Removed Successfully.'
                                    })

                                GetPatientUsage();
                             }
                        }
                    })
        }
            async function removeItemDetails(RFID, Qty) {
            debugger;
            var ItemDetails;
            const settingValues = '@ViewBag.SettingValue';

            switch(settingValues) {
                case "1":
                    if (RFID.startsWith("E00")) {
                        oneTimeUseScreenforRFID(RFID, Qty);
                    } else {
                        const isFDA = await checkIsFDAorNot(RFID);
                        if(isFDA){
                            oneTimeUseScreenforRFID(RFID, Qty);
                        }
                        else{
                            removeBarCodeItems(ItemDetails, RFID, Qty);
                        }
                    }
                    break;

                case "2":
                    if (RFID.startsWith("E00")) {
                        removeRFIDItems(RFID, Qty);
                    } else {
                        oneTimeUseScreenforBarcode(RFID, Qty);
                    }
                    break;

                case "3":
                    if (RFID.startsWith("E00")) {
                        oneTimeUseScreenforRFID(RFID, Qty);
                    } else {
                        oneTimeUseScreenforBarcode(RFID, Qty);
                    }
                    break;

                case "0":
                    if (RFID.startsWith("E00")) {
                        removeRFIDItems(RFID, Qty);
                    } else {
                        const isFDA = await checkIsFDAorNot(RFID,Qty);
                      @*   if(isFDA){
                            oneTimeUseScreenforRFID(RFID, Qty);
                        }
                        else{
                            removeBarCodeItems(ItemDetails, RFID, Qty);
                        } *@
                    }
                    break;

                default:
                    console.error("Invalid setting value: ", settingValues);
                    break;
            }
        }
        async function oneTimeUseScreenforRFID(RFID, Qty){
            try {
                const data = await timeOfImplant(RFID);

                    let description = data.Description ? data.Description : "No description available";
                    let expiredDate = data.ExpiredDate ? data.ExpiredDate : "No expiration date";
                    let ItemStatusID = data.ItemStatusID;

                // Make the AJAX call to fetch item details based on RFID
                const response = await $.ajax({
                    type: 'GET',
                    url: `${appbaseurl}/RemoveItem/FetchItemDetails`,
                    data: { RFID: RFID },
                });
                if (response.message === "The scanned barcode is an RFID item") {
                    $("#ManualInput").modal('hide');
                    Swal.fire({
                        icon: 'info',
                        allowOutsideClick: false,
                        title: 'The scanned barcode is a tracked RFID product.<br>please scan RFID.'
                    });
                }
                else if(response.message === "Not IMPLANT RFID Item Found"){
                    if (ItemStatusID == "1" || ItemStatusID == "0" || ItemStatusID == "2") {
                        $("#ManualInput").modal('hide');
                        const result = await Swal.fire({
                            title: "Verify Package Integrity",
                            html: `Item Description: <div style='font-size:14px;'>${description}</div><br/> Expired Date: ${expiredDate}`,
                            icon: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#3085d6",
                            cancelButtonColor: "#d33",
                            confirmButtonText: "Verify!",
                            allowOutsideClick: false
                        });

                        if (result.isConfirmed) {
                            if (validateDate(expiredDate)) {
                                debugger;
                                //var RFID = formData.RFID;  // Assuming RFID is a field in formData
                                var Qty = 1;    // Assuming Qty is a field in formData
                                RemoveOneTimeItemDetails(RFID, Qty);
                                GetPatientUsage();
                            } else {
                                Swal.fire({
                                    position: "center",
                                    icon: "error",
                                    title: "Item has expired.",
                                    showConfirmButton: true,
                                    timer: 3000
                                });
                            }
                            // GetPatientUsage();

                        }
                    } else {
                        if (ItemStatusID == "4") {
                            $("#ManualInput").modal('hide');
                             Swal.fire({
                                 position: 'center',
                                 icon: 'error',
                                 title: "Item already removed against patient.",
                                 allowOutsideClick: false,
                                 showConfirmButton: false,
                                 timer: 3000,
                                 customClass: {
                                     popup: 'Remove-selection-class'
                                 }
                             });
                        }
                       
                        // let ItemStatus = ItemStatusID == "2" || ItemStatusID == "4" ? "Item already removed against patient." : "RFID not associated to any item.";
                        // Swal.fire({
                        //     position: 'center',
                        //     icon: 'error',
                        //     title: ItemStatus,
                        //     showConfirmButton: false,
                        //     timer: 3000,
                        //     customClass: {
                        //         popup: 'Remove-selection-class'
                        //     }
                        // });
                    }
                }
                // Check if the response is successful
                else if (response.success) {
                    console.log('Response data:', response.data);
                    $("#ManualInput").modal('hide');
                    var patientName = '@Model.Patientdetail.PatientName';

                    // Show the popup with item details using Swal
                    if ((RFID.startsWith("E00") && (ItemStatusID == "1" || ItemStatusID == "0" || ItemStatusID == "2")) || ('isLotNoReq' in response.data)){
                        const result = await Swal.fire({
                        title: 'Please review and verify One Time Use data for <strong>' + patientName + '</strong>.',
                        html: `
                            <div class="item-info-container">
                                <div class="item-info-row">
                                    <label for="catalogNumber">Catalog Number :</label>
                                    <span id="catalogNumber"${response.data.isLotNoReq ? 'style="color: red;"' : ''}>${response.data.catalogNumber}</span>
                                </div>
                                <div class="item-info-row">
                                    <label for="brandName">Brand Name :</label>
                                    <span id="brandName"${response.data.isLotNoReq ? 'style="color: red;"' : ''}>${response.data.brandName}</span>
                                </div>
                                <div class="item-info-row">
                                    <label for="description">Description :</label>
                                    <span id="description"${response.data.isLotNoReq ? 'style="color: red;"' : ''}>${response.data.description}</span>
                                </div>
                                <div class="item-info-row">
                                    <label for="manufacturer">Manufacturer :</label>
                                    <span id="manufacturer"${response.data.isLotNoReq ? 'style="color: red;"' : ''}>${response.data.manufacturer}</span>
                                </div>
                                <div class="item-info-row">
                                    <label for="serialNumber" style="margin-top: 10px;">Serial Number :</label>
                                    <input type="text" id="serialNumber" class="swal2-input" value="${response.data.serialNumber}" ${response.data.isLotNoReq ? 'style="border-color: red;"' : ''}>
                                </div>
                                <div class="item-info-row">
                                    <label for="lotNumber" style="margin-top: 10px;">
                                        Lot Number :
                                        ${response.data.isLotNoReq ? '<span style="color: red;">*</span>' : ''}
                                    </label>
                                    <input type="text" id="lotNumber" class="swal2-input" value="${response.data.lotNumber}"
                                        ${response.data.isLotNoReq ? 'style="border-color: red;" required' : ''}>
                                </div>
                                <div class="item-info-row">
                                    <label for="expiryDate" style="margin-top: 10px;">Expiry Date :</label>
                                    <input type="date" id="expiryDate" class="swal2-input" value="${response.data.expiryDate}"}>
                                </div>
                                <!-- Warning message below inputs -->
                                ${response.data.isLotNoReq ? `
                                    <div style="color: green; margin-top: 10px;">
                                        This item is from FDA and requires additional review.
                                    </div>
                                ` : ''}
                            </div>
                        `,
                        showCancelButton: true,
                        confirmButtonText: 'Submit',
                        cancelButtonText: 'Cancel',
                        allowOutsideClick: false,
                        preConfirm: () => {
                            // Collect the form data entered by the user
                            const LotNumber = document.getElementById('lotNumber').value;
                            const SerialNumber = document.getElementById('serialNumber').value;
                            if (response.data.isLotNoReq) {
                                if(!LotNumber){
                                    Swal.showValidationMessage('Lot Number is required');
                                    return false; // Prevent form submission
                                }
                            }
                            if (response.data.isSerialNoReq === true) {
                                if (!SerialNumber) {
                                    Swal.showValidationMessage('Serial Number is required');
                                    return false; // Prevent form submission
                                }
                            }
                            const CatalogNumber = document.getElementById('catalogNumber').textContent;
                            const BrandName = document.getElementById('brandName').textContent;
                            const Description = document.getElementById('description').textContent;
                            const Manufacturer = document.getElementById('manufacturer').textContent;
                            // const SerialNumber = document.getElementById('serialNumber').value;
                            // const LotNumber = document.getElementById('lotNumber').value;
                            const ExpiryDate = document.getElementById('expiryDate').value;

                            // Return the data to the Swal callback
                            return { RFID, CatalogNumber, BrandName, Description, Manufacturer, SerialNumber, LotNumber, ExpiryDate };
                        },
                        customClass: {
                            popup: 'custom-popup', // Add custom class to the popup
                            title: 'left-aligned-title',
                            actions: 'swal2-actions'
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Prepare the form data for submission
                            const formData = {
                                RFID: result.value.RFID,
                                catalogNumber: result.value.CatalogNumber,
                                brandName: result.value.BrandName,
                                description: result.value.Description,
                                manufacturer: result.value.Manufacturer,
                                serialNumber: result.value.SerialNumber,
                                lotNumber: result.value.LotNumber,
                                expiryDate: result.value.ExpiryDate
                            };
                            // Submit the form data via POST to save it
                            $.ajax({
                                type: 'POST', // Use POST to submit the form data
                                url: `${appbaseurl}/RemoveItem/SaveOneTimeUseitemDetails`,
                                contentType: 'application/json',
                                data: JSON.stringify(formData),
                                success: function (saveResponse) {
                                    if (saveResponse.success) {
                                        Swal.fire({
                                            icon: 'success',
                                            title: 'Item details saved and removed successfully.',
                                            showConfirmButton: false,
                                            allowOutsideClick: false,
                                            timer: 2000
                                        });

                                        var RFID = formData.RFID;  // Assuming RFID is a field in formData
                                        var Qty = 1;    // Assuming Qty is a field in formData
                                        RemoveOneTimeItemDetails(RFID, Qty);
                                        GetPatientUsage();
                                    } @* else {
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Lot number already Exist, please scan again.',
                                            text: saveResponse.message,
                                            allowOutsideClick: false,
                                            showConfirmButton: true
                                        });
                                    } *@
                                    // GetPatientUsage();
                                },
                                error: function () {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Error',
                                        text: 'There was an error saving the item details.',
                                        showConfirmButton: false,
                                        allowOutsideClick: false,
                                        timer: 3000
                                    });
                                }
                            });
                        }
                    });
                        if (ItemStatusID == "1" || ItemStatusID == "0" || ItemStatusID == "2") {
                            $("#ManualInput").modal('hide');
                            const result = await Swal.fire({
                                title: "Verify Package Integrity",
                                html: `Item Description: <div style='font-size:14px;'>${description}</div><br/> Expired Date: ${expiredDate}`,
                                icon: "warning",
                                showCancelButton: true,
                                confirmButtonColor: "#3085d6",
                                cancelButtonColor: "#d33",
                                confirmButtonText: "Verify!",
                                allowOutsideClick: false
                            });

                            if (result.isConfirmed) {
                                if (validateDate(expiredDate)) {
                                    debugger;
                                    //var RFID = formData.RFID;  // Assuming RFID is a field in formData
                                    var Qty = 1;    // Assuming Qty is a field in formData
                                    RemoveOneTimeItemDetails(RFID, Qty);
                                    GetPatientUsage();
                                } else {
                                    Swal.fire({
                                        position: "center",
                                        icon: "error",
                                        title: "Item has expired.",
                                        showConfirmButton: true,
                                        timer: 3000
                                    });
                                }
                                // GetPatientUsage();

                            }
                        } else {
                            $("#ManualInput").modal('hide');
                            // let ItemStatus = ItemStatusID == "2" || ItemStatusID == "4" ? "Item already removed against patient." : "RFID not associated to any item.";
                            // Swal.fire({
                            //     position: 'center',
                            //     icon: 'error',
                            //     title: ItemStatus,
                            //     showConfirmButton: false,
                            //     timer: 3000,
                            //     customClass: {
                            //         popup: 'Remove-selection-class'
                            //     }
                            // });
                        }
                    }
                        else {
                            $("#ManualInput").modal('hide');

                            // Determine ItemStatus message based on ItemStatusID
                            let ItemStatus = (ItemStatusID == "2" || ItemStatusID == "4")
                                ? "Item already removed against patient."
                                : "RFID not associated to any item.";

                            // If no item found or the above condition is true, show appropriate error message
                            if (ItemStatusID == "2" || ItemStatusID == "4" || !ItemStatusID) {
                                Swal.fire({
                                    position: 'center',
                                    icon: 'error',
                                    title: ItemStatus,
                                    showConfirmButton: false,
                                    timer: 5000,
                                    customClass: {
                                        popup: 'Remove-selection-class'
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    allowOutsideClick: false,
                                    text: 'No item found. Please scan again.'
                                });
                            }
                        }
                } else {
                    $("#ManualInput").modal('hide');

                    // Determine ItemStatus message based on ItemStatusID
                    let ItemStatus = (ItemStatusID == "2" || ItemStatusID == "4")
                        ? "Item already removed against patient."
                        : "RFID not associated to any item.";

                    // If no item found or the above condition is true, show appropriate error message
                    if (ItemStatusID == "2" || ItemStatusID == "4" || !ItemStatusID) {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: ItemStatus,
                            showConfirmButton: false,
                            timer: 5000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            allowOutsideClick: false,
                            text: 'No item found. Please scan again.'
                        });
                    }
                }
            } catch (error) {
                $("#ManualInput").modal('hide');
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    allowOutsideClick: false,
                    text: 'There was an error fetching item details.'
                });
            }
        }
        async function removeRFIDItems(RFID, Qty){
            debugger;
            var ItemDetails;
            if (RFID.startsWith("E00")) {
                try {
                    const data = await timeOfImplant(RFID);

                    let description = data.Description ? data.Description : "No description available";
                    let expiredDate = data.ExpiredDate ? data.ExpiredDate : "No expiration date";
                    let ItemStatusID = data.ItemStatusID;
                    let isImplant = data.isImplant;
                    let ProductTypeID = data.ProductTypeID;
                    const RFIDIttemStaus='@Model.OverrideNote';

                    if ((ItemStatusID == "1" || ItemStatusID == "0" || ItemStatusID == "2" || ItemStatusID == "4") && isImplant == 'Y' && RFIDIttemStaus == "RemoveItem") {
                        $("#ManualInput").modal('hide');
                        if (validateDate(expiredDate) || expiredDate === "No expiration date") {
                                if(expiredDate === "No expiration date"){
                                    expiredDate = "N/A";
                                }
                                    const result = await Swal.fire({
                                    title: "Verify Package Integrity",
                                    html: `Item Description: <div style='font-size:14px;'>${description}</div><br/> Expired Date: ${expiredDate}`,
                                    icon: "warning",
                                    showCancelButton: true,
                                    confirmButtonColor: "#3085d6",
                                    cancelButtonColor: "#d33",
                                    confirmButtonText: "Verify!",
                                    allowOutsideClick: false
                                });
                            if (result.isConfirmed) {
                                Swal.fire({
                                    position: 'center',
                                    icon: 'info',
                                    title: 'Please complete tissue usage details in EPIC',
                                    showConfirmButton: true,
                                    customClass: {
                                        popup: 'Remove-selection-class'
                                    }
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        RemoveBarcodeAndRFID(ItemDetails, RFID, Qty);
                                    }
                                });                            
                            } 
                        } else {
                            Swal.fire({
                                position: "center",
                                icon: "error",
                                title: "Item has expired.",
                                showConfirmButton: true,
                                timer: 3000
                            });
                        }
                    }
                    else if ((ItemStatusID == "1" || ItemStatusID == "0" || ItemStatusID == "2" || ItemStatusID == "4") && ProductTypeID == "5" && isImplant == 'Y' && RFIDIttemStaus == "RemoveItem") {
                        $("#ManualInput").modal('hide');
                        if (validateDate(expiredDate) || expiredDate === "No expiration date") {
                                if(expiredDate === "No expiration date"){
                                    expiredDate = "N/A";
                                }
                                    const result = await Swal.fire({
                                    title: "Verify Package Integrity",
                                    html: `Item Description: <div style='font-size:14px;'>${description}</div><br/> Expired Date: ${expiredDate}`,
                                    icon: "warning",
                                    showCancelButton: true,
                                    confirmButtonColor: "#3085d6",
                                    cancelButtonColor: "#d33",
                                    confirmButtonText: "Verify!",
                                    allowOutsideClick: false
                                });
                            if (result.isConfirmed) {
                                Swal.fire({
                                    position: 'center',
                                    icon: 'info',
                                    title: 'Please complete tissue usage details in EPIC',
                                    showConfirmButton: true,
                                    customClass: {
                                        popup: 'Remove-selection-class'
                                    }
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        RemoveBarcodeAndRFID(ItemDetails, RFID, Qty);
                                    }
                                });
                            }
                        } else {
                            Swal.fire({
                                position: "center",
                                icon: "error",
                                title: "Item has expired.",
                                showConfirmButton: true,
                                timer: 3000
                            });
                        }
                    }
                    @* else if ((ItemStatusID == "1" || ItemStatusID == "0" || ItemStatusID == "2" || ItemStatusID == "4") && (isImplant == 'N' || isImplant == "") && ProductTypeID == "5" && RFIDIttemStaus == "RemoveItem") {
                        if (validateDate(expiredDate) || expiredDate === "No expiration date") {
                            if(expiredDate === "No expiration date"){
                                expiredDate = "N/A";
                            }
                            $("#ManualInput").modal('hide');
                            Swal.fire({
                                position: 'center',
                                icon: 'info',
                                title: 'Please complete tissue usage details in EPIC',
                                showConfirmButton: true,
                                customClass: {
                                    popup: 'Remove-selection-class'
                                }
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    RemoveBarcodeAndRFID(ItemDetails, RFID, Qty);
                                }
                            });
                        } else {
                            $("#ManualInput").modal('hide');
                            Swal.fire({
                                position: "center",
                                icon: "error",
                                title: "Item has expired.",
                                showConfirmButton: true,
                                timer: 3000
                            });
                        }
                    } *@
                    else if ((ItemStatusID == "1" || ItemStatusID == "0" || ItemStatusID == "2" || ItemStatusID == "4") && RFID.startsWith("E007M") && isImplant == 'Y' && RFIDIttemStaus == "RemoveItem") {
                        if (validateDate(expiredDate) || expiredDate === "No expiration date") {
                            if(expiredDate === "No expiration date"){
                                expiredDate = "N/A";
                            }
                            $("#ManualInput").modal('hide');
                            Swal.fire({
                                position: 'center',
                                icon: 'info',
                                title: 'Please complete tissue usage details in EPIC',
                                showConfirmButton: true,
                                customClass: {
                                    popup: 'Remove-selection-class'
                                }
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    RemoveBarcodeAndRFID(ItemDetails, RFID, Qty);
                                }
                            });
                        } else {
                            $("#ManualInput").modal('hide');
                            Swal.fire({
                                position: "center",
                                icon: "error",
                                title: "Item has expired.",
                                showConfirmButton: true,
                                timer: 3000
                            });
                        }
                    }
                    else if ((ItemStatusID == "1" || ItemStatusID == "0" || ItemStatusID == "2" || ItemStatusID == "4") && (isImplant == 'N' || isImplant == "") && RFIDIttemStaus == "RemoveItem") {
                        if (validateDate(expiredDate) || expiredDate === "No expiration date") {
                            if(expiredDate === "No expiration date"){
                                expiredDate = "N/A";
                            }
                                RemoveBarcodeAndRFID(ItemDetails, RFID, Qty);
                        } else {
                            $("#ManualInput").modal('hide');
                            Swal.fire({
                                position: "center",
                                icon: "error",
                                title: "Item has expired.",
                                showConfirmButton: true,
                                timer: 3000
                            });
                        }
                    }
                    else if(RFIDIttemStaus != "RemoveItem"){
                        RemoveBarcodeAndRFID(ItemDetails, RFID, Qty);
                    }else {
                        $("#ManualInput").modal('hide');
                        let ItemStatus = ItemStatusID == "2" || ItemStatusID == "4" ? "Item already removed against patient." : "RFID not associated to any item.";
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: ItemStatus,
                            showConfirmButton: false,
                            timer: 5000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        });
                    }

                } catch (error) {
                    console.error('Error occurred during the AJAX request or Swal:', error);
                    // Optionally handle the error (e.g., show an error alert)
                    Swal.fire({
                        position: 'center',
                        icon: 'error',
                        title: 'An error occurred while processing the request.',
                        showConfirmButton: true,
                        timer: 3000
                    });
                }
            }
        }
        function RemoveBarcodeAndRFID(ItemDetails, RFID, Qty) {
            debugger;
            const RFIDIttemStaus='@Model.OverrideNote';
            $.ajax({
                type: 'get',
                url: `${appbaseurl}/RemoveItem/ProcessRFID`,
                data: { RFID: RFID, Qty: Qty,OvrrideNote:RFIDIttemStaus }
            })
                .done(function (response) {
                    debugger;
                    $('#BarcodeCount').text("1");
                    count = 1;
                     $("#ManualInput").modal('hide');
                     $("#ManualInputRFIDAndBarcode").modal('hide');
                    // Check if the function exists before calling it
                    if (typeof resetTimer === 'function') {
                        resetTimer(); // Call the function
                    }
                    const itemMessage = response[0].Message;
                    if (itemMessage == "Item already removed against patient.") {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: itemMessage,
                            showConfirmButton: true,
                            allowOutsideClick: false,
                            timer: 5000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        })
                    }
                    else if (itemMessage.includes("Item is already used for patient")) {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: itemMessage,
                            showConfirmButton: true,
                            showCancelButton: true,
                            confirmButtonText: 'Yes',
                            cancelButtonText: 'No',
                            allowOutsideClick: false,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        }).then((result) => {
                                if (result.isConfirmed) {
                                    $.ajax({
                                        type: 'get',
                                        url: `${appbaseurl}/RemoveItem/ProcessRFIDForDifferentPatient`,
                                        data: { RFID: RFID, Qty: Qty,OvrrideNote:RFIDIttemStaus }
                                        }).done(function (response){
                                            const itemMessage = response[0].Message;
                                            if (itemMessage == "Item already removed against patient.") {
                                                Swal.fire({
                                                    position: 'center',
                                                    icon: 'error',
                                                    title: itemMessage,
                                                    showConfirmButton: true,
                                                    allowOutsideClick: false,
                                                    timer: 5000,
                                                    customClass: {
                                                        popup: 'Remove-selection-class'
                                                    }
                                                })
                                            }
                                            else if (response == "Success") {
                                                $("#UsageGrid").data("kendoGrid").dataSource.read();
                                                if (Status == "Success") {
                                                    Toast.fire({
                                                        icon: 'success',
                                                        title: 'Item Removed Successfully.'
                                                    })
                                                }

                                            }
                                            else if (itemMessage.Message == "Item Removed Successfully.") {
                                                Toast.fire({
                                                        icon: 'success',
                                                        title: 'Item Removed Successfully.'
                                                    })
                                                }
                                            else {
                                                if(RFIDIttemStaus!=="" && RFIDIttemStaus!=="RemoveItem" && RFID.startsWith("E00")){
                                                    ItemDetails = response;

                                                    var divElement = $('#UsageDetailsGrid');
                                                    if (divElement.find('.k-grid').length > 0) {
                                                        addRowToGrid(ItemDetails);
                                                    }else {
                                                        $.ajax({
                                                            type: 'get',
                                                            url: `${appbaseurl}/RemoveItem/DisplayGrid`
                                                        }).done(function (response) {
                                                            $("#UsageDetailsGrid").html(response);
                                                            addRowToGrid(ItemDetails);
                                                        })
                                                    }
                                                }else{
                                                    Toast.fire({
                                                        icon: 'success',
                                                        title: 'Item Removed Successfully.'
                                                    })

                                                GetPatientUsage();
                                                }
                                            }
                                        })
                                } else if (result.isDismissed) {
                                    // User clicked "No" (cancelled the action)
                                    console.log('Action was cancelled by the user.');
                                }
                        });
                    }
                    else if (response == "Item already removed against patient.") {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: response,
                            showConfirmButton: false,
                            timer: 5000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        })
                    }
                    else if (response == "RFID not associated to any item.") {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: response,
                            showConfirmButton: false,
                            timer: 3000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        })
                    }
                    else if (response == "Success") {
                        $("#UsageGrid").data("kendoGrid").dataSource.read();
                        if (Status == "Success") {
                            Toast.fire({
                                icon: 'success',
                                title: 'Item Removed Successfully.'
                            })
                        }

                    }
                    else if (response == "other") {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                                title: 'Product is not registered.',
                            showConfirmButton: false,
                            timer: 3000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        })
                    }
                   else if (response[0].Status == "Fail") {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                                title: 'Product is not registered.',
                            showConfirmButton: false,
                            timer: 3000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        })
                    }
                    else {
                         if(RFIDIttemStaus!=="" && RFIDIttemStaus!=="RemoveItem" && RFID.startsWith("E00")){
                              ItemDetails = response;

                                var divElement = $('#UsageDetailsGrid');
                                if (divElement.find('.k-grid').length > 0) {
                                    addRowToGrid(ItemDetails);
                                }else {
                                    $.ajax({
                                        type: 'get',
                                        url: `${appbaseurl}/RemoveItem/DisplayGrid`
                                    }).done(function (response) {
                                        $("#UsageDetailsGrid").html(response);
                                        addRowToGrid(ItemDetails);
                                    })
                                }
                         }else{
                                Toast.fire({
                                    icon: 'success',
                                    title: 'Item Removed Successfully.'
                                })

                            GetPatientUsage();
                         }
                    }
                })
        }
        function removeBarCodeItems(ItemDetails, RFID, Qty){
            RemoveBarcodeAndRFID(ItemDetails, RFID, Qty);
        }
        async function oneTimeUseScreenforBarcode(RFID, Qty){
            try {
                // Make the AJAX call to fetch item details based on RFID
                const response = await $.ajax({
                    type: 'GET',
                    url: `${appbaseurl}/RemoveItem/FetchItemDetails`,
                    data: { RFID: RFID },
                });
                if (response.message === "The scanned barcode is an RFID item") {
                    $("#ManualInput").modal('hide');
                    Swal.fire({
                        icon: 'info',
                        allowOutsideClick: false,
                        title: 'The scanned barcode is a tracked RFID product.<br>please scan RFID.'
                    });
                }
                // Check if the response is successful                        
                    else if (response.success && 'isLotNoReq' in response.data) {
                        console.log('Response data:', response.data);
                        $("#ManualInput").modal('hide');
                        var patientName = '@Model.Patientdetail.PatientName';

                        // Show the popup with item details using Swal
                        if ('isLotNoReq' in response.data){
                            const result = await Swal.fire({
                            title: 'Please review and verify product details for <strong>' + patientName + '</strong>.',
                            html: `
                                <div class="item-info-container">
                                    <div class="item-info-row">
                                        <label for="catalogNumber">Catalog Number :</label>
                                        <span id="catalogNumber"${response.data.isLotNoReq ? 'style="color: red;"' : ''}>${response.data.catalogNumber}</span>
                                    </div>
                                    <div class="item-info-row">
                                        <label for="brandName">Brand Name :</label>
                                        <span id="brandName"${response.data.isLotNoReq ? 'style="color: red;"' : ''}>${response.data.brandName}</span>
                                    </div>
                                    <div class="item-info-row">
                                        <label for="description">Description :</label>
                                        <span id="description"${response.data.isLotNoReq ? 'style="color: red;"' : ''}>${response.data.description}</span>
                                    </div>
                                    <div class="item-info-row">
                                        <label for="manufacturer">Manufacturer :</label>
                                        <span id="manufacturer"${response.data.isLotNoReq ? 'style="color: red;"' : ''}>${response.data.manufacturer}</span>
                                    </div>
                                    <div class="item-info-row">
                                        <label for="serialNumber" style="margin-top: 10px;">
                                            ${response.data.isSerialNoReq ? '<span style="color: red;">*</span>' : ''}
                                            Serial Number :
                                        </label>
                                        <input type="text" id="serialNumber" class="swal2-input" value="${response.data.serialNumber}"
                                            ${response.data.isSerialNoReq ? 'style="border-color: red;"' : ''}>
                                    </div>
                                    <div class="item-info-row">
                                        <label for="lotNumber" style="margin-top: 10px;">
                                            ${response.data.isLotNoReq ? '<span style="color: red;">*</span>' : ''}
                                            Lot Number :
                                        </label>
                                        <input type="text" id="lotNumber" class="swal2-input" value="${response.data.lotNumber}"
                                            ${response.data.isLotNoReq ? 'style="border-color: red;" required' : ''}>
                                    </div>
                                    <div class="item-info-row">
                                        <label for="expiryDate" style="margin-top: 10px;">Expiry Date :</label>
                                        <input type="date" id="expiryDate" class="swal2-input" value="${response.data.expiryDate}" ${response.data.isLotNoReq ? 'style="border-color: red;"' : ''}>
                                    </div>
                                    <!-- Warning message below inputs -->
                                    ${response.data.isLotNoReq ? `
                                        <div style="color: green; margin-top: 10px;">
                                            This item is from FDA and requires additional review.
                                        </div>
                                    ` : ''}
                                </div>
                            `,
                            showCancelButton: true,
                            confirmButtonText: 'Submit',
                            cancelButtonText: 'Cancel',
                            allowOutsideClick: false,
                            preConfirm: () => {
                                // Collect the form data entered by the user
                                const LotNumber = document.getElementById('lotNumber').value;
                                const SerialNumber = document.getElementById('serialNumber').value;
                                // const response = await $.ajax({
                                //     type: 'GET',
                                //     url: `${appbaseurl}/RemoveItem/isLotNumberSerialNumberExist`,
                                //     data: { RFID: RFID },
                                // });
                                // if (LotNumber === response.data.lotNumber) {
                                //     Swal.showValidationMessage('The Lot Number is the same as the fetched one. Please change it if necessary.');
                                //     return false; // Prevent form submission and return to the form
                                // }
                                if (response.data.isLotNoReq) {
                                    if(!LotNumber){
                                        Swal.showValidationMessage('Lot Number is required');
                                        return false; // Prevent form submission
                                    }
                                }
                                if (response.data.isSerialNoReq === true) {
                                    if (!SerialNumber) {
                                        Swal.showValidationMessage('Serial Number is required');
                                        return false; // Prevent form submission
                                    }
                                }
                                const CatalogNumber = document.getElementById('catalogNumber').textContent;
                                const BrandName = document.getElementById('brandName').textContent;
                                const Description = document.getElementById('description').textContent;
                                const Manufacturer = document.getElementById('manufacturer').textContent;
                                // const SerialNumber = document.getElementById('serialNumber').value;
                                // const LotNumber = document.getElementById('lotNumber').value;
                                const ExpiryDate = document.getElementById('expiryDate').value;

                                // Return the data to the Swal callback
                                return { RFID, CatalogNumber, BrandName, Description, Manufacturer, SerialNumber, LotNumber, ExpiryDate };
                            },
                            customClass: {
                                popup: 'custom-popup', // Add custom class to the popup
                                title: 'left-aligned-title',
                                actions: 'swal2-actions'
                            }
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Prepare the form data for submission
                                const formData = {
                                    RFID: result.value.RFID,
                                    catalogNumber: result.value.CatalogNumber,
                                    brandName: result.value.BrandName,
                                    description: result.value.Description,
                                    manufacturer: result.value.Manufacturer,
                                    serialNumber: result.value.SerialNumber,
                                    lotNumber: result.value.LotNumber,
                                    expiryDate: result.value.ExpiryDate
                                };
                                // Submit the form data via POST to save it
                                $.ajax({
                                    type: 'POST', // Use POST to submit the form data
                                    url: `${appbaseurl}/RemoveItem/SaveOneTimeUseitemDetails`,
                                    contentType: 'application/json',
                                    data: JSON.stringify(formData),
                                    success: function (saveResponse) {
                                        if (saveResponse.success) {
                                            Swal.fire({
                                                icon: 'success',
                                                title: 'Item details saved and removed successfully.',
                                                showConfirmButton: false,
                                                allowOutsideClick: false,
                                                timer: 2000
                                            });

                                            var RFID = formData.RFID;  // Assuming RFID is a field in formData
                                            var Qty = 1;    // Assuming Qty is a field in formData
                                            RemoveOneTimeItemDetails(RFID, Qty);
                                            GetPatientUsage();
                                        } @* else {
                                            Swal.fire({
                                                icon: 'error',
                                                title: 'Lot number already Exist, please scan again.',
                                                text: saveResponse.message,
                                                allowOutsideClick: false,
                                                showConfirmButton: true
                                            });
                                        } *@
                                        // GetPatientUsage();
                                    },
                                    error: function () {
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Error',
                                            text: 'There was an error saving the item details.',
                                            showConfirmButton: false,
                                            allowOutsideClick: false,
                                            timer: 3000
                                        });
                                    }
                                });
                            }
                        });
                        }
                        else {
                            $("#ManualInput").modal('hide');

                            // Determine ItemStatus message based on ItemStatusID
                            let ItemStatus = (ItemStatusID == "2" || ItemStatusID == "4")
                                ? "Item already removed against patient."
                                : "RFID not associated to any item.";

                            // If no item found or the above condition is true, show appropriate error message
                            if (ItemStatusID == "2" || ItemStatusID == "4" || !ItemStatusID) {
                                Swal.fire({
                                    position: 'center',
                                    icon: 'error',
                                    title: ItemStatus,
                                    showConfirmButton: false,
                                    timer: 5000,
                                    customClass: {
                                        popup: 'Remove-selection-class'
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    allowOutsideClick: false,
                                    text: 'No item found. Please scan again.'
                                });
                            }
                        }
                    }
                else if (response.success) {
                    console.log('Response data:', response.data);
                    $("#ManualInput").modal('hide');
                    var patientName = '@Model.Patientdetail.PatientName';

                    // Show the popup with item details using Swal
                    Swal.fire({
                        title: 'Please review and verify One Time Use data for <strong>' + patientName + '</strong>.',
                        html: `
                                <div class="item-info-container">
                                    <div class="item-info-row">
                                    <label for="catalogNumber">Catalog Number :</label>
                                    <span id="catalogNumber">${response.data.catalogNumber}</span>
                                </div>
                                <div class="item-info-row">
                                    <label for="brandName">Brand Name :</label>
                                    <span id="brandName">${response.data.brandName}</span>
                                </div>
                                <div class="item-info-row">
                                    <label for="description">Description :</label>
                                    <span id="description">${response.data.description}</span>
                                </div>
                                <div class="item-info-row">
                                    <label for="manufacturer">Manufacturer :</label>
                                    <span id="manufacturer">${response.data.manufacturer}</span>
                                </div>

                                <!-- Quantity Section -->
                                <div class="item-info-row d-flex align-items-center mb-3">
                                    <label for="quantity" class="mr-3 font-weight-bold">Quantity :</label>
                                    <div class="d-flex align-items-center">
                                        <button id="decrease" class="btn btn-outline-secondary btn-sm px-3 py-2" style="margin-left: -103px; margin-top: 5px;">-</button>
                                        <span id="quantity" class="quantity-value mx-2 px-3 py-2 border border-secondary rounded" style="margin-top: 5px;">${response.data.qty || 1}</span>
                                        <button id="increase" class="btn btn-outline-secondary btn-sm px-3 py-2" style="margin-top: 5px;">+</button>
                                    </div>
                                </div>

                            </div>
                            `,
                        showCancelButton: true,
                        confirmButtonText: 'Submit',
                        cancelButtonText: 'Cancel',
                        allowOutsideClick: false,
                        preConfirm: () => {
                            // Collect the form data entered by the user
                            const CatalogNumber = document.getElementById('catalogNumber').textContent;
                            const BrandName = document.getElementById('brandName').textContent;
                            const Description = document.getElementById('description').textContent;
                            const Manufacturer = document.getElementById('manufacturer').textContent;
                            const Qty = document.getElementById('quantity').textContent;

                            // Return the data to the Swal callback
                            return { RFID, CatalogNumber, BrandName, Description, Manufacturer, Qty };
                        },
                        customClass: {
                            popup: 'custom-popup', // Add custom class to the popup
                            title: 'left-aligned-title',
                            actions: 'swal2-actions'
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Prepare the form data for submission
                            const formData = {
                                RFID: result.value.RFID,
                                catalogNumber: result.value.CatalogNumber,
                                brandName: result.value.BrandName,
                                description: result.value.Description,
                                manufacturer: result.value.Manufacturer,
                                qty: result.value.Qty
                            };

                            // Submit the form data via POST to save it
                            $.ajax({
                                type: 'POST', // Use POST to submit the form data
                                url: `${appbaseurl}/RemoveItem/SaveOneTimeUseitemDetails`,
                                contentType: 'application/json',
                                data: JSON.stringify(formData),
                                success: function (saveResponse) {
                                    if (saveResponse.success) {
                                        Swal.fire({
                                            icon: 'success',
                                            title: 'Item details saved and removed successfully.',
                                            showConfirmButton: false,
                                            timer: 2000
                                        });
                                        var RFID = formData.RFID;  // Assuming RFID is a field in formData
                                        var Qty = formData.Qty;    // Assuming Qty is a field in formData
                                        RemoveOneTimeItemDetails(RFID, Qty);
                                        GetPatientUsage();
                                    } else {
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Error saving item details.',
                                            text: saveResponse.message,
                                            showConfirmButton: true
                                        });
                                    }
                                    GetPatientUsage();
                                },
                                error: function () {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Error',
                                        text: 'There was an error saving the item details.',
                                        showConfirmButton: false,
                                        timer: 3000
                                    });
                                }
                            });
                        }
                    });
                } else {
                    $("#ManualInput").modal('hide');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        allowOutsideClick: false,
                        text: 'No item found.please scan again.'
                    });
                }
            } catch (error) {
                $("#ManualInput").modal('hide');
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    allowOutsideClick: false,
                    text: 'There was an error fetching item details.'
                });
            }
            let quantityValue = parseInt(document.getElementById('quantity').textContent);

            // Increase quantity
            document.getElementById('increase').addEventListener('click', function() {
                quantityValue += 1;
                document.getElementById('quantity').textContent = quantityValue;
            });

            // Decrease quantity
            document.getElementById('decrease').addEventListener('click', function() {
                if (quantityValue > 1) {
                    quantityValue -= 1;
                    document.getElementById('quantity').textContent = quantityValue;
                }
            });
        }
    async function timeOfImplant(RFID) {
        try {
            // Perform the AJAX request to get item details based on the RFID
            const data = await $.ajax({
                type: 'get',
                url: `${appbaseurl}/RemoveItem/TimeOfImplant`,
                data: { RFID: RFID }
            });

            // Return the data to the caller
            return data;
        } catch (error) {
            console.error('Error occurred during the AJAX request:', error);
            throw new Error('Failed to fetch item details');
        }
    }
    let globalItem = null;
    async function checkIsFDAorNot(RFID,Qty) {
    try {
        //here i am checking is primary barcode details popup exist or not in screen
        if (Swal.isVisible()) {
            let alldata = globalItem;
            const serializedData = JSON.stringify(alldata);
                const response = await $.ajax({
                type: 'GET',
                url: `${appbaseurl}/RemoveItem/ProcessSecondoryBarcode`,
                data: { SecondoryBarcodeNo: RFID ,Qty: Qty, PrimaryItemData : serializedData}
            });
            if (!response.success) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Secondory Product Barcode Not Registered.',
                    allowOutsideClick: false
                });
                return;
            }else{
                const item = response.billOnlyItems[0];
                globalItem = item;
                var patientName = '@Model.Patientdetail.PatientName';
                const result = await displayProductDetailsPopup(patientName, item);
                if (result.isConfirmed) {
                    await handleSubmitAction(item, RFID, validateDate);
                }
            }
            return;
        }
        var ItemDetails;
        const response = await $.ajax({
            type: 'GET',
            url: `${appbaseurl}/RemoveItem/CheckItemsInMASystem`,
            data: { RFID: RFID ,Qty: Qty}
        });

        if (response.success && response.message == "This is RFID tracked item please scan RFID") {
            $("#ManualInput").modal('hide');
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    allowOutsideClick: false,
                    text: response.message
                });
            return true;
        }
        else if(response.success && response.message == "Product Barcode Found"){
            $("#ManualInput").modal('hide');
            if (response.billOnlyItems && response.billOnlyItems[0] && response.billOnlyItems[0].ProcessedBarcodeNo) {
                var pbarcodenumber = response.billOnlyItems[0].ProcessedBarcodeNo;
                var produnitid = response.billOnlyItems[0].ProdUnitID;
                var isimplant = response.billOnlyItems[0].isImplant;
                handleBarcodeDisplay(response, RFID, isimplant, produnitid, Qty);
            } else {
                DisplayDetailsScreenForImplantItems(response, RFID);
            }
            
        }
        else if (response.message == "Catalog Number Found"){
            var catNo = response.billOnlyItems[0].CatNo;
            handleCatalogSearch(catNo);
        }
        else if(response.success && response.message == "Product Not in system"){
            $("#ManualInput").modal('hide');
                const response = await $.ajax({
                type: 'GET',
                url: `${appbaseurl}/RemoveItem/FetchFDAItemDetails`,
                data: { barcodeNo: RFID}
            });
                await DisplayFDADetailsScreen(response,RFID);
        }
        else {
            $("#ManualInput").modal('hide');
                Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        allowOutsideClick: false,
                        text: 'Something went wrong, please try again.'
                    });
            return false;  // Return false if the condition is not met
        }
    } catch (error) {
        console.error("Error fetching item details:", error);
        return false;  // Return false in case of an error
    }
}

        async function DisplayFDADetailsScreen(response, RFID) {
        if (response.success) {
            $("#ManualInput").modal('hide');
            var patientName = '@Model.Patientdetail.PatientName';
                if (!response.itemDetails || !response.itemDetails[0].data) {
                // Show an error popup if data is missing
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Product Not Registered.',
                    allowOutsideClick: false
                });
                return;  // Stop further execution if data is missing
            }
            const item = response.itemDetails[0].data;

            // Open the Swal with the details
            const result = await Swal.fire({
            title: 'Please review and verify product details for <strong>' + patientName + '</strong>.',
                html: `
                    <div class="item-info-container">
                        <div class="item-info-row">
                            <label for="catalogNumber">Catalog Number :</label>
                            <span id="catalogNumber" style="color: red;">${item ? item.catalogNumber : ''}</span>
                        </div>
                        <div class="item-info-row">
                            <label for="brandName">Brand Name :</label>
                            <span id="brandName" style="color: red;">${item ? item.brandName : ''}</span>
                        </div>
                        <div class="item-info-row">
                            <label for="description">Description :</label>
                            <span id="description" style="color: red;">${item ? item.description : ''}</span>
                        </div>
                        <div class="item-info-row">
                            <label for="manufacturer">Manufacturer :</label>
                            <span id="manufacturer" style="color: red;">${item ? item.manufacturer : ''}</span>
                        </div>
                        <div class="item-info-row">
                            <label for="serialNumber" style="margin-top: 10px;">Serial Number 
                                : 
                                <span id="serialNumberMandatory" style="color:red; ${item.isSerialNoReq === 'true' ? '' : 'display:none;'}"> *</span>
                            </label>
                            <input type="text" id="serialNumber" class="swal2-input ${item.isSerialNoReq === 'true' ? 'mandatory-field' : ''}" value="${item ? item.serialNumber : ''}">
                        </div>
                        <div class="item-info-row">
                            <label for="lotNumber" style="margin-top: 10px;">Lot Number
                                : 
                                <span id="lotNumberMandatory" style="color:red; ${item.isLotNoReq === 'true' ? '' : 'display:none;'}"> *</span>
                            </label>
                            <input type="text" id="lotNumber" class="swal2-input ${item.isLotNoReq === 'true' ? 'mandatory-field' : ''}" value="${item ? item.lotNumber : ''}">
                        </div>
                        <div class="item-info-row">
                            <label for="expiryDate" style="margin-top: 10px;">
                                Expiry Date
                                <span id="expiryDateMandatory" style="color:red; ${item.isExpirationDateReq === 'true' ? '' : 'display:none;'}"> *</span>:
                            </label>
                            <input type="date" id="expiryDate" class="swal2-input" value="${item ? item.expiryDate : ''}">
                        </div>
                        <input type="hidden" id="isImplant" value="${item ? item.isImplant : ''}">
                        <!-- Warning message below inputs -->
                        <div style="color: green; margin-top: 10px;">
                            This item is from FDA and requires additional review.
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Submit',
                cancelButtonText: 'Cancel',
                allowOutsideClick: false,
                customClass: {
                    popup: 'custom-popup',
                    title: 'left-aligned-title',
                    actions: 'swal2-actions'
                },
                preConfirm: async () => {
                    const serialNumber = document.getElementById('serialNumber').value;
                    const lotNumber = document.getElementById('lotNumber').value;
                    const expiryDate = document.getElementById('expiryDate').value;
                    const isImplant = document.getElementById('isImplant').value;

                    let valid = true;

                    if (item.isLotNoReq === 'true' && !lotNumber) {
                        valid = false;
                        document.getElementById('lotNumber').classList.add('error-border');
                        Swal.showValidationMessage('Lot Number is required!');
                    } else {
                        document.getElementById('lotNumber').classList.remove('error-border');
                    }

                    if (item.isSerialNoReq === 'true' && !serialNumber) {
                        valid = false;
                        document.getElementById('serialNumber').classList.add('error-border');
                        Swal.showValidationMessage('Serial Number is required!');
                    } else {
                        document.getElementById('serialNumber').classList.remove('error-border');
                    }

                    // Return false if validation fails to prevent closing
                    if (!valid) {
                        return Promise.reject();  // Reject to keep the modal open
                    }

                    return true;  // Otherwise, proceed with the action
                }
            });

            // Proceed with the submit action only if form validation passes
            if (result.isConfirmed) {
                const serialNumber = document.getElementById('serialNumber').value;
                const lotNumber = document.getElementById('lotNumber').value;
                const expiryDate = document.getElementById('expiryDate').value;
                const isImplant = document.getElementById('isImplant').value;
                const description = document.getElementById('description').textContent;
                const catalogNumber = document.getElementById('catalogNumber').textContent;

                if (validateDate(expiryDate) || expiryDate == "") {
                        // Handle Implant case
                    if (isImplant == "Y") {
                        const verifyResult = await Swal.fire({
                            title: "Verify Package Integrity",
                            html: `Item Description: <div style='font-size:14px;'>${description}</div><br/> Expiry Date: ${expiryDate}`,
                            icon: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#3085d6",
                            cancelButtonColor: "#d33",
                            confirmButtonText: "Verify!",
                            allowOutsideClick: false
                        });

                        if (verifyResult.isConfirmed) {
                            var ItemDetails = item;
                            var Qty = '1';
                            const ss = await removeNonImplantFDABarcode(RFID, serialNumber, lotNumber, expiryDate, isImplant, item.isLotNoReq, item.isSerialNoReq,catalogNumber);
    @* removeBarCodeItems(ItemDetails, RFID, Qty); *@
                        }
                    } else {
                        // If `isImplant` is not "Y", directly call removeBarCodeItems
                        var ItemDetails = item;
                        var Qty = '1';
                        //reason why calling same mehod for imlant and not implant . handling in db sp
                        const ss = await removeNonImplantFDABarcode(RFID, serialNumber, lotNumber, expiryDate, isImplant, item.isLotNoReq, item.isSerialNoReq,catalogNumber);
                    }
                }
                else{
                    Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Item Has Expired.',
                    showConfirmButton: true,
                    allowOutsideClick: false
                });
                }
                
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Product Not Registered.',
                allowOutsideClick: false
            });
        }
    }

</script>
<script>
        async function removeNonImplantFDABarcode(RFID, serialNumber, lotNumber, expiryDate, isImplant, isLotNoReq, isSerialNoReq,catalogNumber) {
        const response = await $.ajax({
            type: 'GET',
            url: `${appbaseurl}/RemoveItem/ProcessNonImplantFDABarcode`,
            data: {
                Barcode: RFID,
                serialNumber: serialNumber,
                lotNumber: lotNumber,
                expiryDate: expiryDate,
                isImplant: isImplant,
                isLotNoReq: isLotNoReq,
                isSerialNoReq: isSerialNoReq,
                catalogNumber:catalogNumber
            }
        });

        if (response.success) {
            // Show a success Toast notification
            Toast.fire({
                icon: 'success',
                title: 'Item Removed Successfully.'
            });

            // Call GetPatientUsage() to refresh the patient data or related actions
            GetPatientUsage();
        }
        else if(response.message == "Item with this serial number is already used for this patient"){
            Swal.fire({
                        position: "center",
                        icon: "error",
                        title: response.message,
                        showConfirmButton: true
                    });
        }else {
            // Handle the case where the operation was not successful (you can customize this part)
            Toast.fire({
                icon: 'error',
                title: 'Failed to remove item. Please try again..'
            });
            GetPatientUsage();
        }
    }
</script>
<script>
    function handleSearchClick() {
        debugger;
        var errorMessage = document.getElementById("searchError");
        var catNumber = document.getElementById("searchBox").value;

        // Check if catNumber is empty or not provided
        if (!catNumber) {
                Swal.fire({
                position: 'center',
                icon: 'info',
                title: 'Please enter Catalog Number!',
                timer: 2000, 
                showConfirmButton: false,
                customClass: {
                    popup: 'Remove-selection-class'
                }
            });
        }
        
        $('#UsageDetailsGrid').hide();
        $.ajax({
        type: 'get',
        url: `${appbaseurl}/RemoveItem/SearchbyCatalogNo`,
        data: { catNumber: catNumber },
        success: function(response) {
            // On successful AJAX request, load the _ItemDetails partial view into the container
            $('#partialContainer').html(response);  // This replaces the content in the container with the loaded partial view
        },
        error: function(xhr, status, error) {
            console.error('An error occurred while fetching data: ', error);
        }
    });

    }
    function handleCatalogSearch(catNumber) {
        debugger;

        // Check if catNumber is empty or not provided
        if (!catNumber) {
                Swal.fire({
                position: 'center',
                icon: 'info',
                title: 'Please enter Catalog Number!',
                timer: 2000,
                showConfirmButton: false,
                customClass: {
                    popup: 'Remove-selection-class'
                }
            });
        }

        $('#UsageDetailsGrid').hide();
        $("#ManualInput").modal('hide');
        $("#ManualInputRFIDAndBarcode").modal('hide');
        $.ajax({
        type: 'get',
        url: `${appbaseurl}/RemoveItem/SearchbyCatalogNo`,
        data: { catNumber: catNumber },
        success: function(response) {
            // On successful AJAX request, load the _ItemDetails partial view into the container
            $('#partialContainer').html(response);  // This replaces the content in the container with the loaded partial view
        },
        error: function(xhr, status, error) {
            console.error('An error occurred while fetching data: ', error);
        }
    });

    }
</script>
<script>
    async function DisplayDetailsScreenForImplantItems(response, RFID) {
        if (response.success) {
            $("#ManualInput").modal('hide');
            var patientName = '@Model.Patientdetail.PatientName';
                if (!response.success) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Product Not Registered.',
                        allowOutsideClick: false
                    });
                return;
            }
            const item = response.billOnlyItems[0];
            globalItem = item;
            //display item details in popup screen
            const result = await displayProductDetailsPopup(patientName, item);
            //process barcode
            if (result.isConfirmed) {
                await handleSubmitAction(item, RFID, validateDate);
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Product Not Registered.',
                allowOutsideClick: false
            });
        }
    }

    function RemoveLocalBarcodeItems(response,Qty) {
            var pbarcodenumber = response.billOnlyItems[0].ProcessedBarcodeNo;
            var pcatNo = response.billOnlyItems[0].CatNo;
            var plotNo = response.billOnlyItems[0].LotNumber;
            var pserialNo = response.billOnlyItems[0].LotNumber;
            var productID = response.billOnlyItems[0].ProductID;

            const RFIDIttemStaus='@Model.OverrideNote';
            $.ajax({
                type: 'get',
                url: `${appbaseurl}/RemoveItem/ProcessBarcodeItems`,
                data: { ProcessedBarcode: pbarcodenumber, Qty: Qty, CatNo:pcatNo ,SerialNo:pserialNo, LotNo:plotNo, ProductID:productID}
            })
                .done(function (response) {
                    debugger;
                    $('#BarcodeCount').text("1");
                    count = 1;
                     $("#ManualInput").modal('hide');
                     $("#ManualInputRFIDAndBarcode").modal('hide');
                    // Check if the function exists before calling it
                    if (typeof resetTimer === 'function') {
                        resetTimer(); // Call the function
                    }
                    if (response == "Item already removed against patient.") {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: response,
                            showConfirmButton: false,
                            timer: 5000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        })
                    }
                    else if (response == "RFID not associated to any item.") {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: response,
                            showConfirmButton: false,
                            timer: 3000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        })
                    }
                    else if (response == "Success") {
                        $("#UsageGrid").data("kendoGrid").dataSource.read();
                        if (Status == "Success") {
                            Toast.fire({
                                icon: 'success',
                                title: 'Item Removed Successfully.'
                            })
                        }

                    }
                    else if (response == "other") {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                                title: 'Product is not registered.',
                            showConfirmButton: false,
                            timer: 3000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        })
                    }
                   else if (response[0].Status == "Fail") {
                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                                title: 'Product is not registered.',
                            showConfirmButton: false,
                            timer: 3000,
                            customClass: {
                                popup: 'Remove-selection-class'
                            }
                        })
                    }
                    else {
                         if(RFIDIttemStaus!=="" && RFIDIttemStaus!=="RemoveItem" && RFID.startsWith("E00")){
                              ItemDetails = response;

                                var divElement = $('#UsageDetailsGrid');
                                if (divElement.find('.k-grid').length > 0) {
                                    addRowToGrid(ItemDetails);
                                }else {
                                    $.ajax({
                                        type: 'get',
                                        url: `${appbaseurl}/RemoveItem/DisplayGrid`
                                    }).done(function (response) {
                                        $("#UsageDetailsGrid").html(response);
                                        addRowToGrid(ItemDetails);
                                    })
                                }
                         }else{
                                Toast.fire({
                                    icon: 'success',
                                    title: 'Item Removed Successfully.'
                                })

                            GetPatientUsage();
                         }
                    }
                })
        }
</script>
<script>
    // Reusable function for displaying the SweetAlert popup
async function displayProductDetailsPopup(patientName, item) {
    return await Swal.fire({
        title: 'Please review and verify product details for <strong>' + patientName + '</strong>.',
        html: `
            <div class="item-info-container">
                <div class="item-info-row">
                    <label for="catalogNumber">Catalog Number :</label>
                    <span id="catalogNumber" style="color: red;">${item ? item.CatNo : ''}</span>
                </div>
                    <div class="item-info-row">
                        <label for="brandName" style="margin-top: 15px;">Brand Name :</label>
                        <span id="brandName" style="color: red; margin-top: auto;">${item ? item.BrandName : ''}</span>
                    </div>
                    <div class="item-info-row" style="text-align: left;">
                        <label for="description">Description :</label>
                            <span id="description" style="color: red; width: 85%; margin-top: 13px; display: inline-block; word-wrap: break-word; text-align: left;">${item ? item.Description : ''}</span>
                    </div>
                <div class="item-info-row">
                    <label for="manufacturer">Manufacturer :</label>
                    <span id="manufacturer" style="color: red;">${item ? item.Manufacturer : ''}</span>
                </div>
                <div class="item-info-row">
                    <label for="serialNumber" style="margin-top: 10px;">Serial Number 
                        : 
                        <span id="serialNumberMandatory" style="color:red; ${item.isSerialNoReq === 'true' ? '' : 'display:none;'}"> *</span>
                    </label>
                    <input type="text" id="serialNumber" class="swal2-input ${item.isSerialNoReq === 'true' ? 'mandatory-field' : ''}" value="${item ? item.SerialNo : ''}" autocomplete="off">
                </div>
                <div class="item-info-row">
                    <label for="lotNumber" style="margin-top: 10px;">Lot Number
                        : 
                        <span id="lotNumberMandatory" style="color:red; ${item.isLotNoReq === 'true' ? '' : 'display:none;'}"> *</span>
                    </label>
                    <input type="text" id="lotNumber" class="swal2-input ${item.isLotNoReq === 'true' ? 'mandatory-field' : ''}" value="${item ? item.LotNo : ''}">
                </div>
                <div class="item-info-row">
                    <label for="expiryDate" style="margin-top: 10px;">
                        Expiry Date :
                        <span id="expiryDateMandatory" style="color:red; ${item.isExpirationDateReq === 'true' ? '' : 'display:none;'}"> *</span>
                    </label>
                    <input type="date" id="expiryDate" class="swal2-input" value="${item ? item.ExpiredDate : ''}">
                </div>
                <input type="hidden" id="isImplant" value="${item ? item.isImplant : ''}">
                <div style="color: green; margin-top: 10px;">
                    This item requires additional review.
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Submit',
        cancelButtonText: 'Cancel',
        allowOutsideClick: false,
        focusConfirm: false,
        customClass: {
            popup: 'custom-popup',
            title: 'left-aligned-title',
            actions: 'swal2-actions'
        },
        preConfirm: async () => {
            const serialNumber = document.getElementById('serialNumber').value;
            const lotNumber = document.getElementById('lotNumber').value;
            const expiryDate = document.getElementById('expiryDate').value;
            const isImplant = document.getElementById('isImplant').value;

            let valid = true;

            if (item.isLotNoReq === 'true' && !lotNumber) {
                valid = false;
                document.getElementById('lotNumber').classList.add('error-border');
                Swal.showValidationMessage('Lot Number is required!');
            } else {
                document.getElementById('lotNumber').classList.remove('error-border');
            }

            if (item.isSerialNoReq === 'true' && !serialNumber) {
                valid = false;
                document.getElementById('serialNumber').classList.add('error-border');
                Swal.showValidationMessage('Serial Number is required!');
            } else {
                document.getElementById('serialNumber').classList.remove('error-border');
            }

            if (!valid) {
                return Promise.reject(); // Prevent closing the modal
            }

            return true; // Proceed with the action
        }
    });
}

async function handleSubmitAction(item, RFID, validateDate) {
    const serialNumber = document.getElementById('serialNumber').value;
    const lotNumber = document.getElementById('lotNumber').value;
    const expiryDate = document.getElementById('expiryDate').value;
    const isImplant = document.getElementById('isImplant').value;
    const description = document.getElementById('description').textContent;
    const catalogNumber = document.getElementById('catalogNumber').textContent;

    if (validateDate(expiryDate) || expiryDate == "") {
        // Handle Implant case
        if (isImplant == "Y") {
            const verifyResult = await Swal.fire({
                title: "Verify Package Integrity",
                html: `Item Description: <div style='font-size:14px;'>${description}</div><br/> Expiry Date: ${expiryDate}`,
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Verify!",
                allowOutsideClick: false
            });

            if (verifyResult.isConfirmed) {
                // Pass relevant data to your removeNonImplantFDABarcode method
                await removeNonImplantFDABarcode(RFID, serialNumber, lotNumber, expiryDate, isImplant, item.isLotNoReq, item.isSerialNoReq, catalogNumber);
            }
        } else {
            // If `isImplant` is not "Y", directly call removeNonImplantFDABarcode
            await removeNonImplantFDABarcode(RFID, serialNumber, lotNumber, expiryDate, isImplant, item.isLotNoReq, item.isSerialNoReq, catalogNumber);
        }
    } else {
        // Handle expired item case
        Swal.fire({
            position: "center",
            icon: "error",
            title: "Item has expired.",
            showConfirmButton: true
        });
    }
}

function handleBarcodeDisplay(response, RFID, isImplant, prodUnitID, Qty) {
    const implantCondition = isImplant === "Y";
    const prodUnitCondition = (prodUnitID === "4" || prodUnitID === "1");

    if (implantCondition && prodUnitCondition) {
        // Display the details screen for implant items
        DisplayDetailsScreenForImplantItems(response, RFID);
    } else if (!implantCondition && prodUnitID === "1") {
        // Display the details screen for non-implant items with prodUnitID "1"
        DisplayDetailsScreenForImplantItems(response, RFID);
    } else {
        // Remove local barcode items
        RemoveLocalBarcodeItems(response, Qty);
    }
}
    function handleKeyPress(event) {
            if (event.key === "Enter") {
                event.preventDefault(); // Prevents form submission
                handleSearchClick(); // Call the search function
            }
        }
</script>