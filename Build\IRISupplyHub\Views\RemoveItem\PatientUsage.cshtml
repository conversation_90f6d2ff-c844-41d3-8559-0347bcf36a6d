﻿@using iRISupplyWebDeskManagePatients.Models
@model List<PatientUsageViewModel>
@using Kendo.Mvc.UI
@{
    Layout = null;
}
<style>

    .k-pdf-export .k-grid-toolbar,
    .k-pdf-export .k-grid-footer,
    .k-pdf-export .k-grouping-header,
    .k-pdf-export .k-pager-wrap,
    .k-pdf-export .k-grid-pager,
    .k-pdf-export .k-widget,
    .k-pdf-export .k-floatwrap {
        display: none;
    }

    .dot {
        height: 20px;
        width: 20px;
        background-color: #bbb;
        border-radius: 23%;
        padding: 10px;
        margin-left: 5px;
        margin-top: 2px;
    }

    .RB {
        margin: 0px 5px 0px 5px;
        line-height: 1;
    }

    .countlable {
        line-height: 1;
        margin-left: auto;
    }

    .recently-added {
        color: black !important; /* Black font color */
        font-weight: bold !important; /* Bold text */
    }
</style>

@(
Html.Kendo().Grid(Model)
   .Name("UsageGrid")
   .Columns(columns =>
   {
       columns.Bound(u => u.ProductType).Title("Prod Type").Width(100)
       .Filterable(f => f.Multi(true).Search(true));
       columns.Bound(u => u.ItemUsageID).Title("ItemUsageID").Width(120)
       .Filterable(f => f.Multi(true).Search(true)).Hidden(true);
       columns.Bound(u => u.CatNo).Title("Cat No").Width(120)
       .ClientTemplate(
         "<span class='serialNo'>#=CatNo#</span> " +
         "# if(CAPITATIVIEW != '') { #" +
         "<br><span class='badge badge-info mt-2'>#=CAPITATIVIEW#</span>" +
         "# } #"
     )
       .Filterable(f => f.Multi(true).Search(true));
       columns.Bound(u => u.Description).Title("Description").Width(350)
       .Filterable(f => f.Multi(true).Search(true));
       columns.Bound(u => u.ExpirationDate).Title("Expiration Date").Width(150)
      .Filterable(f => f.Multi(true).Search(true)).Hidden(true);
       columns.Bound(u => u.DateUsed).Title("Date Used").Width(120)
       .Filterable(f => f.Multi(true).Search(true));
       columns.Bound(u => u.RFID).Title("RFID/Barcode No").Width(150)
       .Filterable(f => f.Multi(true).Search(true)).Hidden(true);
       columns.Bound(u => u.EpicStatus).Title("EpicStatus").Width(90)
       .Filterable(f => f.Multi(true).Search(true))
       .ClientTemplate(
        "# if (EpicStatus != '') { #" +
        "<span class='badge badge-success mt-2'>#=EpicStatus#</span>" +
        "# } #"
    );
       columns.Bound("").Title("Serial / Lot No").Width(180)
         .ClientTemplate(
            "SerialNo: #=SerialNo#<br>LotNo: #=LotNo#"
         )
         .Filterable(f => f.Multi(true).Search(true));
       columns.Bound("").Title("Qty").Width(50)
       .ClientTemplate(
            "#= Math.floor(Qty) #"
         )
       .Filterable(f => f.Multi(true).Search(true))
       .HtmlAttributes(new { style = "text-align: center;" });
       columns.Bound("").Title("Options").Width(80).ClientTemplate("<a onclick='ShowTheOption(this)'  title='Click to Show the Options' class='fas fa-bars' style='font-size:26px;color:white;cursor:pointer;'></a>")
            .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
            .HtmlAttributes(new { @style = "text-align: center" });
   })

.Pageable(p =>
   {
       p.PageSizes(new[] { 5, 10, 20, 50, 75 });
   })
   .Selectable(selectable => selectable
           .Mode(GridSelectionMode.Multiple)
           .Type(GridSelectionType.Row))
   .Sortable()
   //.Groupable()
   .Scrollable(scr => scr.Height(300))
   .ToolBar(t =>
   {
       t.Search();
       t.Pdf();
       t.Custom().Text("Product Catalog Search").IconClass("fa fa-edit").HtmlAttributes(new { onclick = "EnterRFIDAndBarcode();" });
       @* t.Custom().ClientTemplate("<div class='inner-toolbar'><button class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' style='margin-right: 5px;'>RFID Item<span class='dot' style='background:rgb(31, 71, 136);'></span></button><button class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-base'>Barcode Item<span class='dot' style='background:rgb(47, 186, 65);'></span></button></div>"); *@
       t.Custom().ClientTemplate("<div class='countlable' style='display: flex; margin-left: auto;'><b id='counter'>Qty: " + "0" + "</b>&nbsp;&nbsp;</div>");
   })
   // .Events(e => e.Change("SelectRow"))
   .Filterable()
   .Events(events =>
    {
        events.DataBound("changeBgColor");
        events.Change("changeBgColor");
        events.Filter("changeBgColor");
        events.Sort("changeBgColor");
        events.Page("changeBgColor");
    })
   .ColumnMenu(true)
   .Resizable(resize => resize.Columns(true))
   //.Events(e => e.DataBound("SelectRow"))
   .DataSource(dataSource => dataSource
       .Ajax()
       .GroupPaging(true)
       .PageSize(100)
       //.Read(read => read.Action("LoadRemovedItems", "RemoveItem"))
       .ServerOperation(false)
)

)
<script>
     let ItemDetails;
     let RFID;
     let CAPITATE;

     changeRowBgColor();

     // $(".k-grid-toolbar").append('<div class="inner-toolbar" ></div>');
     // $(".inner-toolbar").append('<button class=" k-button k-button-md k-rounded-md k-button-solid k-button-solid-base" >' + ' RFID Item ' + '<span class="dot" style = "background:#1F4788;" >  </span></button> <p class="RB" >' + '</p > ');
     // $(".inner-toolbar").append('<button class=" k-button k-button-md k-rounded-md k-button-solid k-button-solid-base">' + ' Barcode Item ' + '<span class="dot" style = "background:#2fba41;" >  </span></button><p class="RB" >' + '</p > ');
     // $(".k-grid-toolbar").append('<p class="countlable" >' + '<b id="counter" >' + 'Qty: ' + GridQty() + '</b>' + '&nbsp;&nbsp;' + '</p>');

     function RemovePatientDetails() {
         RemoveItemsAgainstPatient(grid)
     }
      function SelectRow(e){
          $(e).closest('tr').css('background-color','rgb(197 204 216)');
     }
     function changeBgColor() {
              UpdateQuantity();
              var grid = $("#UsageGrid").data("kendoGrid");
              var dataSource = grid.dataSource;
              grid.tbody.find("tr").each(function () {
                  var dataItem = grid.dataItem(this);
                  var row = $(this);

                  // Check the value of the property you want to use for conditional formatting
                  var propertyValue = dataItem.RFID; // Change to your property name

                  // Define the condition and set the row's background color
                   if (!(propertyValue.startsWith('E00') || propertyValue.startsWith('MA'))) {
                      row.css("background-color", "#2fba41"); // Change to your desired background color
                      row.css("color", "white");
                      row.css("font-weight", 600);
                  }
                  else {
                      row.css("background-color", "#1F4788");
                      row.css("color", "white");
                      row.css("font-weight", 600);
                  }
              });
          }
     function changeRowBgColor() {

             changeBgColor();
        }


     function ShowTheOption(e){


         var grid = $("#UsageGrid").data("kendoGrid");
         ItemDetails = grid.dataItem(e.closest('tr'));
         var uid = ItemDetails.uid;
         const UsageID = ItemDetails.ItemUsageID;
         RFID = ItemDetails.RFID;
         var row = e.closest("tr");
         var badge = row.querySelector(".serialNo");
         var Parent = badge.parentElement;

         CAPITATE = Parent.children.length == 1 ? "CAPITATE" : "UNCAPITATE";
         Swal.fire({
             showDenyButton: true,
             confirmButtonText: "RETURN",
             denyButtonText: CAPITATE,
             denyButtonColor:"#0639c4",
             confirmButtonColor:"#0639c4",
             showCancelButton: true,
             allowOutsideClick: false
         }).then((result) => {

             if (result.isConfirmed) {
                 $.ajax({
                     type: 'get',
                     url: '@Url.Action("ReturnItemAgainstPatient", "RemoveItem")',
                     data: {
                         RFID: RFID, ItemUsageID : UsageID
                     }
                 })
                     .done(function (response) {
                         debugger;
                         if (response == "Error") {
                             Toast.fire({
                                 icon: 'error',
                                 title: 'Failed to return,try again.'
                             })

                         } else {
                             var dataRow = grid.dataSource.getByUid(uid);

                             var qty = parseInt(dataRow.Qty)
                             if (qty > 1) {
                                 $("#counter").text("Qty: "+(GridQty() - 1).toFixed(2));
                                 dataRow.set("Qty", (qty - 1).toFixed(2));
                                 changeRowBgColor();
                                 GetPatientUsage();
                                 Toast.fire({
                                     icon: 'success',
                                     title: 'Item Returned Successfully.'
                                 })
                             } else {
                                   $("#counter").text("Qty: "+(GridQty() - 1).toFixed(2));
                                  dataRow.set("Qty", (qty - 1).toFixed(2));
                                 grid.dataSource.remove(dataRow);
                                 changeRowBgColor();
                                 GetPatientUsage();
                                 Toast.fire({
                                     icon: 'success',
                                     title: 'Item Returned Successfully.'
                                 })
                             }
                         }
                     })
             } else if (result.isDenied) {
                 const CapitateValue = CAPITATE == "CAPITATE" ? 1 : 0;
                 $.ajax({
                     type: 'post',
                     url: '@Url.Action("CapitateItem", "RemoveItem")',
                     data: {
                         UsageID: UsageID,
                         CapitateValue: CapitateValue
                     }
                 })
                 .done(function (response) {

                     if (response === "ITEM UNCAPITATED SUCCESSFULLY") {
                             Parent.children[2].remove();
                             var brElements = Parent.querySelectorAll('br');
                             brElements.forEach(function (br) {
                                 br.remove();
                             });
                             Swal.fire(response, "", "info");

                     }
                     else {
                             var br = document.createElement("br");
                             var span = document.createElement("span");
                             span.classList="badge mt-2 badge-info";
                             span.textContent="CAPITATE";
                             Parent.append(br);
                             Parent.append(span);
                             Swal.fire(response, "", "info");

                     }


                 })

             }
         });
     }

    function GridQty(){
        debugger;
        var grid = $("#UsageGrid").data("kendoGrid");
        var data=grid.dataSource.view();
        var totalQty=0;
        for(var i=0;i<data.length;i++){
            totalQty += parseFloat(data[i].Qty)||0;
        }
         highlightRecentlyAddedRow(grid);
        return totalQty.toFixed(2);
    }
     function UpdateQuantity() {
        var totalQty = GridQty();
        $("#counter").text("Qty: " + totalQty);
     }
     function highlightRecentlyAddedRow(grid) {
         var topRow = grid.tbody.find(">tr:first");

         topRow.addClass("recently-added");

         setTimeout(function() {
             topRow.removeClass("recently-added");
         }, 3000);
     }
      localStorage.setItem("view", "partial");
</script>
<script type="text/javascript">
    $(document).ready(function() {
        // Set the placeholder for the search input
        var searchInput = $(".k-grid-search input");
        searchInput.attr("placeholder", "Filter Items...");
    });
</script>
