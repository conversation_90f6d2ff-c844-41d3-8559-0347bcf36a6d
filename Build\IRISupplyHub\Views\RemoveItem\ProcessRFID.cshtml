﻿@using iRISupplyWebDeskManagePatients.Models
@model List<ItemDetailsViewModel>
@using Kendo.Mvc.UI
@{
    Layout = null;
    string Status=@ViewBag.Status;
}
<style>
.Remove-selection-class{
        padding-bottom:50px;
    }

    .barcodeItem{
        background:#bebed8 !important;
    }

    .k-grid td, .k-grid .k-table-td {
        border: #edebeb !important;
        border-bottom: 0.5px solid !important;
    }

    .dot {
        height: 20px;
        width: 20px;
        background-color: #bbb;
        border-radius: 23%;
        padding: 10px;
        margin-left: 5px;
        margin-top: 2px;
    }

    .RB {
        margin: 0px 5px 0px 5px;
        line-height: 1;
    }

    .countlable {
        line-height: 1;
        margin-left: auto;
    }
</style>

@(
Html.Kendo().Grid(Model)
   .Name("UsageGrid")
   .Columns(columns =>
   {
       columns.Bound(u => u.RFID).Title("RFID/Barcode No").Width(150);

       columns.Bound(u=>u.Description).Title("Description").Width(300);
       columns.Bound(u=>u.CatNo).Title("Cat #").Width(120);
       
       columns.Bound(u=>u.ProductType).Title("Product Type").Width(120);
       columns.Bound(u=>u.SerialNo).Title("Serial #").Width(120);
       columns.Bound(u=>u.QTY).Title("Qty").Width(120);
       columns.Bound(u => u.DateUsed).Title("Date Used").Width(200);
     @* columns.Bound("").Title("Return Item").Width(120).ClientTemplate("<a href='' title='Click to return item' class='fas fa-edit' style='font-size:26px;color:blue;cursor:pointer;''></a>")
         .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: center" });*@
      @*columns.Command(command =>
        {
            command.Custom("Return").Template("<i class='fas fa-edit editbtn' title='Click to rerigester' role='button' onclick ='SelfRegister(this)'></i>");
        }).Title("Return").Width(80).Hidden(false).HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center"})
                .HtmlAttributes(new { @style = "text-align: center" });*@
   }
   )

.Pageable(p =>
   {
       p.PageSizes(new[] { 5, 10, 20 });
   })
   .Selectable(selectable => selectable
           .Mode(GridSelectionMode.Multiple)
           .Type(GridSelectionType.Row))
   .Sortable()
   .Groupable()
   .Scrollable(scr => scr.Height(300))
   .ToolBar(t =>
   {
       t.Search();
       t.Pdf();
       t.Custom().Text("Enter Barcode/RFID").IconClass("fa fa-edit").HtmlAttributes(new { onclick = "EnterRFIDAndBarcode();" });
   })
   .Filterable()
   .ColumnMenu(true)
   .Resizable(resize => resize.Columns(true))
   //.Events(e=>e.DataBound("onDataBound"))
   .DataSource(dataSource => dataSource
       .Ajax()
       .GroupPaging(true)
       .PageSize(10)
       //.Read(read => read.Action("LoadRemovedItems", "RemoveItem"))
       .ServerOperation(false)
)

)
<script>
    $(".k-grid-toolbar").append('<div class="inner-toolbar" ></div>');
    $(".inner-toolbar").append('<button class=" k-button k-button-md k-rounded-md k-button-solid k-button-solid-base" >' + ' RFID Item ' + '<span class="dot" style = "background:#1F4788;" >  </span></button> <p class="RB" >' + '</p > ');
    $(".inner-toolbar").append('<button class=" k-button k-button-md k-rounded-md k-button-solid k-button-solid-base">' + ' Barcode Item ' + '<span class="dot" style = "background:#2fba41;" >  </span></button><p class="RB" >' + '</p > ');
    $(".k-grid-toolbar").append('<p class="countlable">' + '<b id="counter" >' + 'Qty: ' + '<count id="removalcount"></count>' + '</b>' + '&nbsp;&nbsp;' + '</p>');
    
var Status='@Status';
if('@ViewBag.Status'=='Error'){
   
    showErrorAlert();
}
 

function showErrorAlert(){
    Swal.fire({
  icon: 'error',
  title: 'Error',
  text: 'Item already removed from the cabinet.'
})
}

    
</script>