﻿@using iRISupplyWebDeskManagePatients.Models
@model List<ProductDetailViewModel>
@using Kendo.Mvc.UI
@{
    Layout = null;
    string Status = @ViewBag.Status;
}
<style>
    .k-pdf-export .k-grid-toolbar, 
    .k-pdf-export .k-grid-footer,
    .k-pdf-export .k-grouping-header, 
    .k-pdf-export .k-pager-wrap, 
    .k-pdf-export .k-grid-pager,
    .k-pdf-export .k-widget,
    .k-pdf-export .k-floatwrap {
    display: none;
    }
    /* Remove selection class styling */
    .Remove-selection-class {
    padding-bottom: 50px;
    }

    .barcodeItem {
    background: #bebed8 !important;
    }

    .k-grid td, .k-grid .k-table-td {
    border-right: 1px solid #edebeb !important; /* Add vertical line */
    border-bottom: 0.5px solid #edebeb !important; /* Keep horizontal lines for row separation */
    }

    /* Optional: Style the first column to avoid extra left-border */
    .k-grid td:first-child, .k-grid th:first-child {
    border-left: 1px solid #edebeb !important; /* Add left border to first column */
    }

    /* Make the dot consistent for any elements with the dot class */
    .dot {
    height: 20px;
    width: 20px;
    background-color: #bbb;
    border-radius: 50%;
    padding: 10px;
    margin-left: 5px;
    margin-top: 2px;
    }

    /* Simplified background color for all rows in the grid */
    .k-grid tr {
    background-color: #f4f4f4 !important; /* Light gray background for all rows */
    }

    /* Optional: Make the text lighter in terms of styling */
    .RB {
    margin: 0px 5px;
    line-height: 1;
    }

    .countlable {
    line-height: 1;
    margin-left: auto;
    }
</style>
<style>
    /* Style to increase the width of the modal */
    .modal-dialog {
    max-width: 700px; /* Adjusted the max width of the modal */
    }

    /* Align the labels and spans in the same line with equal spacing */
    .modal-body .mb-3 {
    display: flex;
    align-items: left;
    justify-content: space-between;
    margin-bottom: 5px; /* Reduced gap between rows */
    }

    /* Adjust space between the label and the value (in this case, span) */
    .modal-body .mb-3 label {
    flex: 1;
    text-align: left;
    margin-right: -50px; /* Reduced margin between label and value */
    }

    /* Align the span elements (values) to the left with consistent spacing */
    .modal-body .mb-3 span {
    flex: 2;
    text-align: left;
    margin-left: 0; /* No extra margin on the left */
    }

    /* Style for Quantity row with decrease/increase buttons */
    .item-info-row .d-flex {
    justify-content: flex-start;
    align-items: center; /* Ensures alignment of items vertically */
    gap: 5px; /* Reduced gap between quantity value and buttons */
    }

    /* Optional: Add padding for buttons to make them look consistent */
    #decrease, #increase {
    padding: 5px 10px; /* Adjust padding for buttons */
    margin: 0; /* Remove margin to reduce unnecessary space */
    }

    /* Ensure the Quantity value is nicely aligned */
    #quantity {
    display: inline-block;
    width: 50px; /* Ensures the quantity value has a fixed width */
    text-align: center;
    margin-top: 0; /* No extra margin on top */
    }

    /* Align buttons with quantity value */
    .item-info-row button {
    margin-left: 0; /* Removed margin between buttons */
    margin-right: 0; /* Removed margin between buttons */
    }

    /* Modal content width adjustment */
    .modal-content {
    width: 700px !important;
    }
    .modal-footer {
    display: flex !important;
    justify-content: center !important;
    gap: 10px !important; /* Optional: adds spacing between the buttons */
    }

    .k-searchbox-with-button {
        display: flex;
        align-items: center;
        background-color: #f1f1f1;
        border-radius: 20px;
        padding: 2px;
        position: relative;
    }

        .k-searchbox-with-button .k-input-inner {
            border: none;
            background-color: transparent;
            padding-left: 10px;
            flex-grow: 1;
            width:300px;
            font-style: italic;
        }

    .k-submit-button {
        background-color: #007bff; /* Change to your preferred color */
        color: white;
        border: none;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin-left: 5px;
        padding: 0;
    }

        .k-submit-button:hover {
            background-color: #0056b3; /* Change to a darker shade for hover */
        }

        .k-submit-button .k-icon {
            font-size: 18px;
        }
        .k-master-row{
            font-weight: bold;
        }

        .bold-label {
            font-weight: 900; /* Extra bold */
            font-size: 18px; /* Adjust as needed */
        }

        .left-aligned-title {
            padding-top: 60px; /* Adds 60px of padding at the top of the title */
        }
</style>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
@if (Model != null && Model.Any())
{
    @(
            Html.Kendo().Grid(Model)
                .Name("ItemDetailsGrid")
                .Columns(columns =>
                {
                    columns.Bound(u => u.CatNo)
                        .Title("Catalog No")
                        .Width(200)
                        .HeaderHtmlAttributes(new { style = "font-weight: bold; color: blue;" })
                        .Filterable(f => f.Multi(true).Search(true))
                        .Lockable(true)
                        .MinResizableWidth(100);

                    columns.Bound(u => u.Description)
                        .Title("Description")
                        .Width(450)
                        .HeaderHtmlAttributes(new { style = "font-weight: bold; color: blue;" })
                        .Filterable(f => f.Multi(true).Search(true));

                    columns.Bound(u => u.Location)
                        .Title("Location")
                        .Width(190)
                        .Hidden(true)
                        .HeaderHtmlAttributes(new { style = "font-weight: bold; color: blue;" })
                        .Filterable(f => f.Multi(true).Search(true));

                    columns.Bound(u => u.Manufacturer)
                        .Title("Manufacturer")
                        .Width(230)
                        .HeaderHtmlAttributes(new { style = "font-weight: bold; color: blue;" })
                        .Filterable(f => f.Multi(true).Search(true));

                    columns.Bound(u => u.Qty)
                        .Title("On Hand Quantity")
                        .Width(200)
                        .Hidden(true)
                        .HeaderHtmlAttributes(new { style = "font-weight: bold; color: blue;" })
                        .Filterable(f => f.Multi(true).Search(true));

                    columns.Bound(u => u.BrandName)
                        .Title("BrandName")
                        .Width(60)
                        .HeaderHtmlAttributes(new { style = "font-weight: bold; color: blue;" })
                        .Filterable(f => f.Multi(true).Search(true))
                        .Hidden(true);

                    columns.Bound(u => u.BarCodeNo)
                        .Title("BarCodeNo")
                        .Width(60)
                        .HeaderHtmlAttributes(new { style = "font-weight: bold; color: blue;" })
                        .Filterable(f => f.Multi(true).Search(true))
                        .Hidden(true);

                    columns.Bound(u => u.ImplantStatus)
                        .Title("ImplantStatus")
                        .Width(60)
                        .HeaderHtmlAttributes(new { style = "font-weight: bold; color: blue;" })
                        .Filterable(f => f.Multi(true).Search(true))
                        .Hidden(true);

                    columns.Bound("")
                        .Title("Options")
                        .Width(120)
                            .ClientTemplate("<a onclick='ShowTheOption(this)' title='Click to Use Items' style='font-size:16px;color:blue;text-decoration:none;cursor:pointer;'><i class='fas fa-hand-pointer' style='margin-right:5px;'></i>Use</a>")
                        .HeaderHtmlAttributes(new { style = "font-weight: bold; color: blue;" })
                        .HtmlAttributes(new { @style = "text-align: center" });
                })
                .Pageable(p =>
                {
                    p.PageSizes(new[] { 5, 10, 20 });
                })
                .Selectable(selectable => selectable
                    .Mode(GridSelectionMode.Multiple)
                    .Type(GridSelectionType.Row))
                .Sortable()
                .Scrollable(scr => scr.Height(300))
                .ToolBar(t =>
                {
                    t.ClientTemplateId("GridToolbarTemplate");
                })
                .Filterable()
                .Events(events =>
                {
                    events.DataBound("changeBgColorforIDG");
                    events.Change("changeBgColorforIDG");
                    events.Filter("changeBgColorforIDG");
                    events.Sort("changeBgColorforIDG");
                    events.Page("changeBgColorforIDG");
                })
                .ColumnMenu(true)
                .Resizable(resize => resize.Columns(true))
                .DataSource(dataSource => dataSource
                    .Ajax()
                    .GroupPaging(true)
                    .PageSize(10)
                    .ServerOperation(false)
                )
        )

        <script id="GridToolbarTemplate" type="text/x-kendo-template">
            <div class="k-toolbar-search">
                <!-- Second Search Box with Submit Button -->
                <div class="k-searchbox-with-button">
                    <span class="k-input-icon k-icon k-i-edit"></span>
                        <input autocomplete="off" placeholder="Search items by Catalog Number..." title="Additional Input" class="k-input-inner" id="customSecondBox" onkeydown="handle_KeyPress(event)"/>
                    <button class="k-submit-button" id="searchButton" onclick="handleCatalogSearchClick();">
                        <span class="k-icon k-i-search"></span>
                    </button>
                </div>
            </div>
        </script>

}
else
{
        <script>
            // Display SweetAlert warning if no data is found
            Swal.fire({
                position: 'center',
                icon: 'warning',
                title: 'Only BARCODE products can be searched using catalog number search!',
                showConfirmButton: false,
                showConfirmButton: true,
                allowOutsideClick: false
            });
        </script>
    @(
        Html.Kendo().Grid(Model)
                   .Name("ItemDetailsGrid")
                   .Columns(columns =>
                   {
                       columns.Bound(u => u.CatNo).Title("Catalog No").Width(100)
                      .Filterable(f => f.Multi(true).Search(true));
                       columns.Bound(u => u.Description).Title("Description").Width(250)
                      .Filterable(f => f.Multi(true).Search(true));
                       columns.Bound(u => u.Location).Title("Location").Width(90)
                      .Filterable(f => f.Multi(true).Search(true));
                       columns.Bound(u => u.Manufacturer).Title("Manufacturer").Width(200)
                      .Filterable(f => f.Multi(true).Search(true));
                       columns.Bound(u => u.Qty).Title("Qty").Width(70)
                      .Filterable(f => f.Multi(true).Search(true));
                       columns.Bound(u => u.BrandName).Title("BrandName").Width(60)
                      .Filterable(f => f.Multi(true).Search(true)).Hidden(true);
                       columns.Bound(u => u.ProductID).Title("ProductID").Width(60)
                      .Filterable(f => f.Multi(true).Search(true)).Hidden(true);
                       columns.Bound(u => u.BarCodeNo).Title("BarCodeNo").Width(60)
                      .Filterable(f => f.Multi(true).Search(true)).Hidden(true);
                       columns.Bound(u => u.ImplantStatus).Title("ImplantStatus").Width(60)
                      .Filterable(f => f.Multi(true).Search(true)).Hidden(true);
                       columns.Bound("").Title("Options").Width(60).ClientTemplate("<a onclick='ShowTheOption(this)'  title='Click to Show the Options' class='fas fa-bars' style='font-size:26px;color:blue;cursor:pointer;'></a>")
          .HeaderHtmlAttributes(new { @style = "text-align: center; justify-content: center" })
          .HtmlAttributes(new { @style = "text-align: center" });
                   })

            .Pageable(p =>
               {
                   p.PageSizes(new[] { 5, 10, 20 });
               })
               .Selectable(selectable => selectable
                       .Mode(GridSelectionMode.Multiple)
                       .Type(GridSelectionType.Row))
               .Sortable()
               //.Groupable()
               .Scrollable(scr => scr.Height(300))
               .ToolBar(t =>
               {
                   @* t.Search(); *@
                   t.ClientTemplateId("GridToolbarTemplate");
                   @* t.Pdf(); *@
                   @* t.Custom().Text("Enter Barcode/RFID").IconClass("fa fa-edit").HtmlAttributes(new { onclick = "EnterRFIDAndBarcode();" }); *@
               })
           // .Events(e => e.Change("SelectRow"))
           .Filterable()
           .Events(events =>
            {
                events.DataBound("changeBgColorforIDG");
                events.Change("changeBgColorforIDG");
                events.Filter("changeBgColorforIDG");
                events.Sort("changeBgColorforIDG");
                events.Page("changeBgColorforIDG");
            })
           .ColumnMenu(true)
           .Resizable(resize => resize.Columns(true))
           //.Events(e => e.DataBound("SelectRow"))
           .DataSource(dataSource => dataSource
               .Ajax()
               .GroupPaging(true)
               .PageSize(10)
               //.Read(read => read.Action("LoadRemovedItems", "RemoveItem"))
               .ServerOperation(false)
        )

        )
        <script id="GridToolbarTemplate" type="text/x-kendo-template">
            <div class="k-toolbar-search">
                <!-- Second Search Box with Submit Button -->
                <div class="k-searchbox-with-button">
                    <span class="k-input-icon k-icon k-i-edit"></span>
                        <input autocomplete="off" placeholder="Search items by Catalog Number..." title="Additional Input" class="k-input-inner" id="customSecondBox" onkeydown="handle_KeyPress(event)"/>
                    <button class="k-submit-button" id="searchButton" onclick="handleCatalogSearchClick();">
                        <span class="k-icon k-i-search"></span>
                    </button>
                </div>
            </div>
        </script>
}
<script>
        function ShowTheOption(e) {
        debugger;
        var row = $(e).closest('tr');

        var grid = $("#ItemDetailsGrid").data("kendoGrid");
        var rowData = grid.dataItem(row);
     
        var catNo = rowData.CatNo;
        var description = rowData.Description;
        var manufacturer = rowData.Manufacturer;
        var location = rowData.Location;
        var brandName = rowData.BrandName;
        var barCodeNo = rowData.BarCodeNo;
        var productID = rowData.ProductID;
        @* var implantStatusvalue = rowData.ImplantStatus; *@
        var qty = rowData.Qty;  // Assuming this is part of your data as well


        // Show the popup with item details using Swal, replacing response.data with rowData
        Swal.fire({
            title: 'Please review and verify item details.',
            html: `
                <div class="item-info-container">
                    <div class="item-info-row">
                        <label for="catalogNumber">Catalog Number :</label>
                        <span id="catalogNumber" style="font-weight: bold; color: red;">${catNo}</span>
                        <span id="hiddenBarcodeNo" style="display:none">${barCodeNo}</span>
                        <span id="hiddenproductID" style="display:none">${productID}</span>
                    </div>
                    <div class="item-info-row" style="margin-top: 10px;">
                        <label for="manufacturer">Manufacturer :</label>
                        <span id="manufacturer" style="font-weight: bold; color: red;">${manufacturer}</span>
                    </div>
                       <div class="item-info-row" style="margin-top: 10px; display: flex; align-items: baseline;">
                            <label for="description" style="margin-right: 10px;">Description :</label>
                            <span id="description" style="color: red; word-wrap: break-word; text-align: left; width: 85%; font-weight: bold; display: inline-block; white-space: normal;">${description}</span>
                       </div>
                    <div class="item-info-row" style="margin-top: 10px;">
                        <label for="brandName">Brand Name :</label>
                        <span id="brandName" style="font-weight: bold; color: red;">${brandName}</span>
                    </div>

                    <!-- Quantity Section -->
                    <div class="item-info-row d-flex align-items-center mb-3" style="margin-top: 10px;">
                        <label for="quantity">Quantity :</label>
                        <div class="d-flex align-items-center">
                            <button id="decrease" class="btn btn-outline-secondary btn-sm px-3 py-2" style="margin-left: -100px; margin-top: 5px; color: red;"><strong>-</strong></button>
                            <span id="quantity" class="quantity-value mx-2 px-3 py-2 border border-secondary rounded" style="margin-top: 5px;"><strong>${1}</strong></span>
                            <button id="increase" class="btn btn-outline-secondary btn-sm px-3 py-2" style="margin-top: 5px; color: red;"><strong>+</strong></button>
                        </div>
                    </div>
                    @* <div class="item-info-row">
                        <label for="implantStatus">isImplant :</label>
                        <button id="implantToggle" class="btn btn-outline-primary btn-sm" style="margin-top: 05px; margin-left:-100px">${implantStatusvalue === "Y" ? "Implant" : "Not Implant"}</button>
                    </div> *@

                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Submit',
            cancelButtonText: 'Cancel',
            allowOutsideClick: false,
            preConfirm: () => {
                // Collect the form data entered by the user
                const CatNo = document.getElementById('catalogNumber').textContent;
                const BrandName = document.getElementById('brandName').textContent;
                const Description = document.getElementById('description').textContent;
                const Manufacturer = document.getElementById('manufacturer').textContent;
                const Qty = document.getElementById('quantity').textContent;
                @* const ImplantValue = document.getElementById('implantToggle').textContent; *@
                const HiddenBarcodeNo = document.getElementById('hiddenBarcodeNo').textContent;
                const HiddenProductID = document.getElementById('hiddenproductID').textContent;

                // Return the data to the Swal callback
                return { CatNo, BrandName, Description, Manufacturer, Qty, HiddenBarcodeNo, HiddenProductID };
                @* return { CatNo, BrandName, Description, Manufacturer, Qty, ImplantValue, HiddenBarcodeNo }; *@
            },
            customClass: {
                popup: 'custom-popup', // Add custom class to the popup
                title: 'left-aligned-title',
                actions: 'swal2-actions'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Prepare the form data for submission
                const formData = {
                    catNo: result.value.CatNo,
                    brandName: result.value.BrandName,
                    description: result.value.Description,
                    manufacturer: result.value.Manufacturer,
                    qty: result.value.Qty,
                    implantStatus: result.value.ImplantValue,
                    barCodeNo:result.value.HiddenBarcodeNo,
                    productID:result.value.HiddenProductID
                };

                // Submit the form data via POST to save it
                $.ajax({
                    type: 'POST', // Use POST to submit the form data
                    url: `${appbaseurl}/RemoveItem/RemoveProductBarcodes`,
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function (saveResponse) {
                        if (saveResponse.success) {
                            Toast.fire({
                                icon: 'success',
                                title: 'Item Removed Successfully.'
                            })
                            GetPatientUsage();
                            window.location.reload();
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error saving item details.',
                                text: saveResponse.message,
                                showConfirmButton: true
                            });
                        }
                        @* GetPatientUsage(); *@
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'There was an error saving the item details.',
                            showConfirmButton: false,
                            timer: 3000
                        });
                    }
                });
            }
        });
        let quantityValue = parseInt(document.getElementById('quantity').textContent);

            // Increase quantity
            document.getElementById('increase').addEventListener('click', function() {
                quantityValue += 1;
                document.getElementById('quantity').textContent = quantityValue;
            });

            // Decrease quantity
            document.getElementById('decrease').addEventListener('click', function() {
                if (quantityValue > 1) {
                    quantityValue -= 1;
                    document.getElementById('quantity').textContent = quantityValue;
                }
            });

        document.getElementById('implantToggle').addEventListener('click', function() {
        var button = this;

        // Toggle the button text between "Implant" and "Not Implant"
        if (button.innerText === 'Implant') {
            button.innerText = 'Not Implant';
            button.classList.remove('btn-outline-primary');
            button.classList.add('btn-outline-danger'); // Optional: You can change the button style if needed
        } else {
            button.innerText = 'Implant';
            button.classList.remove('btn-outline-danger');
            button.classList.add('btn-outline-primary'); // Optional: Revert button style if needed
        }
    });
    }
    function handleCatalogSearchClick() {
        debugger;
        var catNumber = document.getElementById("customSecondBox").value;
            $('#UsageDetailsGrid').hide();
            $.ajax({
        type: 'get',
        url: `${appbaseurl}/RemoveItem/SearchbyCatalogNo`,
        data: { catNumber: catNumber },
        success: function(response) {
            // On successful AJAX request, load the _ItemDetails partial view into the container
            $('#partialContainer').html(response);  // This replaces the content in the container with the loaded partial view
        },
        error: function(xhr, status, error) {
            console.error('An error occurred while fetching data: ', error);
        }
    });

    }

    function handle_KeyPress(event) {
        if (event.key === "Enter") {
            event.preventDefault(); // Prevents form submission
            handleCatalogSearchClick(); // Call the search function
        }
    }

</script>
