﻿@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor

@using Kendo.Mvc.UI
@{
    ViewBag.Title = "Return Item";
    ViewBag.pTitle = "Please scan the RFID tag to return item";
    ViewBag.pageTitle = "Return Item";
    Layout = "~/Views/_Shared/_Layout.cshtml";
    //Layout = null;
    var session = HttpContextAccessor.HttpContext.Session;
    var appurl = session.GetString("BaseUrl");
    
}
<style>
	.k-pdf-export .k-grid-toolbar, .k-pdf-export .k-grid-footer,
	.k-pdf-export .k-grouping-header {
		display: none;
	}
    #ReturnItemGridContainer{
        margin-left:10px;
        margin-right:10px;
    }

	.ManualEdit {
		text-align: end;
		color: black;
		padding: 20px;
		cursor: pointer;
	}

	#ManualInputRFIDAndBarcode {
		border: none;
		padding: 0px;
		background: transparent;
	}

	.modal-content {
		transform: translateY(100%);
		opacity: 0;
		transition: transform 0.5s ease-out, opacity 0.5s ease-out;
	}

	.modal.show .modal-content {
		transform: translateY(0);
		opacity: 1;
	}

	.modal.hide .modal-content {
		transform: translateY(100%);
		opacity: 0;
	}
</style>
<!-- JAVASCRIPT -->


    <div id="ReturnItemGridContainer">
    <div class="container">
        <div class="row">
            <div class="col-md-3"></div>
            <div class="col-md-6 text-center" mt style=" padding-top:20px">
             <img src="~/assets/images/RFIDTAG.png"  class="mx-auto" alt="Admin">
             </div>
            <div class="col-md-3 ManualEdit">
                        <button  class="text-dark fas fa-edit fa-2x" data-bs-toggle="modal" data-bs-target="#ManualInput" id="ManualInputRFIDAndBarcode"></button>
                        
            </div>
        </div>
    </div>
    @*<div class="text-center" mt style=" padding-top:20px">

        <img src="~/assets/images/NewRFID.png" class="mx-auto" alt="Admin" >
    </div>*@
    <div style="text-align: center;padding-top:5px" >
		<span><b class="text-dark" style="font-size:20px;">Scan the RFID or Barcode.</b></span>
    </div>
    </div>
<div class="modal fade" id="ManualInput" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title font-weight-bold" id="exampleModalLabel">Scan Barcode/RFID Item</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>

			<div class="modal-body">

				<div class="form-group">
					<label for="exampleInputEmail1">Enter RFID Tag/Barcode No:</label>
					<input type="text" class="form-control" id="tagno" required>

				</div>

			</div>
			<div class="modal-footer">
				<div style="margin-right: auto;">
					<label class="h2">Quantity :</label>
                    <i class="fas fa-minus-circle fa-2x" id="Minus" style="cursor: pointer;"></i>
                    <strong class="h2 font-weight-bold mx-2" id="BarcodeCount">1</strong>
                    <i class="fas fa-plus-circle fa-2x" id="Plus" style="cursor: pointer;"></i>
                </div>
				<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
				<button type="button" class="btn btn-primary" id="Enter">Enter</button>
			</div>

		</div>
	</div>
</div>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")
<script>
   
    var appbaseurl = '@appurl';
    let ipAddress = '@TempData["ReturnSessionID"]';
    let url = `${appbaseurl}/returnItemHub?parmSi=${ipAddress}`;
	let count = 1;
</script>
<script>
    function ReturnItemRFID(RFID,Qty) {
		debugger;
		$.ajax({
			type: 'get',
			url: `${appbaseurl}/ReturnItem/ProcessRFIDToReturn`,
			data: { RFID: RFID,Qty:Qty }
		})
			.done(function (response) {
				$('#BarcodeCount').text("1");
				 $("#ManualInput").modal('hide');
				count = 1;
				resetTimer();
				if (response === "Item already returned in cabinet") {
					Swal.fire({
						position: 'center',
						icon: 'error',
						title: "The item has already been returned to the inventory.",
						showConfirmButton: false,
						timer: 3000,
						customClass: {
							popup: 'Remove-selection-class'
						}
					});
				}
				if (response == "Item already stocked in cabinet.") {

					Swal.fire({
						position: 'center',
						icon: 'error',
						title: response,
						showConfirmButton: false,
						timer: 3000,
						customClass: {
							popup: 'Remove-selection-class'
						}
					})
				} else if (response == "Product is not registered") {
					Swal.fire({
						position: 'center',
						icon: 'error',
						title: response,
						showConfirmButton: false,
						timer: 3000,
						customClass: {
							popup: 'Remove-selection-class'
						}
					})
				}else if (response == "Item already returnred in cabinet") {
					Swal.fire({
						position: 'center',
						icon: 'error',
						title: "The item has already been returned to the inventory.",
						showConfirmButton: false,
						timer: 3000,
						customClass: {
							popup: 'Remove-selection-class'
						}
					})
				}else if (response == "" || response == null) {
					Swal.fire({
						position: 'center',
						icon: 'error',
						title: "The item has already been returned to the inventory.",
						showConfirmButton: true,
						customClass: {
							popup: 'Remove-selection-class'
						}
					})
				}
				else if (response == "RFID not associated to any item.") {
					Swal.fire({
						position: 'center',
						icon: 'error',
						title: response,
						showConfirmButton: true,
						customClass: {
							popup: 'Remove-selection-class'
						}
					})
				}
				else if (response == "Success") {
					$("#UsageGrid").data("kendoGrid").dataSource.read();
					if (Status == "Success") {
						Toast.fire({
							icon: 'success',
							title: 'Item Returned Successfully.'
						})
					}
				}
				else if (response == "other") {
					Swal.fire({
						position: 'center',
						icon: 'error',
						title: 'Something went wrong please try again.',
						showConfirmButton: true,
						customClass: {
							popup: 'Remove-selection-class'
						}
					})
				}
				else {
					$("#ReturnItemGridContainer").html(response);

					if (Status == "Success") {
						changeBgColor();
						Toast.fire({
							icon: 'success',
							title: 'Item Returned Successfully.'
						})
					}
				}


			})
    }
	function changeBgColor() {
		var grid = $("#ReturnItemGrid").data("kendoGrid");
		var dataSource = grid.dataSource;
		grid.tbody.find("tr").each(function () {
			var dataItem = grid.dataItem(this);
			var row = $(this);

			// Check the value of the property you want to use for conditional formatting
			var propertyValue = dataItem.RFID; // Change to your property name

			// Define the condition and set the row's background color
			if (!(propertyValue.startsWith('E00') || propertyValue.startsWith('MA'))) {
				row.css("background-color", "#2fba41"); // Change to your desired background color
				row.css("color", "white");
				row.css("font-weight", 600);
			}
			else {
				row.css("background-color", "#1F4788");
				row.css("color", "white");
				row.css("font-weight", 600);
			}
		});
	};
	$("#tagno").keydown(function () {
		$('#BarcodeCount').text("1");
	});
	$("#ManualInputRFIDAndBarcode").click(function () {
		$("#ManualInput").modal(focus);
	});
	function EnterRFIDAndBarcode(){
		$("#ManualInput").modal(focus);
	};
	$("#Enter").click(function () {
		const itemNo = $("#tagno").val();
		if (itemNo !== "") {
			const RFID = itemNo
			$("#tagno").val("");
			ReturnItemRFID(RFID, count);
		}
	});
	
	
	$(document).ready(function () {
		 // count of barcode increase and decrease
		
		$('#BarcodeCount').text(count);

		// Handle plus icon click
		$('#Plus').on('click', function () {
			const itemNo = $("#tagno").val();
			if (itemNo !== "" && itemNo.substring(0, 3).toLowerCase() !== "e00") {
				count += 1;
				$('#BarcodeCount').text(count);
			} else if (itemNo === "") {
				alert("Scan the items");
			}
			else {
				alert("You can not increase/decrease qty for RFID item");
			}

		});

		// Handle minus icon click
		$('#Minus').on('click', function () {
			const itemNo = $("#tagno").val();
			if (itemNo !== "" && itemNo.substring(0, 3).toLowerCase() !== "e00") {
				if (count > 1) {
					count -= 1;
					$('#BarcodeCount').text(count);
				}
			} else if (itemNo === "") {
				alert("Scan the items");
			} else {
				alert("You can not increase/decrease qty for RFID item!");
			}
		});
		var barcode = "";
		document.body.addEventListener("keypress", function (evt) {


			if (evt.key === 'Enter') {
				if (barcode !== null && barcode !== "") {
					var RFID = barcode;
					barcode = "";
					$("#tagno").val("");
					ReturnItemRFID(RFID,count);
				}

			}
			else {
				barcode += evt.key;
			}

		})

	})
</script>


