﻿@{
   // ViewData["Title"] = "Login";
    Layout = null;
    
}
@using iRISupplyWebDeskAuthLogin.Models;
@model LoginViewModel
    <div class="account-pages my-3 pt-sm-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6 col-xl-5">
                    <div class="card overflow-hidden">
                        <div class="bg-soft-primary">
                            <div class="row">
                                <div class="col-7">
                                    <div class="text-primary p-4">
                                    <h5 class="text-primary">@ViewBag.ProductName</h5>
                                        <p>Sign in</p>
                                    </div>
                                </div>
                                @*<div class="col-5 align-self-end">
                                    <img src="~/assets/images/profile-img.png" alt="" class="img-fluid">
                                    </div>*@
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div>
                                <a href="#">
                                    <div class="avatar-md profile-user-wid mb-4">
                                        <span class="avatar-title rounded-circle bg-light" style="display:contents">
                                            <img src="~/assets/images/maicon.png" alt="" class="rounded-circle" height="60">
                                        </span>
                                    </div>
                                </a>
                            </div>
                           
                            <div class="p-2">
                                <form class="form-horizontal" name="LoginForm" method="post" id="LoginOptionScreen"action="@Url.Action("AuthenticateUserNamePasswordLogin", "AuthLogin")">
                                    @Html.AntiForgeryToken()
                                    <div class="form-group">
                                        <label for="username">Username</label>
                                        <input type="text" class="form-control" id="username" name="UserName" asp-for="@Model.UserName" placeholder="Enter username" required>
                                        <div class="mt-1" style="color:red">
                                            <p id="usernameerror"></p>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="userpassword">Password</label>
                                        <input type="password" class="form-control" id="userpassword" name="Password" placeholder="Enter password" asp-for="@Model.Password" required>
                                        @*<input type="text" class="form-control" id="ClientDatetime" placeholder="Enter password" asp-for="@User.Claims" hidden>*@
                                        <div class="mt-1" style="color:red">
                                            <p id="passworderror"></p>
                                        </div>
                                    </div>

                                    <div class="mt-3" style="color:red">
                                       
                                        <p id="LoginErrorMessage">@ViewBag.LoginStatus</p>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button id="loginBtn" class="btn btn-primary btn-block waves-effect waves-light" type="submit">Log In</button>
                                    </div>
                                   <div class="mt-4 text-center">
                            <a href="/login_" class="link-primary">Login using Badge</a>
                            
                          </div>
                                </form>
                                @*<form action="\login" method="post" class="text-center">
                                <input type="hidden" name="type" value="1" />
                                <button class="btn btn-link">Login using badge</button>
                            </form>*@
                                <div class="mt-4 text-center">
                                    <a href=@Url.Action("Index", "AuthRecoverpw") class="text-muted"><i class="mdi mdi-lock mr-1"></i> Forgot your password?</a>
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="mt-4 text-center">

                        <div>
                            @* <p>Don't have an account ? <a href=@Url.Action("Index", "AuthRegister") class="font-weight-medium text-primary"> Signup now </a> </p>*@
                            <p>Powered by Mobile Aspects. All rights reserved.</p>
                            <p>Version @ViewBag.AppVersion</p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
