﻿@{
    // ViewData["Title"] = "Login";
    Layout = null;

}
<!--Jquery scripts -->
<script src="~/assets/libs/jquery/jquery.min.js"></script>
<!-- Signal R scripts For Login -->
    <script src="~/microsoft/signalr/dist/browser/signalr.js"></script>
    <script src="~/js/Login.js"></script>
    <script>
history.pushState({}, null, 'login')
</script>
<style>
    .loginimage{
        height: 238px;
        width: 232px;
        text-align: center !important;
        margin-left: 3px;
        border-color: blue;
        border: #4b4bd2 4px solid;
    }
</style>
<div class="account-pages my-3 pt-sm-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6 col-xl-5">
                <div class="card overflow-hidden">
                    <div class="bg-soft-primary">
                        <div class="row">
                            <div class="col-7">
                                <div class="text-primary p-4">
                                    <h5 class="text-primary">@ViewBag.ProductName</h5>
                                    <p>Sign in</p>
                                </div>
                            </div>
                            @*<div class="col-5 align-self-end">
                                <img src="~/assets/images/profile-img.png" alt="" class="img-fluid">
                                </div>*@
                        </div>
                    </div>
                    <div class="card-body pt-0">
                        <div>
                            <a href="#">
                                <div class="avatar-md profile-user-wid mb-4">
                                    <span class="avatar-title rounded-circle bg-light" style="display:contents">
                                        <img src="~/assets/images/maicon.png" alt="" class="rounded-circle" height="60">
                                    </span>
                                </div>
                            </a>
                        </div>

                        <div class=" text-center">
                            <h5 style="font-weight:bolder">Please scan your badge to login</h5>
                            <img src="~/assets/images/RFIDbadge.png" class="img-thumbnail loginimage" />
                               <br />
                               <br />
                   <span id="errorMessage" class="text-danger" style="font-weight:bolder"></span>
                          <div class="mt-4 text-center">
                            <a href="/login" class="link-primary">Login using username and password</a>
                            <form action="\login" method="post">
                                <input type="hidden" name="type" value="0" />
                                <button class="btn btn-link">Login using username and password</button>
                            </form>
                        </div>
                        </div>

                    </div>
                </div>

                <div class="mt-4 text-center">

                    <div>
                        @* <p>Don't have an account ? <a href=@Url.Action("Index", "AuthRegister") class="font-weight-medium text-primary"> Signup now </a> </p>*@
                        <p>Powered by Mobile Aspects. All rights reserved.</p>
                        <p>Version @ViewBag.AppVersion</p>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>


<!-- Button trigger modal -->

<!-- Enter Password Modal Modal -->


