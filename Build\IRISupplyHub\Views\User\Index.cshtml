﻿@using iRISupplyWebDeskUser.Models
@model UserViewModel
@using Kendo.Mvc.UI.Fluent

@{
    ViewData["Title"] = "Manage Profile";
    ViewBag.Title = "Manage Profile";
    ViewBag.pTitle = "Manage Profile";
    ViewBag.pageTitle = "Manage Profile";
    Layout = "~/Views/_Shared/_Layout.cshtml";
    //Layout = null;
}

<style>
    .required {
        color: red;
    }

    .user-img-span {
        background: #65c9f0;
        display: block;
        width: 145px;
        margin: 0 auto;
        text-align: center;
        height: 140px;
        line-height: 138px;
        border-radius: 50%;
        font-size: 40px;
        letter-spacing: 1px;
    }


    .btn-manage-profile {
        height: 40px;
        padding: 0 15px;
        font-size: 15px;
        margin: 0 15px;
    }

    .spn-manage-profile {
        margin: 0 10px;
        letter-spacing: 0.4px;
    }

    .k-i-collapse::before {
        content: "\25AC";
    }

    .k-i-expand::before {
        content: "\271A";
    }

    .k-icon {
        margin-right: 5px;
    }

    .k-group {
        display: inline-block;
    }
        /*
                    .k-group li {
                        float: right;
                    }*/

        .k-group .k-item {
            margin-right: 25px !important;
        }

    @@media screen and (max-width: 680px) {
        .treeview-flex {
            flex: auto !important;
            width: 100%;
        }
    }

    #demo-section-title h3 {
        margin-bottom: 2em;
        text-align: center;
    }

    .treeview-flex h4 {
        color: #656565;
        margin-bottom: 1em;
        text-align: center;
    }

    #demo-section-title {
        width: 100%;
        flex: auto;
    }

    .treeview-flex {
        flex: 1;
        -ms-flex: 1 0 auto;
    }

    .k-treeview {
        max-width: 240px;
        margin: 0 auto;
    }

    .alert {
        display: none;
    }

    #confirm_password, #password, #OldPassword {
        background: #2a3042;
        color: whitesmoke;
    }

</style>
@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

<!-- JAVASCRIPT -->


<div style="margin: 0px 0px 0px 0px;">
    <div class="form-group">
        <span class="user-img-span">
            @Model.Initials.ToUpper()
        </span>
    </div>
    <div class="row justify-content-center">
        <div class="col-md-6 col-sm-12">
            @{
                if (Model.Domain == "iRISupplyWebConsole")
                {
                        <div class="col-12">
                            <ul class="nav nav-pills mb-3" role="tablist">
                                <li class="nav-item col-6">
                                    <a class="nav-link active text-center btn btn-outline-primary" id="pills-Edit-tab" data-toggle="pill" href="#pills-Edit" role="tab" aria-controls="pills-Edit" aria-selected="true"><i class="fa fa-edit"></i><span class="spn-manage-profile">Profile</span></a>
                                </li>
                                <li class="nav-item col-6">
                                    <a class="nav-link text-center btn btn-outline-primary" id="pills-changePassword-tab" data-toggle="pill" href="#pills-changePassword" role="tab" aria-controls="pills-changePassword" aria-selected="false"><i class="fa fa-key"></i><span class="spn-manage-profile">Password</span></a>
                                </li>
                            </ul>
                        </div>
                }
            }
        </div>
    </div>

</div>

<div class="tab-content">
    <div class="tab-pane fade show active" id="pills-Edit" role="tabpanel" aria-labelledby="pills-Edit-tab">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <form class="needs-validation custom-validation" novalidate asp-action="UpdateUserDetail" asp-controller="User">
                            <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="pills-home-tab" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">Primary</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="pills-profile-tab" data-toggle="pill" href="#pills-profile" role="tab" aria-controls="pills-profile" aria-selected="false">Account</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="pills-office-tab" data-toggle="pill" href="#pills-office" role="tab" aria-controls="pills-office" aria-selected="false">Office</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="pills-address-tab" data-toggle="pill" href="#pills-address" role="tab" aria-controls="pills-address" aria-selected="false">Address</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="pills-contact-tab" data-toggle="pill" href="#pills-contact" role="tab" aria-controls="pills-contact" aria-selected="false">Contact</a>
                                </li>

                            </ul>
                            <div class="tab-content" id="pills-tabContent">
                                <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <input type="hidden" asp-for="@Model.UserID">
                                                <input type="hidden" asp-for="@Model.Domain">
                                                <label id="lblUserGroups" for="ddnUserGroups"> <span class="required">User Groups *</span></label>
                                                <select id="ddlUserGroups" class="custom-select" asp-for="@Model.UserGroupID" disabled>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label for="basicpill-Suffix-input">Suffix</label>
                                                <input type="text" class="form-control" id="basicpill-Suffix-input" asp-for="@Model.Suffix">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label for="FirstName"><span class="required">First name *</span></label>
                                                <input type="text" class="form-control" id="FirstName" name="FirstName" asp-for="@Model.FirstName" required>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label for="basicpill-middlename-input">Middle name</label>
                                                <input type="text" class="form-control" id="basicpill-middlename-input" asp-for="@Model.MiddleName">
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label for="basicpill-lastname-input"><span class="required">Last name *</span></label>
                                                <input type="text" class="form-control" id="basicpill-lastname-input" asp-for="@Model.LastName" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label for="basicpill-username-input"><span class="required">User name *</span></label>
                                                <input type="text" class="form-control" id="basicpill-usename-input" asp-for="@Model.UserName" required>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label for="emailid"><span class="required">Email *</span></label>
                                                <input type="email" class="form-control" id="emailid" name="emailid"
                                                       asp-for="@Model.EmailID" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row"><div class="col-sm-4" id="statusField"></div></div>
                                </div>
                                <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                                    <div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-title-input">Title</label>
                                                    <input type="text" class="form-control" id="basicpill-title-input" asp-for="@Model.Title">
                                                </div>
                                            </div>

                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-EmployeeID-input">Employee ID</label>
                                                    <input type="text" class="form-control" id="basicpill-EmployeeID-input" asp-for="@Model.EmployeeID">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            @*  <div class="col-lg-6">
                                            <div class="form-group">
                                            <label for="basicpill-pin-input">PIN</label>
                                            <input type="text" data-parsley-type="number"
                                            oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                            class="form-control" id="basicpill-pin-input" asp-for="@Model.Pin">
                                            </div>
                                            </div> *@

                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-rfid-input">RFID</label>
                                                    <input type="text" class="form-control" id="basicpill-rfid-input" asp-for="@Model.RFID" readonly="@ViewBag.isEditable">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-department-input">Department</label>
                                                    <input type="text" class="form-control" id="basicpill-department-input" asp-for="@Model.Department">
                                                </div>
                                            </div>

                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-designation-input">Designation</label>
                                                    <input type="text" class="form-control" id="basicpill-designation-input" asp-for="@Model.Designation">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="tab-pane fade" id="pills-office" role="tabpanel" aria-labelledby="pills-office-tab">
                                    <div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-Company-input">Company</label>
                                                    <input type="text" class="form-control" id="basicpill-Company-input" asp-for="@Model.Company">
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-Office-input">Office</label>
                                                    <input type="text" class="form-control" id="basicpill-Office-input" asp-for="@Model.Office">
                                                </div>
                                            </div>

                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-OfficeStreetAddress-input">Office Street Address</label>
                                                    <input type="text" class="form-control" id="basicpill-OfficeStreetAddress-input" asp-for="@Model.OfficeStreetAddress">
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-OfficeCity-input">Office City</label>
                                                    <input type="text" class="form-control" id="basicpill-OfficeCity-input" asp-for="@Model.OfficeCity">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-OfficeState-input">Office State</label>
                                                    <select id="ddlOfficeState" class="custom-select" asp-for="@Model.OfficeState"></select>
                                                </div>

                                            </div>
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-OfficeCountry-input">Office Country</label>
                                                    <select id="ddlOfficeCountry" class="custom-select" asp-for="@Model.OfficeCountry"></select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-OfficeZip-input">Office Zip</label>
                                                    <input type="text" class="form-control" id="basicpill-OfficeZip-input" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                           asp-for="@Model.OfficeZip">
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="pills-address" role="tabpanel" aria-labelledby="pills-address-tab">
                                    <div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-department-input">Home Street Address</label>
                                                    <input type="text" class="form-control" id="basicpill-HomeStreetAddress-input" asp-for="@Model.HomeStreetAddress">
                                                </div>
                                            </div>

                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-HomeCity-input">Home City</label>
                                                    <input type="text" class="form-control" id="basicpill-HomeCity-input" asp-for="@Model.HomeCity">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-HomeState-input">Home State</label>
                                                    <select id="ddlHomeState" class="custom-select" asp-for="@Model.HomeState"></select>
                                                </div>
                                            </div>

                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-HomeZip-input">Home Zip</label>
                                                    <input type="text" class="form-control" id="basicpill-HomeZip-input" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                           asp-for="@Model.HomeZip">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-expiration-input">Home Country</label>
                                                    <select id="ddlHomeCountry" class="custom-select" asp-for="@Model.HomeCountry"></select>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="pills-contact" role="tabpanel" aria-labelledby="pills-contact-tab">
                                    <div>
                                        @*<form>*@
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-HomePhoneNumber-input">Home Phone #</label>
                                                    <input type="text" class="form-control" id="basicpill-HomePhoneNumber-input" data-parsley-type="number"
                                                           oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                           asp-for="@Model.HomePhoneNumber">
                                                </div>
                                            </div>

                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-OfficePhoneNumber-input">Office Phone #</label>
                                                    <input type="text" class="form-control" id="basicpill-OfficePhoneNumber-input" data-parsley-type="number"
                                                           oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                           asp-for="@Model.OfficePhoneNumber">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-MobilePhoneNumber-input">Mobile Phone #</label>
                                                    <input type="text" class="form-control" id="basicpill-MobilePhoneNumber-input" data-parsley-type="number"
                                                           oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                           asp-for="@Model.MobilePhoneNumber">
                                                </div>
                                            </div>

                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-FaxNumber-input">Fax #</label>
                                                    <input type="text" class="form-control" id="basicpill-FaxNumber-input" data-parsley-type="number"
                                                           oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                           asp-for="@Model.FaxNumber">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label for="basicpill-PagerNumber-input">Pager #</label>
                                                    <input type="text" class="form-control" id="basicpill-PagerNumber-input" data-parsley-type="number"
                                                           oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                                                           asp-for="@Model.PagerNumber">
                                                </div>
                                            </div>

                                        </div>

                                    </div>

                                </div>
                            </div>
                            <div class="row input-field-row flex-row justify-content-end text-right">
                                <div>
                                    <button type="reset" class="btn btn-secondary waves-effect" id="cancel" onclick=" document.location.href = $('#cancelUrl').attr('href');">
                                        Cancel
                                    </button>
                                    <a id="cancelUrl" href="@Url.Action("Index", "Index")" style="display:none;"></a>
                                    <button type="submit" class="btn btn-primary waves-effect waves-light mr-1" id="submitUserBtn">
                                        Submit
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-pane fade" id="pills-changePassword" role="tabpanel" aria-labelledby="pills-changePassword-tab">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card shadow-lg">
                    <div class="card-body">
                        <h4 class="card-title mb-4">
                            Change Password
                            <button type="button" class="mdi mdi-information-outline" style="border: none;background: none;" data-toggle="popover" data-placement="top" title="Password rule" data-content="Password must contain minimum 8 characters,atleast one number,one special character,one uppercase and lowercase letters">
                                @*<i class="mdi mdi-information-outline" style="padding: 25px;" data-toggle="popover" data-placement="top" title="Password rule" data-content="Password must contain minimum 8 characters,atleast one number,one special character,one uppercase and lowercase letter"</i>*@
                            </button>
                        </h4>

                        <form class="needs-validation custom-validation" action="#" id="passwordForm">
                            <div class="form-group">
                                <div>
                                    <input type="password" id="OldPassword" class="form-control"
                                           name="OldPwd" placeholder="Old Password" asp-for="@Model.OldPwd" required />
                                </div>
                                <div class="mt-3">
                                    <input id="userid" asp-for="@Model.UserID" type="hidden" />
                                    <input id="username" asp-for="@Model.UserName" type="hidden" />
                                    <input type="password" id="password" class="form-control"
                                           name="Password" placeholder="New Password" asp-for="@Model.Pwd" required />
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped bg-danger progress-bar-animated" role="progressbar" style="width: 0%" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="invalid-feedback">
                                    Password must contain at least one number and one uppercase and lowercase letter, and at least 8 or more characters
                                </div>
                                <div class="mt-2">
                                    <input type="password" class="form-control" id="confirm_password"
                                           name="ConfirmPassword"
                                           placeholder="Confirm Password" asp-for="@Model.ConfirmPassword" required />

                                </div>
                                <br>
                                <span id="passwordError" style="color: red;"></span>
                                <span id="passwordSuccess" style="color: green;"></span>
                                <br>
                            </div>
                            <button type="reset" class="btn btn-secondary waves-effect" onclick=" document.location.href = $('#CancelChangePassword').attr('href');">
                                Cancel
                            </button>
                            <a id="CancelChangePassword" href="@Url.Action("Index", "User")" style="display:none;"></a>
                            <button type="submit" class="btn btn-primary" asp-controller="User" asp-action="UpdatePassword" id="ChangePasswordSubmit">Submit</button>

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $("#basicpill-pin-input").val("0");
    //Duplicate user name vliation start
    $("#basicpill-usename-input").keydown(function () {
        $("#submitUserBtn").attr('disabled', true);
        $("#statusField").hide();
    })

    $("#basicpill-usename-input").blur(function () {
        if (this.value == "") {
            $("#statusField").hide();
        } else {
            $("#statusField").show();
            $.ajax({
                type: 'get',
                url: '@Url.Action("UserNameValidation", "User")',
                data: { UserName: this.value, OldUserName: "@Model.UserName" }
            })
                .done(function (response) {

                    if (response == "Username already Exists") {
                        $("#submitUserBtn").attr('disabled', true);
                        $("#statusField").text(response).css({ 'color': 'red', 'margin-top': '-15px' });

                    } else if (response == "Same User") {
                        $("#submitUserBtn").attr('disabled', false);
                        $("#statusField").text("");
                    } else {
                        $("#submitUserBtn").attr('disabled', false);
                        $("#statusField").text("Available").css({ 'color': 'green', 'margin-top': '-15px' });
                    }

                })
        }
    })
    //Duplicate user name vliation end



    $(document).ready(
        function () {

            //
            $.ajax({

                type: "POST",
                url: '@Url.Action("GetUserGroups", "ManageUsers")',
                data: "",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (data) {
                    let output = data.map(x => "<option value='" + x.GroupID + "'>" + x.Group + "</option>");
                    //$("#ddlUserGroups").html('<option value="3" selected="selected">User</option>');
                    $("#ddlUserGroups").append(output.join(' '));
                    $("#ddlUserGroups").val("@Model.UserGroupID").attr("selected", "selected");

                }
            });

            $.ajax({

                type: "POST",
                url: '@Url.Action("GetStatesList", "ManageUsers")',
                data: "",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (data) {
                    let output = data.map(x => "<option value='" + x.StateName + "'>" + x.StateName + "</option>");
                    $("#ddlOfficeState").append(output.join(' '));
                    $("#ddlOfficeState").val("@Model.OfficeState").attr("selected", "selected");


                    $("#ddlHomeState").append(output.join(' '));
                    $("#ddlHomeState").val("@Model.HomeState").attr("selected", "selected");
                }
            });

            $.ajax({

                type: "POST",
                url: '@Url.Action("GetContryList", "ManageUsers")',
                data: "",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (data) {
                    let output = data.map(x => "<option value='" + x.CountryName + "'>" + x.CountryName + "</option>");
                    $("#ddlOfficeCountry").append(output.join(' '));
                    $("#ddlOfficeCountry").val("@Model.OfficeCountry").attr("selected", "selected");

                    $("#ddlHomeCountry").append(output.join(' '));
                    $("#ddlHomeCountry").val("@Model.HomeCountry").attr("selected", "selected");
                }
            });

        }

    );

</script>

<script>
    $(document).ready(function () {
        if ('@TempData["UserProfileUpdate"]' == "Profile updated successfully") {
            SuccessMessage("Your Profile has been Updated Successfully.");
        }
        else if ('@TempData["UserProfileUpdate"]' == "Failed to update profile") {
            FailedMessage("Failed to update your profile.");
        } else if ('@TempData["Password"]' == "Password has been updated Successfully") {
            SuccessMessage("Password has been updated Successfully");
        } else if ('@TempData["Password"]' == "Your old password does not match.Please try again") {
            FailedMessage("Your old password does not match.Please try again");
        } else if ('@TempData["Password"]' == "New password cannot be the same as the old password") {
            FailedMessage("New password cannot be the same as the old password");
        }
        else {
        }
        function SuccessMessage(text) {
            Toast.fire({
                icon: 'success',
                title: text
            })
        }

        function FailedMessage(text) {
            Toast.fire({
                icon: 'error',
                title: text
            })
        }

    });
    //password update

    function PasswordStrength(pwd, OldPwd) {

        var containsSpecialChar = /[!@@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(pwd);
        var containsUppercase = /[A-Z]+/.test(pwd);
        var containsLowercase = /[a-z]+/.test(pwd);
        var containsNumber = /[0-9]+/.test(pwd);
        var isLengthValid = pwd.length >= 8;
        var message = "Password must ";
        $('#passwordSuccess').text("")
        let count = 0

        if (!containsSpecialChar) {
            message += "contain at least one special character, ";

        }
        if (!containsUppercase) {
            message += "contain at least one uppercase letter, ";

        }
        if (!containsLowercase) {
            message += "contain at least one lowercase letter, ";

        }
        if (!containsNumber) {
            message += "contain at least one number, ";

        }
        if (!isLengthValid) {
            message += "be at least eight characters long, ";

        }
        if (pwd.length > 8) count += 10;
        if (containsSpecialChar) count += 20;
        if (containsNumber) count += 20;
        if (containsLowercase) count += 20;
        if (containsUppercase) count += 20;
        if (isLengthValid) count += 10;
        if (containsSpecialChar && containsUppercase && containsLowercase && containsNumber && isLengthValid) {
            if (pwd === OldPwd) {
                $('#passwordSuccess').text("");
                $('#passwordError').text("Error: New password cannot be the same as the old password.");
            } else if (pwd !== OldPwd) {
                $('#passwordSuccess').text("Password is valid.");
                $('#passwordError').text("");


            }


        } else {
            message = message.slice(0, -2) + ".";

        }
        if (message != "Password must ") $('#passwordError').text(message);
        $('.progress-bar').css('width', count + '%');
        if (count == 20 || count == 30) $('.progress-bar').text("Very Weak").removeClass("bg-warning bg-success bg-danger").addClass("bg-danger");
        else if (count == 40 || count == 50) $('.progress-bar').text("Weak").removeClass("bg-warning bg-success bg-danger").addClass("bg-danger");
        else if (count == 60 || count == 70) $('.progress-bar').text("Medium").removeClass("bg-warning bg-success bg-danger").addClass("bg-warning");
        else if (count == 80) $('.progress-bar').text("Medium").removeClass("bg-warning bg-success bg-danger").addClass("bg-warning");
        else if (count == 90 && count <= 90) $('.progress-bar').text("Good").removeClass("bg-warning bg-success bg-danger").addClass("bg-success");
        else if (count == 100) $('.progress-bar').text("Very Good").removeClass("bg-warning bg-success bg-danger").addClass("bg-success");
        else {
            $('.progress-bar').css('width', '0%').text("");

        }


    }
    $(document).ready(function () {
        $("#password").on('input', function () {
            $("#ChangePasswordSubmit").attr("disabled", true);
            pwd = $(this).val();
            Oldpwd = $("#OldPassword").val();

            PasswordStrength(pwd, Oldpwd);



        });
        $("#OldPassword").on('input', function () {
            var OldPwd = $(this).val();
            var pwd = $("#password").val();
            var cnfpwd = $("#confirm_password").val();

            $("#ChangePasswordSubmit").attr("disabled", true);
            if (pwd === OldPwd) {
                $('#passwordSuccess').text("");
                $('#passwordError').text("Error: New password cannot be the same as the old password.");
            } if (pwd !== OldPwd && cnfpwd === pwd) {
                $('#passwordError').text("");
                $("#ChangePasswordSubmit").attr("disabled", false);

            }
        });
        $("#confirm_password").on('input', function () {
            $("#ChangePasswordSubmit").attr("disabled", true);
            cnfpwd = $(this).val();
            pwd = $("#password").val();
            Oldpwd = $("#OldPassword").val();
            var progressBarText = $('.progress-bar').text().trim();
            if (progressBarText === 'Very Good' || progressBarText === 'Good') {
                if (cnfpwd === pwd && cnfpwd !== Oldpwd && pwd !== Oldpwd) {
                    $("#ChangePasswordSubmit").attr("disabled", false);
                }
            }
        })
    });
    function TriggerEmptyTab() {
            $("#pills-home-tab").addClass("active");
            $("#pills-profile-tab").removeClass("active");
            $("#pills-office-tab").removeClass("active");
            $("#pills-address-tab").removeClass("active");
            $("#pills-contact-tab").removeClass("active");

            $("#pills-home").addClass("active show");
            $("#pills-profile").removeClass("active show");
            $("#pills-office").removeClass("active show");
            $("#pills-address").removeClass("active show");
            $("#pills-contact").removeClass("active show");
        }
        $("#submitUserBtn").click(function () {

            const FirstName = document.getElementById("FirstName");
            const LastName = document.getElementById("basicpill-lastname-input");
            const UserName = document.getElementById("basicpill-usename-input");
            const emailid = document.getElementById("emailid");
            if (emailid.value.trim() === '') {
                TriggerEmptyTab();
            } else if (FirstName.value.trim() === '') {
                TriggerEmptyTab();
            } else if (LastName.value.trim() === '') {
                TriggerEmptyTab();
            } else if (UserName.value.trim() === '') {
                TriggerEmptyTab();
            } else {

            }
        });

</script>

