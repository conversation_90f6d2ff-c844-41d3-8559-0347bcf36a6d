{"version": 3, "sources": ["custom/fonts/_fonts.scss", "custom/structure/_topbar.scss", "app-rtl.css", "custom/structure/_page-head.scss", "custom/structure/_footer.scss", "custom/structure/_right-sidebar.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "custom/structure/_vertical.scss", "custom/structure/_horizontal-nav.scss", "custom/structure/_layouts.scss", "custom/components/_waves.scss", "custom/components/_avatar.scss", "custom/components/_accordion.scss", "custom/components/_helper.scss", "custom/components/_preloader.scss", "custom/components/_forms.scss", "custom/components/_widgets.scss", "custom/components/_demos.scss", "custom/components/_print.scss", "custom/plugins/_custom-scrollbar.scss", "custom/plugins/_calendar.scss", "custom/plugins/_dragula.scss", "custom/plugins/_session-timeout.scss", "custom/plugins/_range-slider.scss", "custom/plugins/_sweatalert2.scss", "custom/plugins/_rating.scss", "custom/plugins/_toastr.scss", "custom/plugins/_cropperjs.scss", "custom/plugins/_parsley.scss", "custom/plugins/_select2.scss", "custom/plugins/_switch.scss", "custom/plugins/_timepicker.scss", "custom/plugins/_datepicker.scss", "custom/plugins/_form-editors.scss", "custom/plugins/_form-upload.scss", "custom/plugins/_form-wizard.scss", "custom/plugins/_responsive-table.scss", "custom/plugins/_table-editable.scss", "custom/plugins/_apexcharts.scss", "custom/plugins/_echarts.scss", "custom/plugins/_flot.scss", "custom/plugins/_sparkline-chart.scss", "custom/plugins/_google-map.scss", "custom/plugins/_vector-maps.scss", "custom/plugins/_leaflet-maps.scss", "custom/pages/_authentication.scss", "custom/pages/_ecommerce.scss", "custom/pages/_email.scss", "custom/pages/_file-manager.scss", "custom/pages/_chat.scss", "custom/pages/_projects.scss", "custom/pages/_contacts.scss", "custom/pages/_crypto.scss", "custom/pages/_coming-soon.scss", "custom/pages/_timeline.scss", "custom/pages/_extras-pages.scss", "custom/rtl/_general-rtl.scss", "custom/rtl/_bootstrap-rtl.scss", "../../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "custom/rtl/_spacing-rtl.scss", "custom/rtl/_float-rtl.scss", "custom/rtl/_text-rtl.scss", "custom/rtl/_structure-rtl.scss", "custom/rtl/_plugins-rtl.scss", "custom/rtl/_components-rtl.scss", "custom/rtl/_pages-rtl.scss"], "names": [], "mappings": "AAIA,8FCAA,aACI,SAAA,MACA,IAAA,EACA,MAAA,EACA,KAAA,EACA,QAAA,KACA,iBAAA,KACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAGJ,eACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,QACA,iBAAA,QAAA,gBAAA,cACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,OAAA,EAAA,KACA,OAAA,KACA,QAAA,EAAA,eAAA,EAAA,EAPJ,2CAWY,iBAAA,QAKZ,kBACI,QAAA,EAAA,OACA,WAAA,OACA,MAAA,MAGJ,MACI,YAAA,KADJ,eAIQ,QAAA,KAIR,YACI,QAAA,KAKJ,YACI,QAAA,eAAA,EADJ,0BAIQ,OAAA,KACA,OAAA,KACA,aAAA,KACA,cAAA,KACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KACA,cAAA,KAVR,iBAaQ,SAAA,SACA,QAAA,GACA,UAAA,KACA,YAAA,KACA,KAAA,KACA,IAAA,EACA,MAAA,QAMR,kBAEQ,SAAA,SACA,QAAA,IAAA,EAHR,oBAKY,MAAA,QAKZ,yBACI,kBACI,MAAA,KAGJ,mBAGQ,QAAA,KAHR,mBAOQ,QAAA,cAKZ,cACI,QAAA,kBAAA,eAAA,KAAA,eAGJ,aACI,OAAA,KACA,mBAAA,eAAA,WAAA,eACA,MAAA,QACA,OAAA,EACA,cAAA,EALJ,mBAQQ,MAAA,QAIR,qBACI,OAAA,KACA,MAAA,KACA,iBAAA,QACA,QAAA,IAGJ,aAEQ,UAAA,KACA,MAAA,QAHR,kBAOQ,SAAA,SACA,IAAA,KACA,MAAA,IAIR,0BAEQ,QAAA,OAAA,KAFR,gCAKY,iBAAA,QAMZ,oBACI,QAAA,MACA,cAAA,IACA,YAAA,KACA,WAAA,OACA,QAAA,KAAA,EAAA,IACA,QAAA,MACA,OAAA,IAAA,MAAA,YACA,MAAA,QARJ,wBAWQ,OAAA,KAXR,yBAeQ,QAAA,MACA,SAAA,OACA,cAAA,SACA,YAAA,OAlBR,0BAsBQ,aAAA,QAKR,mEAGY,QAAA,QAKZ,oCAEQ,iBAAA,QAFR,kEAOgB,iBAAA,sBAPhB,kEAYY,WAAA,qBAZZ,oCAiBQ,MAAA,QAjBR,0CAoBY,MAAA,QApBZ,4CAyBQ,iBAAA,sBAzBR,oCA8BY,MAAA,QA9BZ,kCAmCQ,QAAA,KAnCR,mCAuCQ,QAAA,MAvCR,iDA6CY,iBAAA,sBACA,MAAA,KC7DZ,iFDeA,wCAkDY,MAAA,qBAKZ,0CAEQ,WAAA,QAFR,mCAMQ,QAAA,KANR,oCAUQ,QAAA,MAIR,yBACI,yBAEQ,SAAA,OAFR,wCAKY,KAAA,eACA,MAAA,gBAMhB,yBACI,kBACI,QAAA,MAIR,+CAEQ,MAAA,KAFR,2CAKQ,WAAA,KACA,QAAA,kBAAA,eAAA,KAAA,eAIR,yBACI,2CAEQ,WAAA,MEzRZ,gBACI,eAAA,KADJ,4BAIQ,iBAAA,YACA,QAAA,EALR,mBASQ,eAAA,UACA,YAAA,IACA,UAAA,eCXR,QACI,OAAA,EACA,QAAA,KAAA,eACA,SAAA,SACA,MAAA,EACA,MAAA,QACA,KAAA,MACA,OAAA,KACA,iBAAA,QAGJ,yBACI,QACI,KAAA,GAKR,2BAEQ,KAAA,KAIR,qCAEQ,KAAA,YC1BR,WACI,iBAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBACA,QAAA,MACA,SAAA,MACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,MACA,QAAA,KACA,MAAA,gBACA,MAAA,OACA,IAAA,EACA,OAAA,EAXJ,6BAcQ,iBAAA,QACA,OAAA,KACA,MAAA,KACA,YAAA,KACA,MAAA,QACA,WAAA,OACA,cAAA,IApBR,mCAuBY,iBAAA,QAMZ,kBACI,iBAAA,mBACA,SAAA,SACA,KAAA,EACA,MAAA,EACA,IAAA,EACA,OAAA,EACA,QAAA,KACA,QAAA,KACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SAGJ,8BAEQ,MAAA,EAFR,qCAKQ,QAAA,MCuBJ,4BDlBA,WACI,SAAA,KADJ,4BAGQ,OAAA,gBEtDZ,WACI,OAAA,EADJ,cAIQ,QAAA,MACA,MAAA,KALR,wBASQ,QAAA,KATR,sCAYY,QAAA,KAZZ,gCAgBY,QAAA,MAhBZ,0BAqBQ,SAAA,SACA,OAAA,EACA,SAAA,OACA,mCAAA,KAAA,2BAAA,KACA,4BAAA,KAAA,oBAAA,KACA,4BAAA,MAAA,CAAA,WAAA,oBAAA,MAAA,CAAA,WAKR,eACI,MAAA,MACA,QAAA,KACA,WAAA,KACA,OAAA,EACA,WAAA,EACA,SAAA,MACA,IAAA,KACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAGJ,cACI,YAAA,MACA,SAAA,OAFJ,uBAKQ,QAAA,EAAA,KAAA,KAAA,KACA,WAAA,KAKR,cACI,QAAA,KAAA,EAAA,KAAA,EADJ,0CAMgB,kBAAA,gBAAA,UAAA,gBANhB,+BAaY,QAAA,SACA,YAAA,wBACA,QAAA,MACA,MAAA,MACA,mBAAA,kBAAA,IAAA,WAAA,kBAAA,IAAA,WAAA,UAAA,IAAA,WAAA,UAAA,GAAA,CAAA,kBAAA,IACA,UAAA,KAlBZ,sBAyBgB,QAAA,MACA,QAAA,QAAA,OACA,MAAA,QACA,SAAA,SACA,UAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IA9BhB,wBAiCoB,QAAA,aACA,UAAA,QACA,eAAA,OACA,UAAA,QACA,YAAA,WACA,eAAA,OACA,MAAA,QACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAxCpB,4BA4CoB,MAAA,QA5CpB,8BA+CwB,MAAA,QA/CxB,2BAqDgB,WAAA,IArDhB,gCAyDgB,QAAA,EAzDhB,qCA8DwB,QAAA,MAAA,OAAA,MAAA,OACA,UAAA,KACA,MAAA,QAhExB,+CAoEwB,QAAA,EApExB,oDAwEgC,QAAA,MAAA,OAAA,MAAA,OACA,UAAA,OAWhC,YACI,QAAA,KAAA,eACA,eAAA,MACA,eAAA,KACA,OAAA,QACA,UAAA,KACA,eAAA,UACA,MAAA,QACA,YAAA,IAGJ,WACI,MAAA,kBADJ,aAGQ,MAAA,kBAHR,eAKY,MAAA,kBALZ,mBASQ,MAAA,kBATR,qBAYY,MAAA,kBAZZ,aAgBQ,MAAA,kBAIR,yBACI,eACI,QAAA,KAGJ,cACI,YAAA,YAGJ,mCAEQ,QAAA,OAMZ,iCAGQ,YAAA,KAHR,qCAOQ,MAAA,eAPR,sCAYY,QAAA,KAZZ,sCAgBY,QAAA,MAhBZ,kCAsBQ,SAAA,SACA,MAAA,eACA,QAAA,ELgNN,6DKxOF,kDA4BY,SAAA,kBA5BZ,uDAgCY,QAAA,eAhCZ,oDAoCY,OAAA,YL2MV,uDACA,6DKhPF,4DA6CgB,QAAA,eA7ChB,8DAiDgB,OAAA,kBAjDhB,iEAsDoB,QAAA,KAtDpB,sDA4DoB,SAAA,SACA,YAAA,OA7DpB,wDAgEwB,QAAA,KAAA,KACA,WAAA,KACA,mBAAA,KAAA,WAAA,KAlExB,+DAAA,8DAAA,8DAuE4B,MAAA,QAvE5B,0DA2E4B,UAAA,QACA,YAAA,IA5E5B,6DAgF4B,QAAA,KACA,aAAA,KAjF5B,8DAuF4B,SAAA,SACA,MAAA,mBACA,MAAA,QACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KA3F5B,gEA8FgC,MAAA,QA9FhC,mEAkGgC,QAAA,OAlGhC,+DAuG4B,QAAA,MACA,KAAA,KACA,SAAA,SACA,MAAA,MACA,OAAA,eACA,mBAAA,IAAA,IAAA,KAAA,EAAA,kBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,kBA5G5B,kEA+GgC,mBAAA,IAAA,IAAA,KAAA,EAAA,kBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,kBA/GhC,iEAmHgC,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,KACA,SAAA,SACA,MAAA,MACA,QAAA,EACA,MAAA,QAxHhC,uEA2HoC,MAAA,QA3HpC,sDAmIoB,QAAA,IAAA,EACA,QAAA,KACA,QAAA,KACA,iBAAA,KAtIpB,kEA2IgC,QAAA,MACA,KAAA,MACA,OAAA,eACA,WAAA,MACA,SAAA,SACA,MAAA,MAhJhC,2EAsJgC,SAAA,SACA,MAAA,KACA,IAAA,KACA,kBAAA,eAAA,UAAA,eAzJhC,kEAgK4B,MAAA,QAW5B,uCAEQ,WAAA,QAFR,8CAUoB,MAAA,QAVpB,gDAawB,MAAA,QAbxB,oDAiBwB,MAAA,KAjBxB,sDAoB4B,MAAA,KApB5B,6DA6B4B,MAAA,QA7B5B,mEAgCgC,MAAA,KAhChC,0CA0CQ,WAAA,OA1CR,qFAsDgC,WAAA,QACA,MAAA,KAvDhC,uFAyDoC,MAAA,KAzDpC,wFA+DoC,MAAA,QA/DpC,8FAiEwC,MAAA,KAjExC,6EAyEwB,iBAAA,QAzExB,+FAkF4B,MAAA,eAlF5B,iGAoFgC,MAAA,eApFhC,mCAgGQ,MAAA,eAhGR,qCAkGY,MAAA,eAlGZ,uCAoGgB,MAAA,eApGhB,qCAwGY,MAAA,eAxGZ,2CA2GY,MAAA,eA3GZ,6CA8GgB,MAAA,eA9GhB,oCAoHQ,MAAA,QAKR,2CAEQ,YAAA,YAMR,gDAEQ,MAAA,MAFR,6CAKQ,MAAA,MACA,WAAA,OLsEN,oDK5EF,8DAUY,QAAA,eAVZ,4CAcQ,YAAA,MAdR,sCAiBQ,KAAA,MAjBR,6DAuBgB,iBAAA,QAvBhB,sDA2BoB,QAAA,MA3BpB,mEAiCwB,aAAA,OAjCxB,kFAqCwB,aAAA,OArCxB,8DA6CY,YAAA,KA7CZ,6EAiDgB,WAAA,KAjDhB,uFAsDgC,QAAA,aAtDhC,wDA8DY,KAAA,KAOZ,0CAEQ,iBAAA,QAFR,6CAKQ,iBAAA,QALR,wDAOY,QAAA,KAPZ,yDAUY,QAAA,MAVZ,sCAeQ,MAAA,eAfR,wCAiBY,MAAA,eAjBZ,0CAmBgB,MAAA,eAnBhB,8CAAA,wCAuBY,MAAA,eAvBZ,0DA+BoB,MAAA,qBA/BpB,iDAmCoB,MAAA,qBAnCpB,mDAqCwB,MAAA,qBArCxB,4EAyC0B,WAAA,qBAzC1B,uDA8CwB,MAAA,KA9CxB,yDAiD4B,MAAA,KAjD5B,gEAyD4B,MAAA,qBAzD5B,sEA2DgC,MAAA,KA3DhC,wFA0E4B,iBAAA,QACA,MAAA,KA3E5B,0FA6EgC,MAAA,KA7EhC,kGAuFgC,MAAA,kBAvFhC,uGA+FwC,MAAA,QA/FxC,yGAmGoC,MAAA,kBAnGpC,2GAqGwC,MAAA,kBArGxC,6GAuG4C,MAAA,kBCjpB5C,QACI,WAAA,KACA,QAAA,EAAA,eACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,WAAA,KACA,SAAA,MACA,KAAA,EACA,MAAA,EACA,QAAA,IARJ,qBAWQ,OAAA,EACA,QAAA,EAZR,8BAkBY,UAAA,KACA,SAAA,SACA,QAAA,KAAA,OACA,MAAA,QArBZ,gCAuBgB,UAAA,KAvBhB,oCAAA,oCA0BgB,MAAA,QACA,iBAAA,YA3BhB,mCAgCY,MAAA,QAhCZ,0CAAA,yCAkCgB,MAAA,QAlChB,+CAwCgB,MAAA,QAxChB,uCA+CoB,MAAA,QACA,iBAAA,YFQhB,0BECA,8CNonBF,4CMjnBU,UAAA,KFJR,yBEUA,sDAKoB,aAAA,EALpB,uBAYQ,QAAA,MAAA,OACA,UAAA,MAbR,oDAoBgB,KAAA,EACA,MAAA,KArBhB,iCAyBY,WAAA,EACA,cAAA,EAAA,EAAA,OAAA,OA1BZ,oDA8BoB,MAAA,KACA,kBAAA,gBAAA,iBAAA,UAAA,gBAAA,iBACA,SAAA,SAhCpB,0DAsCoB,SAAA,SACA,IAAA,YACA,KAAA,KACA,QAAA,KAzCpB,uCAgDgB,QAAA,MAhDhB,sEAsDQ,QAAA,MAIR,eACI,QAAA,MAIR,YACI,QAAA,aADJ,kBAIQ,aAAA,QACA,aAAA,MACA,aAAA,EAAA,EAAA,IAAA,IACA,QAAA,GACA,OAAA,KACA,QAAA,aACA,MAAA,IACA,IAAA,IACA,YAAA,KACA,kBAAA,eAAA,iBAAA,UAAA,eAAA,iBACA,yBAAA,IAAA,iBAAA,IACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,KF5EJ,6BEkFA,kEAMwB,MAAA,KACA,KAAA,MFzFxB,4BEoGA,6BAEQ,QAAA,MAFR,0CAIY,QAAA,MAJZ,8BASQ,QAAA,KAIR,QACI,WAAA,MACA,WAAA,KACA,QAAA,EAHJ,8BAMY,QAAA,OAAA,OANZ,iCAYY,iBAAA,YACA,OAAA,KACA,mBAAA,KAAA,WAAA,KACA,aAAA,KAfZ,uDAiBgB,MAAA,KAjBhB,4DAoBoB,OAAA,EApBpB,iCA0BY,SAAA,SACA,iBAAA,YA3BZ,wCAAA,wCA+BgB,MAAA,QA/BhB,2BAsCY,MAAA,KACA,SAAA,UFrKZ,yBE8KA,6EAGY,QAAA,MAHZ,8EAOY,QAAA,KAPZ,wDAYQ,iBAAA,QAZR,8EAgBgB,MAAA,qBAhBhB,oFAAA,oFAmBoB,MAAA,qBAnBpB,uFA0BwB,MAAA,gCAW5B,+DAEQ,iBAAA,QACA,mBAAA,KAAA,WAAA,KAHR,6DAOQ,QAAA,KAPR,8DAWQ,QAAA,MAXR,4EAiBY,iBAAA,sBACA,MAAA,KNugBZ,4GMzhBA,mEAsBY,MAAA,qBAtBZ,+DA0BQ,MAAA,QA1BR,qEA6BY,MAAA,QA7BZ,6FAoCgB,iBAAA,qBApChB,6FAyCY,WAAA,qBAzCZ,+DA+CY,MAAA,QFlQR,yBEmNJ,0DAqDY,iBAAA,QArDZ,gFAyDoB,MAAA,qBAzDpB,sFAAA,sFA4DwB,MAAA,qBA5DxB,yFAmE4B,MAAA,gCC9U5B,6BACI,iBAAA,QADJ,6CAGQ,iBAAA,QACA,UAAA,OACA,OAAA,EAAA,KACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBANR,0CAUQ,UAAA,OACA,OAAA,EAAA,KAXR,qCAeQ,OAAA,EAAA,KACA,UAAA,qBAhBR,uDAqBY,UAAA,oBAQZ,qEAAA,kEAAA,6DAEQ,UAAA,KAFR,sEAAA,oEAKQ,UAAA,OAOJ,yBADJ,+CAAA,iDAGY,SAAA,UAKJ,yBARR,uEAAA,kEAUgB,SAAA,UCrDhB;;;;;;AAOC,cACG,SAAA,SACA,OAAA,QACA,QAAA,aACA,SAAA,OACA,oBAAA,KACA,iBAAA,KACA,gBAAA,KACA,YAAA,KACA,4BAAA,YAEF,4BACE,SAAA,SACA,cAAA,IACA,MAAA,MACA,OAAA,MACA,WAAA,MACA,YAAA,MACA,QAAA,EACA,WAAA,eAIA,WAAA,mHACA,mBAAA,IAAA,IAAA,SAGA,WAAA,IAAA,IAAA,SACA,4BAAA,iBAAA,CAAA,QAGA,4BAAA,OAAA,CAAA,kBAAA,oBAAA,OAAA,CAAA,kBAAA,oBAAA,SAAA,CAAA,QAAA,oBAAA,SAAA,CAAA,OAAA,CAAA,kBACA,kBAAA,SAAA,eAIA,UAAA,SAAA,eACA,eAAA,KAEF,wCACE,WAAA,qBAIA,WAAA,2IAEF,0CACE,WAAA,eAEF,sDACE,WAAA,qBAEF,oBACE,mBAAA,eAGA,WAAA,eAEF,cRs1BF,cQp1BI,kBAAA,cAIA,UAAA,cACA,mBAAA,oDAEF,cRq1BF,oBAFA,oBACA,sBQh1BI,YAAA,OACA,eAAA,OACA,OAAA,QACA,OAAA,KACA,QAAA,EACA,MAAA,QACA,iBAAA,cACA,UAAA,IACA,YAAA,IACA,WAAA,OACA,gBAAA,KACA,QAAA,EAEF,cACE,QAAA,MAAA,MACA,cAAA,KAEF,oBACE,OAAA,EACA,QAAA,MAAA,MAEF,qBACE,cAAA,KACA,eAAA,OAEF,kCACE,QAAA,EAEF,yCACE,SAAA,SACA,IAAA,EACA,KAAA,EACA,QAAA,EAEF,cACE,WAAA,OACA,MAAA,MACA,OAAA,MACA,YAAA,MACA,cAAA,IAEF,aACE,mBAAA,KACA,mBAAA,EAAA,IAAA,MAAA,IAAA,gBACA,WAAA,EAAA,IAAA,MAAA,IAAA,gBACA,mBAAA,IAAA,IAGA,WAAA,IAAA,IAEF,oBACE,mBAAA,EAAA,IAAA,KAAA,IAAA,eACA,WAAA,EAAA,IAAA,KAAA,IAAA,eAEF,aACE,QAAA,MAGJ,wCAEQ,iBAAA,qBAIR,0CAEQ,iBAAA,oBAGR,0CAEQ,iBAAA,oBAGR,uCAEQ,iBAAA,oBAGR,0CAEQ,iBAAA,oBAGR,yCAEQ,iBAAA,qBCjKR,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,OACA,MAAA,OAGF,cACE,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,iBAAA,QACA,MAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,YAAA,IACA,OAAA,KACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,MAAA,KChCF,kCAEQ,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,IACA,iBAAA,QACA,QAAA,KAAA,KACA,MAAA,QACA,YAAA,IACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,iBAAA,QAAA,cAAA,QAAA,gBAAA,cATR,qEAaoB,QAAA,SAbpB,mDAmBY,QAAA,aACA,UAAA,KACA,OAAA,KACA,MAAA,KACA,YAAA,KACA,iBAAA,KACA,WAAA,OACA,cAAA,IA1BZ,uDAoCoB,QAAA,SApCpB,6BA4CQ,MAAA,QC7CR,cACE,UAAA,eAGF,cACE,UAAA,eAGF,cACE,UAAA,eAGF,cACE,UAAA,eAGF,cACE,UAAA,eAGF,cACE,UAAA,eAGF,cACE,UAAA,eAGF,cACE,UAAA,eAGF,cACE,UAAA,eAGF,cACE,UAAA,eAGF,cACE,UAAA,eAGF,cACE,UAAA,eAKF,oBACE,YAAA,IAGF,sBACE,YAAA,IAKF,kBACE,OAAA,KACA,MAAA,KACA,YAAA,iBACA,QAAA,MACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,MAAA,QACA,WAAA,OACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IATF,wBAWI,MAAA,QACA,iBAAA,QAKJ,MACE,UAAA,KAEF,MACE,UAAA,KAEF,MACE,UAAA,MAEF,MACE,UAAA,MAGF,MACE,UAAA,MC3FF,WACI,SAAA,MACA,IAAA,EACA,KAAA,EACA,MAAA,EACA,OAAA,EACA,iBAAA,KACA,QAAA,KAGJ,QACI,MAAA,KACA,OAAA,KACA,SAAA,SACA,KAAA,IACA,IAAA,IACA,OAAA,MAAA,EAAA,EAAA,MAGJ,eACI,OAAA,EAAA,KACA,MAAA,KACA,OAAA,KACA,SAAA,SACA,kBAAA,cAAA,KAAA,SAAA,OAAA,KAAA,UAAA,cAAA,KAAA,SAAA,OAAA,KAGJ,WACI,MAAA,KACA,OAAA,KACA,SAAA,SACA,KAAA,EACA,IAAA,EACA,kBAAA,UAAA,GAAA,SAAA,YAAA,KAAA,UAAA,UAAA,GAAA,SAAA,YAAA,KANJ,kBAQQ,QAAA,GACA,QAAA,MACA,MAAA,IACA,OAAA,IACA,iBAAA,QACA,cAAA,KACA,kBAAA,iBAAA,GAAA,SAAA,YAAA,KAAA,UAAA,iBAAA,GAAA,SAAA,YAAA,KAdR,wBAkBQ,wBAAA,MAAA,gBAAA,MAlBR,+BAoBY,wBAAA,MAAA,gBAAA,MApBZ,wBAwBQ,wBAAA,IAAA,gBAAA,IAxBR,+BA0BY,wBAAA,IAAA,gBAAA,IA1BZ,wBA8BQ,wBAAA,KAAA,gBAAA,KA9BR,+BAgCY,wBAAA,KAAA,gBAAA,KAhCZ,wBAoCQ,wBAAA,KAAA,gBAAA,KApCR,+BAsCY,wBAAA,KAAA,gBAAA,KAtCZ,wBA0CQ,wBAAA,KAAA,gBAAA,KA1CR,+BA4CY,wBAAA,KAAA,gBAAA,KA5CZ,wBAgDQ,wBAAA,KAAA,gBAAA,KAhDR,+BAkDY,wBAAA,KAAA,gBAAA,KAKZ,iCACI,KACI,kBAAA,eAAA,UAAA,gBAFR,yBACI,KACI,kBAAA,eAAA,UAAA,gBAIR,6BACI,KAAA,IACI,kBAAA,eAAA,UAAA,gBAFR,qBACI,KAAA,IACI,kBAAA,eAAA,UAAA,gBAIR,oCACI,IACI,kBAAA,UAAA,UAAA,UAEJ,GAAA,KACI,kBAAA,SAAA,UAAA,UALR,4BACI,IACI,kBAAA,UAAA,UAAA,UAEJ,GAAA,KACI,kBAAA,SAAA,UAAA,UChGR,kBACI,aAAA,EACA,QAAA,aACA,cAAA,QAHJ,oCAKM,MAAA,EACA,YAAA,EANN,oCAUM,QAAA,MAKJ,sBACE,aAAA,EACA,cAAA,OACA,QAAA,aAHF,4CAKI,QAAA,aALJ,kDAAA,mDAOM,KAAA,KACA,MAAA,QARN,4CAaI,KAAA,KAMN,uDAGI,aAAA,IAHJ,mFASQ,iBAAA,KACA,QAAA,SACA,YAAA,wBACA,UAAA,KACA,IAAA,KACA,KAAA,MAdR,oFAkBQ,iBAAA,sBAQR,gFAKQ,iBAAA,KACA,QAAA,SACA,YAAA,wBACA,UAAA,IACA,IAAA,IACA,KAAA,MAVR,iFAcQ,iBAAA,sBAWN,oFAAA,iFAGM,iBAAA,QACA,aAAA,QAKN,qGAIQ,MAAA,QAbR,sFAAA,mFAGM,iBAAA,QACA,aAAA,QAKN,uGAIQ,MAAA,QAbR,oFAAA,iFAGM,iBAAA,QACA,aAAA,QAKN,qGAIQ,MAAA,QAbR,iFAAA,8EAGM,iBAAA,QACA,aAAA,QAKN,kGAIQ,MAAA,QAbR,oFAAA,iFAGM,iBAAA,QACA,aAAA,QAKN,qGAIQ,MAAA,QAbR,mFAAA,gFAGM,iBAAA,QACA,aAAA,QAKN,oGAIQ,MAAA,QAbR,iFAAA,8EAGM,iBAAA,QACA,aAAA,QAKN,kGAIQ,MAAA,QAbR,kFAAA,+EAGM,iBAAA,QACA,aAAA,QAKN,mGAIQ,MAAA,QAbR,iFAAA,8EAGM,iBAAA,QACA,aAAA,QAKN,kGAIQ,MAAA,QAQV,iFAAA,8EAGM,MAAA,QAMN,sBACE,OAAA,QAKF,kBACE,aAAA,KADF,wCAGI,YAAA,KAHJ,+CAKM,MAAA,KACA,OAAA,KACA,cAAA,KACA,KAAA,MARN,8CAWM,MAAA,iBACA,OAAA,iBACA,KAAA,kBAbN,6EAiBI,kBAAA,oBAAA,UAAA,oBAKJ,kBACE,aAAA,QADF,wCAGI,YAAA,KAHJ,+CAKM,MAAA,KACA,OAAA,KACA,cAAA,KACA,KAAA,SARN,8CAWM,MAAA,iBACA,OAAA,iBACA,KAAA,qBACA,cAAA,IAdN,6EAkBI,kBAAA,mBAAA,UAAA,mBAKJ,8BACE,iBAAA,KC1KF,gCAEQ,SAAA,OACA,SAAA,SAHR,sCAAA,uCAKY,QAAA,GACA,SAAA,SACA,MAAA,IACA,OAAA,KACA,iBAAA,qBACA,KAAA,KACA,kBAAA,cAAA,UAAA,cACA,IAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAbZ,uCAiBY,KAAA,MACA,MAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAnBZ,6CA0BgB,KAAA,KCzBhB,cACI,YAAA,KACA,cAAA,MAFJ,mBAKQ,cAAA,KACA,YAAA,IAMR,gBACI,UAAA,OAKJ,kBACI,SAAA,SACA,IAAA,KACA,MAAA,KACA,OAAA,KACA,KAAA,KACA,QAAA,EACA,QAAA,MAKJ,mBACI,WAAA,OACA,MAAA,QAFJ,qBAKM,QAAA,MACA,UAAA,KACA,cAAA,KACA,MAAA,QACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IATN,6BAaM,WAAA,KAbN,qCAiBU,MAAA,QACA,kBAAA,WAAA,UAAA,WASV,gCAEQ,iBAAA,QACA,WAAA,KACA,UAAA,MACA,YAAA,IACA,QAAA,KAAA,KAOR,YACE,iBAAA,KACA,OAAA,IAAA,MAAA,QACA,cAAA,OACA,QAAA,KACA,SAAA,OACA,cAAA,SACA,YAAA,OAPF,kBAUI,OAAA,QAIJ,kBACE,QAAA,MAIF,kBACE,QAAA,KADF,sCAGI,aAAA,kBAIJ,wBAEM,WAAA,KAFN,+BAIU,MAAA,KACA,OAAA,KACA,YAAA,eACA,UAAA,eACA,cAAA,cACA,iBAAA,+BACA,MAAA,kBACA,OAAA,IAAA,cC3GV,ahBs+CE,QADA,eADA,gBADA,WgBl+CE,eAKI,QAAA,eAEJ,WhBi+CF,cAEA,cADA,WAEA,KgB/9CM,QAAA,EACA,OAAA,EAGJ,MACI,OAAA,GhBq5CR,iBiB36CE,SAAA,SACA,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OACA,cAAA,KAAA,UAAA,KACA,iBAAA,MAAA,cAAA,MAAA,gBAAA,WACA,mBAAA,MAAA,cAAA,WACA,kBAAA,MAAA,eAAA,MAAA,YAAA,WAGF,mBACE,SAAA,OACA,MAAA,QACA,OAAA,QACA,UAAA,QACA,WAAA,QAGF,gBACE,UAAA,QACA,SAAA,SACA,SAAA,OACA,QAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EACA,OAAA,EACA,MAAA,EACA,MAAA,eACA,OAAA,eACA,QAAA,EAGF,kBACE,UAAA,kBACA,mBAAA,kBAAA,WAAA,kBACA,OAAA,eACA,SAAA,SACA,IAAA,EACA,KAAA,YACA,OAAA,EACA,MAAA,YACA,QAAA,EACA,OAAA,EACA,2BAAA,MAGF,2BACE,UAAA,QACA,mBAAA,qBAAA,WAAA,qBACA,SAAA,SACA,QAAA,MACA,OAAA,KACA,MAAA,KACA,WAAA,QACA,SAAA,KACA,UAAA,KACA,WAAA,KACA,gBAAA,KACA,QAAA,YAGF,8CjBkgDA,6CiBhgDE,QAAA,KjBogDF,yBiBjgDA,0BAEE,QAAA,IACA,QAAA,MAGF,uBACE,WAAA,KACA,UAAA,KACA,MAAA,KACA,eAAA,KAGF,wCACE,mBAAA,kBAAA,WAAA,kBACA,OAAA,KACA,MAAA,KACA,UAAA,IACA,SAAA,SACA,MAAA,KACA,WAAA,IACA,SAAA,OACA,QAAA,GACA,QAAA,EACA,OAAA,EACA,eAAA,KACA,iBAAA,QAAA,kBAAA,QAAA,UAAA,QACA,kBAAA,EAAA,YAAA,EACA,wBAAA,EAAA,WAAA,EAGF,gCACE,mBAAA,QAAA,WAAA,QACA,QAAA,MACA,QAAA,EACA,SAAA,SACA,IAAA,EACA,KAAA,EACA,OAAA,MACA,MAAA,MACA,WAAA,IACA,UAAA,IACA,SAAA,OACA,eAAA,KACA,QAAA,GAGF,iBACE,QAAA,EACA,SAAA,SACA,MAAA,EACA,OAAA,EACA,eAAA,KACA,SAAA,OjBw6CF,uDiBp6CE,eAAA,KACA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KACA,oBAAA,KjBu6CF,qDiBn6CE,eAAA,IAGF,qBACE,SAAA,SACA,MAAA,IACA,MAAA,IACA,WAAA,KAGF,4BACE,SAAA,SACA,QAAA,GACA,WAAA,QACA,cAAA,IACA,KAAA,EACA,MAAA,EACA,QAAA,EACA,mBAAA,QAAA,IAAA,OAAA,WAAA,QAAA,IAAA,OAGF,8CAEE,QAAA,GACA,mBAAA,QAAA,GAAA,OAAA,WAAA,QAAA,GAAA,OAGF,oCACE,IAAA,EACA,MAAA,KAGF,gEACE,IAAA,IACA,OAAA,IAGF,sCACE,KAAA,EACA,OAAA,KAGF,kEACE,OAAA,KACA,KAAA,IACA,MAAA,IAGF,2DACE,MAAA,KACA,KAAA,EACA,IAAA,IACA,OAAA,IACA,WAAA,EACA,UAAA,KACA,MAAA,KjB+5CF,mEiB15CE,MAAA,KACA,KAAA,EAGF,yBACE,UAAA,IACA,SAAA,MACA,QAAA,EACA,WAAA,OACA,OAAA,MACA,MAAA,MACA,WAAA,OACA,WAAA,OAGF,0BACE,SAAA,MACA,KAAA,EACA,WAAA,OACA,WAAA,OACA,gBAAA,KAGF,eACE,OAAA,KC/MF,oBACE,QAAA,aACA,aAAA,IAGF,0BAAA,qCACE,iBAAA,eAGF,qClB2sDA,wBACA,4BACA,6BAGA,qCADA,qCADA,qCkBvsDE,aAAA,kBAGF,0CACE,WAAA,OACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,cAAA,IACA,iBAAA,KACA,MAAA,QACA,aAAA,QAGF,iCACE,MAAA,kBAGF,iCAAA,0CACE,YAAA,IClCF,UACE,OAAA,IAAA,MAAA,QAGF,YACI,OAAA,IAAA,OAAA,kBACA,iBAAA,kBCTJ,+BAEQ,QAAA,KAFR,0CAMQ,MAAA,QACA,YAAA,IAPR,qCAWQ,iBAAA,KACA,MAAA,QACA,mBAAA,KAAA,WAAA,KCZR,sBAAA,uBAAA,yBAAA,qBAEI,WAAA,kBACA,UAAA,KAHJ,8BAAA,gCAAA,4BAOM,iBAAA,QAPN,uBAYI,WAAA,QACA,aAAA,QAbJ,4BAgBI,UAAA,KACA,MAAA,QAjBJ,sBAAA,sBAoBI,MAAA,QACA,WAAA,QACA,UAAA,KAtBJ,yBA0BI,OAAA,IAAA,MAAA,QACA,MAAA,KACA,OAAA,KACA,IAAA,KACA,iBAAA,eC9BJ,8BAEI,UAAA,KACA,YAAA,IAIJ,aACE,UAAA,KAGF,2BAEI,aAAA,QACA,MAAA,QAHJ,sDAOM,iBAAA,QAPN,8CAWM,aAAA,oBAXN,0BAeI,aAAA,QACA,MAAA,QAIJ,oBAEI,mBAAA,KAAA,WAAA,KAIJ,2CAEI,WAAA,QAFJ,sEAIM,WAAA,QAJN,2FAAA,gGAMQ,WAAA,oBANR,gDAYI,WAAA,QAIJ,cACE,aAAA,QAAA,YAAA,QAAA,YCtDF,QACE,aAAA,KAGF,0BAAA,0BACE,UAAA,KAGF,0BACE,IAAA,ECLF,qBAEQ,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,QAAA,EAHR,2BAKY,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,QAAA,GANZ,6CAAA,0CAYU,UAAA,IACA,OAAA,IAAA,KAQN,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,iBACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,cACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,aACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,4BAOR,aACI,iBAAA,qBACA,OAAA,IAAA,MAAA,QAGJ,gBACI,QAAA,KACA,iBAAA,QACA,cAAA,EACA,OAAA,IAAA,MAAA,QC1CJ,iCAEM,MAAA,KACA,cAAA,MACA,aAAA,MACA,SAAA,OACA,iBAAA,QACA,WAAA,OACA,MAAA,KARN,qCAWU,UAAA,KAXV,gCAgBM,OAAA,KACA,MAAA,MAjBN,gCAoBM,OAAA,OACA,MAAA,KArBN,gCAwBM,OAAA,QACA,MAAA,KAzBN,gCA4BM,OAAA,SACA,aAAA,EACA,MAAA,KAIN,0CAAA,6CAGM,QAAA,MACA,OAAA,QAAA,QACA,QAAA,OAAA,OCxCN,OACE,MAAA,QAGF,eACE,aAAA,QAGF,qBACE,QAAA,KACA,OAAA,EACA,QAAA,EAHF,4BAKI,QAAA,MALJ,wBAQI,UAAA,KACA,WAAA,KACA,MAAA,QACA,WAAA,ICnBJ,8CAGI,iBAAA,KACA,OAAA,IAAA,MAAA,QACA,OAAA,KALJ,oDAOM,QAAA,EAPN,2EAWM,YAAA,KACA,aAAA,KACA,MAAA,QAbN,wEAiBM,OAAA,KACA,MAAA,KACA,MAAA,IAnBN,0EAsBQ,aAAA,QAAA,YAAA,YAAA,YACA,aAAA,IAAA,IAAA,EAAA,IAvBR,8EA4BM,MAAA,QAKN,gFAMQ,aAAA,YAAA,YAAA,QAAA,sBACA,aAAA,EAAA,IAAA,IAAA,cAMR,sDAEM,QAAA,KACA,iBAAA,KAHN,6EAKU,OAAA,IAAA,MAAA,QACA,iBAAA,KACA,MAAA,QACA,QAAA,EARV,iFAYM,iBAAA,QAZN,yEAeM,iBAAA,QACA,MAAA,QAhBN,+EAkBU,iBAAA,QACA,MAAA,KAKV,yBACE,QAAA,IAAA,KAGF,kBACE,OAAA,IAAA,MAAA,gBACA,iBAAA,KACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAGF,sBAEI,OAAA,IAAA,MAAA,QAIJ,gDAEI,WAAA,KACA,iBAAA,KACA,OAAA,IAAA,MAAA,kBAJJ,6EAOM,QAAA,IAAA,KAPN,uEAUM,OAAA,EACA,MAAA,QAXN,kGAaU,MAAA,QAbV,yFAaU,MAAA,QAbV,6FAaU,MAAA,QAbV,8FAaU,MAAA,QAbV,oFAaU,MAAA,QAbV,2EAiBM,iBAAA,QACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,QAAA,EAAA,IAKN,kFAGM,aAAA,QAHN,oDAQI,YAAA,IAMJ,mCACI,MAAA,KACA,MAAA,KACA,aAAA,KAHJ,uCAKI,MAAA,KACA,OAAA,KACA,cAAA,IAIJ,uCACE,WAAA,IAGF,kC3By5DA,uCACA,qC2Bv5DE,QAAA,aACA,UAAA,KACA,aAAA,IACA,MAAA,QANF,sC3Bg6DE,2CACA,yC2Bx5DE,aAAA,IATJ,uD3Bo6DI,4DACA,0D2Bx5DI,QAAA,QACA,YAAA,sBAMR,wE3Bs5DA,6EACA,2E2Bn5DE,MAAA,qBAIF,iCACE,SAAA,OAMF,UACE,aAAA,IACA,OAAA,KACA,MAAA,KClLF,cACE,QAAA,KADF,oBAGI,UAAA,IACA,YAAA,EACA,MAAA,KACA,OAAA,KACA,iBAAA,QACA,iBAAA,KACA,cAAA,KACA,QAAA,UACA,OAAA,QACA,QAAA,aACA,WAAA,OACA,SAAA,SACA,YAAA,IACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YAhBJ,2BAkBM,MAAA,QACA,QAAA,qBACA,QAAA,MACA,YAAA,QACA,YAAA,IACA,UAAA,KACA,YAAA,KACA,SAAA,SACA,MAAA,IACA,OAAA,IACA,IAAA,KACA,WAAA,OACA,UAAA,WACA,SAAA,OACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YAhCN,0BAoCM,QAAA,GACA,SAAA,SACA,KAAA,IACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KACA,cAAA,KACA,OAAA,KACA,MAAA,KACA,IAAA,IACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YA7CN,4BAkDI,iBAAA,QAIJ,4BACE,iBAAA,QADF,mCAGI,MAAA,KACA,QAAA,oBACA,MAAA,KACA,KAAA,IANJ,kCAUI,KAAA,KACA,iBAAA,QAIJ,yBACE,iBAAA,QAEF,gCAAA,wC5B6jEA,2C4B3jEE,MAAA,KAGF,iCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,iCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,mCACE,iBAAA,QAGF,iCACE,iBAAA,QAGF,eACE,aAAA,IADF,mCAAA,yCAGI,cAAA,IClHJ,wCAIQ,MAAA,QAJR,8CAMU,iBAAA,YACA,aAAA,YACA,cAAA,IACA,MAAA,QACA,gBAAA,KAVV,4CAcQ,MAAA,KACA,OAAA,KACA,OAAA,EACA,MAAA,QACA,OAAA,IAAA,MAAA,QACA,iBAAA,KAnBR,iDA0BI,oBAAA,QA1BJ,4DA6BI,iBAAA,QC3BJ,YACE,OAAA,IAAA,MAAA,QACA,QAAA,IACA,QAAA,cAHF,wBAOQ,YAAA,IAPR,yCAAA,+BAAA,8CAAA,qCAAA,iCAAA,0CAAA,gDAAA,uCAAA,8BAAA,uCAAA,6CAAA,oCAaU,iBAAA,kBACA,iBAAA,KACA,mBAAA,KAAA,WAAA,KACA,MAAA,e9B0rER,qCACA,mC8B3sEF,oCAAA,kCAuBY,WAAA,Q9BurEV,iCACA,iC8B/sEF,4BAAA,4BA8BY,MAAA,QACA,QAAA,GA/BZ,8BAAA,uCAAA,6CAAA,oCAmCY,iBAAA,QAOZ,6BAAA,6BAEI,QAAA,IAIJ,gDAEI,MAAA,eACA,QAAA,aAQJ,sBACE,OAAA,IAAA,MAAA,QACA,mBAAA,KAAA,WAAA,KACA,iBAAA,KAHF,wCAMI,MAAA,MAIJ,wBAIM,iBAAA,KACA,cAAA,IALN,+BAAA,qCAQQ,iBAAA,qBACA,MAAA,QATR,oCAAA,0CAAA,8BAaQ,iBAAA,QACA,MAAA,KAdR,8BAAA,oCAmBQ,MAAA,QACA,QAAA,GApBR,wCA4BQ,YAAA,IA5BR,wCAAA,8CAgCQ,iBAAA,KCpGR,aACE,OAAA,IAAA,MAAA,kBAGF,oBAEI,WAAA,IAAA,MAAA,kBAFJ,4BAAA,kBAAA,oBAKM,iBAAA,eACA,WAAA,cANN,eASM,MAAA,kBATN,2DAWY,iBAAA,kBAXZ,qBAgBY,iBAAA,kBAhBZ,kBAAA,4BAAA,2BAqBM,WAAA,kBArBN,2BAyBM,iBAAA,kBAzBN,eA6BM,MAAA,kBA7BN,mBA+BQ,KAAA,kBA/BR,4BAoCM,iBAAA,eApCN,sBAAA,+BAAA,+BAwCM,MAAA,kBAxCN,2DA4CM,aAAA,IAAA,MAAA,kBAMN,wBAEM,OAAA,IAAA,MAAA,QACA,mBAAA,KAAA,WAAA,KACA,OAAA,EAJN,wCAOQ,iBAAA,QACA,WAAA,IAAA,MAAA,QARR,yDAAA,0DAaU,OAAA,KACA,MAAA,QACA,iBAAA,YAOR,0BAEI,iBAAA,kBACA,aAAA,kBAHJ,gDAOQ,QAAA,aACA,YAAA,OACA,eAAA,OACA,QAAA,GACA,WAAA,KAAA,MACA,aAAA,KAAA,MAAA,YACA,cAAA,EACA,YAAA,KAAA,MAAA,YAMR,0BAEI,iBAAA,kBACA,aAAA,kBAIJ,oBACE,QAAA,KAGF,8BAIM,cAAA,E/BktER,qD+B7sEE,0DAEI,UAAA,MAGJ,cACE,aAAA,Q/B8sEJ,0B+B3sEE,+BAEE,iBAAA,QAGF,cACE,QAAA,EAAA,EAAA,IAAA,cCnIJ,UACE,WAAA,MACA,OAAA,IAAA,OAAA,QACA,WAAA,KACA,cAAA,IAJF,sBAOI,UAAA,KACA,MAAA,KCRJ,0CAGI,QAAA,IACA,iBAAA,oBAJJ,uDAOM,QAAA,aACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,OAAA,IAAA,MAAA,QACA,MAAA,QACA,WAAA,OACA,cAAA,IAEA,4BAhBN,uDAiBQ,QAAA,MACA,OAAA,EAAA,KAAA,eAKF,yBAvBN,oDAwBQ,WAAA,MAxBR,2DA2BQ,iBAAA,YACA,MAAA,QA5BR,wEA+BU,iBAAA,QACA,MAAA,KAhCV,iDAuCI,YAAA,KACE,aAAA,EACA,WAAA,KACA,cAAA,EA1CN,oDA4CQ,QAAA,aA5CR,sDA8CU,QAAA,aACA,QAAA,OAAA,OACA,iBAAA,QACA,MAAA,KACA,cAAA,OAlDV,+DAsDY,OAAA,YACA,iBAAA,QAvDZ,yDA4DU,MAAA,MAMV,+BACE,YAAA,KACA,WAAA,MCrEF,+BAEI,QAAA,MAFJ,oCAKI,OAAA,eALJ,0CASM,iBAAA,QACA,MAAA,QACA,OAAA,IAAA,MAAA,QAXN,sDAaU,iBAAA,QACA,aAAA,QACA,MAAA,KACA,mBAAA,EAAA,EAAA,EAAA,IAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,IAAA,oBAhBV,wCAoBM,MAAA,MApBN,uDAsBQ,MAAA,EACA,kBAAA,eAAA,UAAA,eACA,IAAA,eAxBR,2BA8BM,UAAA,KACA,YAAA,IA/BN,gCAoCI,aAAA,KACA,MAAA,kBArCJ,sCAwCM,iBAAA,kBAxCN,sCA4CM,QAAA,aACA,aAAA,IACA,SAAA,SA9CN,8CAgDQ,cAAA,IAAA,YACA,mBAAA,IAAA,YACA,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,WAAA,IAAA,YACA,MAAA,KACA,QAAA,YA7DR,6CAgEQ,MAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,aAAA,IACA,YAAA,IACA,SAAA,SACA,IAAA,KACA,MAAA,KA1ER,qDA8EM,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,YAjFN,oEAoFQ,QAAA,IApFR,yEAyFQ,eAAA,KACA,QAAA,EA1FR,0EA+FQ,QAAA,QACA,YAAA,sBACA,YAAA,IAjGR,4EAsGQ,iBAAA,QACA,OAAA,YAvGR,2EA4GQ,iBAAA,QACA,aAAA,QA7GR,0EAgHQ,MAAA,KAhHR,uDAuHM,IAAA,eACA,iBAAA,QAxHN,6DA0HQ,MAAA,KlCg3ER,uCADA,qDkCz+EA,qDAkIM,WAAA,QACA,aAAA,QACA,MAAA,KlC42EJ,6CADA,2DkC/+EF,2DAuIU,MAAA,KAMR,yBADF,kEAIQ,IAAA,iBC/IR,8CAGM,OAAA,KAHN,2BAQI,MAAA,QARJ,8CAaQ,UAAA,KAbR,+BAoBQ,mBAAA,MAAA,EAAA,EAAA,IAAA,IAAA,kBAAA,WAAA,MAAA,EAAA,EAAA,IAAA,IAAA,kBAMR,WACE,QAAA,aACA,YAAA,wBACA,UAAA,QACA,YAAA,IACA,WAAA,QAGF,qBAEM,QAAA,SAIN,yBAEM,QAAA,SAIN,kBACE,OAAA,eACA,iBAAA,eAFF,sCAAA,wCAKI,oBAAA,kBALJ,8BAWQ,iBAAA,kBC3DR,aACI,WAAA,eADJ,kBAGQ,YAAA,OAAA,CAAA,qBACA,KAAA,QAJR,gCAOQ,OAAA,EAAA,KpC4hFR,yBoCxhFA,0BAEI,YAAA,OAAA,CAAA,qBAGJ,0BACI,YAAA,IAGJ,qBACI,eAAA,KACA,OAAA,QAGJ,wBACI,MAAA,kBACA,YAAA,OAAA,CAAA,qBACA,UAAA,eAGJ,sBACI,KAAA,epCshFJ,uBoCnhFA,uBAGQ,YAAA,OAAA,CAAA,qBACA,KAAA,QCtCR,UACI,OAAA,MCHJ,oBACE,OAAA,MAGF,SACE,QAAA,IAAA,KACA,iBAAA,kBACA,QAAA,IACA,MAAA,QACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,cAAA,IAGF,aACE,MAAA,QCbF,YACE,mBAAA,YAAA,WAAA,YACA,MAAA,eACA,OAAA,eACA,iBAAA,kBACA,mBAAA,EAAA,KAAA,KAAA,iBAAA,WAAA,EAAA,KAAA,KAAA,iBACA,QAAA,IAAA,eACA,cAAA,IACA,aAAA,kBAGF,UACE,MAAA,kBACA,UAAA,eACA,YAAA,eACA,YAAA,OAAA,CAAA,qBACA,YAAA,cCfF,OAAA,gBACE,OAAA,MACA,WAAA,QACA,cAAA,IAGF,eACE,QAAA,MACA,WAAA,OACA,MAAA,KACA,UAAA,KACA,YAAA,KACA,WAAA,QACA,cAAA,IACA,QAAA,KAAA,KAGF,qBACE,KAAA,IACA,YAAA,MACA,MAAA,EACA,OAAA,EACA,SAAA,SALF,2BAOI,OAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,WAAA,KAAA,MAAA,QAVJ,2BAaI,IAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,cAAA,KAAA,MAAA,QClCJ,kBACI,OAAA,KACA,WAAA,QACA,MAAA,QACA,YAAA,OAAA,CAAA,WACA,UAAA,SACA,QAAA,IAAA,ICNJ,aACI,OAAA,MADJ,+BAGQ,QAAA,GCDR,UACI,SAAA,SACA,IAAA,KACA,MAAA,KAKJ,2BAEQ,QAAA,MAFR,4BAKQ,QAAA,KAIR,cACI,iBAAA,KAMJ,cACI,iBAAA,qBACA,QAAA,YAAA,QAAA,YAAA,QAAA,KAEA,0BAJJ,cAKQ,OAAA,OALR,sBAUQ,QAAA,GACA,SAAA,SACA,MAAA,MACA,OAAA,MACA,cAAA,IAdR,0BAkBQ,WAAA,mCACA,gBAAA,MACA,kBAAA,UACA,oBAAA,OAIR,wBACI,QAAA,YAAA,QAAA,YAAA,QAAA,KAEA,0BAHJ,wBAIQ,WAAA,OAIR,wDAKoB,iBAAA,qBALpB,+DAAA,8DASwB,iBAAA,QClExB,0BAEQ,cAAA,KACA,aAAA,KAHR,yBAMQ,UAAA,KACA,SAAA,SACA,KAAA,KACA,IAAA,EACA,YAAA,KAIR,mBAGY,QAAA,MACA,QAAA,IAAA,EACA,MAAA,QAKZ,sCAGY,YAAA,IAHZ,sCAMY,MAAA,KACA,OAAA,KACA,UAAA,KACA,QAAA,EACA,YAAA,KACA,WAAA,OACA,cAAA,IAKZ,gBACI,SAAA,SACA,MAAA,EACA,IAAA,EAKJ,mCAGY,OAAA,IAAA,EAHZ,0CAMgB,iBAAA,QAMhB,iBAEQ,QAAA,aACA,WAAA,OACA,MAAA,QAJR,qCAMY,OAAA,IANZ,wBAAA,uBASY,MAAA,QATZ,4CAAA,2CAWgB,aAAA,kBAQhB,sBAEQ,YAAA,GAFR,yBAMQ,SAAA,SACA,UAAA,MACA,MAAA,sBACA,YAAA,GACA,MAAA,EACA,OAAA,EAOR,oCAGY,cAAA,KACA,WAAA,OACA,iBAAA,KACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBANZ,2CASgB,iBAAA,QAThB,oDAYgB,UAAA,KCjHhB,eACE,MAAA,MACA,MAAA,KACA,QAAA,KACA,cAAA,IAGF,gBACE,YAAA,MAGF,4BAEI,MAAA,QACA,YAAA,IAHJ,iBAMI,UAAA,KAIJ,yBACE,eACE,MAAA,KACA,MAAA,KAEF,gBACE,OAAA,GAKJ,aAEI,QAAA,MACA,MAAA,QACA,YAAA,KACA,QAAA,IAAA,IALJ,oBAOM,MAAA,QACA,YAAA,IAKN,cACE,QAAA,MACA,aAAA,EAFF,iBAKI,SAAA,SACA,QAAA,MACA,OAAA,KACA,YAAA,KACA,OAAA,QACA,4BAAA,IAAA,oBAAA,IAVJ,mBAaM,MAAA,QAbN,uBAiBM,WAAA,QACA,4BAAA,KAAA,oBAAA,KAlBN,2BAsBM,MAAA,KACA,SAAA,SAvBN,6BA2BM,MAAA,M7CuvFA,oDACA,kC6CnxFN,0CAgCQ,QAAA,MACA,MAAA,KAjCR,kCAqCQ,OAAA,IAAA,MAAA,YACA,cAAA,MACA,OAAA,KAAA,KAAA,EACA,OAAA,EACA,MAAA,EACA,YAAA,EACA,UAAA,EA3CR,oDA+CQ,OAAA,KAAA,KAAA,EAAA,KA/CR,0CAmDQ,WAAA,KACA,YAAA,IApDR,oCAwDQ,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,cAAA,SACA,SAAA,OACA,YAAA,OACA,cAAA,EA/DR,6BAoEM,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,OAAA,E7C2uFA,mC6CnzFN,sCA4EQ,SAAA,SACA,IAAA,EA7ER,sCAiFQ,KAAA,EACA,MAAA,MACA,cAAA,SACA,SAAA,OACA,YAAA,OArFR,mCAyFQ,MAAA,EACA,MAAA,MACA,aAAA,KA3FR,wBAAA,8BAiGM,mBAAA,MAAA,IAAA,EAAA,EAAA,QAAA,WAAA,MAAA,IAAA,EAAA,EAAA,QAjGN,wBAsGI,iBAAA,QACA,YAAA,IACA,MAAA,QAxGJ,0BA0GQ,MAAA,QACA,YAAA,IA3GR,qCAkHI,OAAA,QACA,OAAA,KACA,MAAA,KACA,SAAA,SACA,QAAA,aACA,mBAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAAA,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QACA,cAAA,IAxHJ,2CA2HM,QAAA,EACA,OAAA,QA5HN,yDA+HM,QAAA,EA/HN,2CAmIM,SAAA,SACA,OAAA,KACA,MAAA,KACA,KAAA,EACA,OAAA,QACA,QAAA,EACA,cAAA,EACA,4BAAA,KAAA,oBAAA,KACA,IAAA,EA3IN,kDA6IQ,QAAA,SACA,YAAA,wBACA,IAAA,EACA,OAAA,KACA,MAAA,QACA,MAAA,KACA,SAAA,SACA,WAAA,MACA,KAAA,IACA,UAAA,KAMR,4BACE,6BACI,MAAA,OCtMF,0BAFJ,qBAGQ,UAAA,MACA,UAAA,OAGJ,0BAPJ,qBAQQ,UAAA,MACA,UAAA,OAKR,iBACI,QAAA,IAAA,EADJ,sBAIY,QAAA,MACA,QAAA,IAAA,KACA,MAAA,QACA,YAAA,IAPZ,6BAYgB,MAAA,QAZhB,uBAiBY,aAAA,KAjBZ,4BAoBoB,QAAA,IAAA,KACA,MAAA,QACA,UAAA,KACA,YAAA,ICtClB,yBADF,kBAEI,UAAA,OAFJ,6CAQM,iBAAA,KARN,qDAYM,WAAA,MAKN,kCAGM,QAAA,GACA,SAAA,SACA,MAAA,IACA,OAAA,IACA,iBAAA,QACA,cAAA,IACA,MAAA,EATN,yBAcI,QAAA,IACA,mBAAA,KAAA,WAAA,KACA,UAAA,KAIJ,+BAEI,OAAA,EAIJ,WACE,OAAA,EADF,uBAKQ,iBAAA,KACA,aAAA,YACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAPR,gBAWM,QAAA,MACA,QAAA,KAAA,KACA,MAAA,QACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,WAAA,IAAA,MAAA,QACA,cAAA,IAhBN,sBAkBQ,iBAAA,KACA,aAAA,YACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAQR,kCAGM,OAAA,KACA,MAAA,KACA,YAAA,KACA,mBAAA,KAAA,WAAA,KACA,QAAA,EACA,UAAA,KACA,iBAAA,QACA,cAAA,IAVN,wCAcM,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,OAAA,IAAA,MAAA,QAMN,sBAEI,MAAA,KAFJ,mCAMI,SAAA,SACA,WAAA,OACA,cAAA,KARJ,0CAWM,iBAAA,KACA,SAAA,SACA,QAAA,EACA,QAAA,IAAA,KAdN,0CAkBM,QAAA,GACA,SAAA,SACA,MAAA,KACA,OAAA,IACA,KAAA,EACA,MAAA,EACA,iBAAA,QACA,IAAA,KAzBN,0CA4BM,UAAA,KA5BN,sCAgCI,cAAA,KACA,QAAA,aACA,SAAA,SAlCJ,kDAqCM,QAAA,KAAA,KACA,iBAAA,oBACA,cAAA,IAAA,IAAA,IAAA,EACA,SAAA,OAxCN,qEA2CQ,YAAA,IACA,MAAA,QACA,cAAA,IA7CR,gDAkDM,MAAA,MAlDN,iEAoDQ,UAAA,KACA,QAAA,IACA,MAAA,QACA,4BAvDR,iEAwDU,QAAA,MAxDV,+DA6DQ,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,OAAA,IAAA,MAAA,QA9DR,iDAmEM,UAAA,KAnEN,6CAyEM,MAAA,MAzEN,yDA2EQ,iBAAA,QACA,WAAA,MACA,cAAA,IAAA,IAAA,EAAA,IA7ER,uDAgFQ,MAAA,KAhFR,iFAsFY,MAAA,EACA,KAAA,KAvFZ,wDAkGQ,QAAA,SACA,YAAA,wBACA,SAAA,SACA,MAAA,QACA,MAAA,EACA,OAAA,EACA,UAAA,KAEJ,4BA1GJ,wDA2GM,QAAA,MAON,oBACE,WAAA,IAAA,MAAA,QAGF,YACE,cAAA,KACA,iBAAA,kBACA,aAAA,kBACA,cAAA,MAGF,kBACE,SAAA,SACA,MAAA,KACA,IAAA,IACA,kBAAA,iBAAA,UAAA,iBAJF,uBAOM,UAAA,KACA,YAAA,KACA,QAAA,EAAA,IACA,QAAA,aAMJ,4BADF,WAEI,UAAA,MCxOJ,oBACI,gBAAA,SACA,eAAA,EAAA,KAFJ,uBAII,iBAAA,KCNJ,iBAEI,MAAA,QAMJ,kBACE,WAAA,MCTF,yBACI,gBACI,SAAA,SADJ,sBAGQ,QAAA,SACA,YAAA,wBACA,UAAA,KACA,SAAA,SACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,cAAA,IACA,WAAA,OACA,MAAA,EACA,IAAA,IACA,kBAAA,iBAAA,UAAA,iBACA,iBAAA,QACA,MAAA,KACA,QAAA,EACA,MAAA,OAMZ,6BACI,OAAA,IAAA,MAAA,QACA,WAAA,EAMJ,gCAEQ,WAAA,MAFR,4CAIY,OAAA,KAAA,EAUZ,SACI,SAAA,SACA,YAAA,KACA,eAAA,KAHJ,kBAMQ,iBAAA,eAIR,aACI,MAAA,QACA,cAAA,IAMJ,YACI,QAAA,EAAA,KACA,MAAA,KACA,QAAA,IACA,cAAA,EACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YACA,cAAA,IAAA,MAAA,qBAEA,4BARJ,YASQ,iBAAA,MATR,yBAaQ,YAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAdR,oCAgBY,QAAA,KACA,4BAjBZ,oCAkBgB,QAAA,OAlBhB,qCAsBY,QAAA,MACA,4BAvBZ,qCAwBgB,QAAA,MAxBhB,4CAiCgB,MAAA,qBACA,YAAA,KACA,QAAA,IAAA,KACA,YAAA,IACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAEA,4BAvChB,4CAwCoB,MAAA,SAxCpB,mDAAA,kDA4CoB,MAAA,qBAEA,4BA9CpB,mDAAA,kDA+CwB,MAAA,SAIR,4BAnDhB,4CAoDoB,YAAA,gBApDpB,uBA2DQ,iBAAA,KACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBA5DR,oCA8DY,YAAA,KA9DZ,+CAiEgB,QAAA,MAjEhB,gDAoEgB,QAAA,KApEhB,uDA2EoB,YAAA,KACA,MAAA,QA5EpB,8DAAA,6DA8EwB,MAAA,QAQxB,YACI,SAAA,SACA,OAAA,KACA,MAAA,KACA,MAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EACA,QAAA,GACA,iBAAA,KAGJ,cACI,YAAA,MACA,eAAA,MAFJ,0BAKQ,iBAAA,sCACA,gBAAA,MACA,oBAAA,IAGJ,4BAVJ,cAWQ,YAAA,MACA,eAAA,MAZR,0BAgBQ,UAAA,KACA,4BAjBR,0BAkBY,UAAA,MAlBZ,6BAuBQ,UAAA,KACA,aAAA,MACA,YAAA,MACA,4BA1BR,6BA2BY,QAAA,OA3BZ,yCA8BY,aAAA,KACA,YAAA,KACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,QAAA,IACA,iBAAA,KAEA,4BArCZ,yCAsCgB,QAAA,aACA,MAAA,IACA,cAAA,MAxChB,8CA4CgB,iBAAA,QACA,UAAA,KACA,QAAA,IACA,WAAA,IA/ChB,gCAqDQ,SAAA,QArDR,8CAwDY,SAAA,QAxDZ,gDA4DY,SAAA,SACA,WAAA,MACA,MAAA,QACA,OAAA,KACA,UAAA,KACA,YAAA,IAQZ,gBACI,SAAA,SACA,OAAA,KAMJ,mBAEQ,WAAA,KACA,MAAA,eACA,OAAA,KAAA,KACA,QAAA,GACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAOR,iBACI,QAAA,GAMJ,+BAGY,MAAA,QACA,UAAA,KAOZ,sBAEQ,SAAA,SACA,IAAA,KACA,MAAA,KAMR,gBACI,QAAA,KAAA,EAAA,KACA,iBAAA,QACA,MAAA,qBAHJ,mCAMQ,MAAA,qBANR,uCAYgB,QAAA,MACA,MAAA,qBACA,cAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAfhB,6CAiBoB,MAAA,qBAjBpB,iCA0BY,QAAA,MACA,MAAA,qBACA,QAAA,KAAA,EACA,cAAA,IAAA,MAAA,qBA7BZ,6CA+BgB,MAAA,qBACA,UAAA,KAhChB,+CAoCgB,YAAA,EApChB,8CAwCgB,eAAA,EACA,cAAA,EAzChB,+BA+CQ,aAAA,qBC5UR,gBACI,UAAA,KACA,YAAA,IACA,WAAA,OACA,QAAA,YAAA,QAAA,YAAA,QAAA,KAJJ,qBAMQ,UAAA,KACA,YAAA,IACA,QAAA,MACA,YAAA,IAIR,YACI,MAAA,ICXJ,mCAGY,WAAA,OACA,QAAA,MAJZ,oDAOgB,SAAA,SAPhB,4DASoB,QAAA,GACA,SAAA,SACA,MAAA,KACA,IAAA,KACA,KAAA,EACA,MAAA,EACA,cAAA,IAAA,OAAA,QAfpB,qEAkBoB,SAAA,SACA,iBAAA,KACA,QAAA,IApBpB,0DA0BoB,kBAAA,UAAA,KAAA,SAAA,OAAA,UAAA,UAAA,KAAA,SAAA,OA1BpB,2DAgCoB,kBAAA,UAAA,KAAA,SAAA,OAAA,UAAA,UAAA,KAAA,SAAA,OAhCpB,kEAkCwB,QAAA,QAWxB,gBACI,YAAA,IAAA,OAAA,QACA,OAAA,EAAA,KAFJ,4BAIQ,SAAA,SACA,QAAA,EAAA,EAAA,KAAA,KALR,gDAQY,SAAA,SACA,KAAA,KACA,IAAA,EACA,QAAA,EACA,UAAA,KAZZ,2CAeY,SAAA,SACA,OAAA,IAAA,MAAA,QACA,cAAA,IAjBZ,uDAsBgB,MAAA,QAtBhB,uCA2BY,eAAA,ECvEZ,oBAEQ,SAAA,SAFR,4BAKY,QAAA,GACA,SAAA,SACA,MAAA,KACA,OAAA,IACA,WAAA,QACA,KAAA,EACA,MAAA,EACA,IAAA,KASZ,gBACI,SAAA,SACA,IAAA,IACA,KAAA,EACA,MAAA,EACA,kBAAA,iBAAA,UAAA,iBACA,OAAA,EAAA,KC/BJ,KACI,UAAA,IAGJ,KACI,WAAA,MCFJ,oBAEQ,WAAA,MACA,KAAA,eACA,MAAA,EACA,OAAA,KAIR,qBACI,MAAA,eACA,KAAA,YAFJ,0BAIQ,KAAA,YAQR,GACI,cAAA,EAGJ,mCACI,YAAA,IACA,aAAA,EAKJ,cACI,YAAA,IAAA,MAAA,kBAGJ,aACI,aAAA,IAAA,MAAA,kBAMJ,WACQ,cAAA,KACA,aAAA,KAFR,uBAIQ,KAAA,KACA,MAAA,EACA,aAAA,EACA,YAAA,IAAA,MAAA,qBAPR,iCAYY,aAAA,EACA,YAAA,IAAA,MAAA,kBAOZ,WvD04GA,oBuDx4GI,UAAA,IAKJ,8CAIgB,aAAA,EACA,uBAAA,EACA,0BAAA,ECjDZ,wBAAA,OACA,2BAAA,OD0CJ,6CAYgB,wBAAA,EACA,2BAAA,EC1CZ,uBAAA,OACA,0BAAA,ODmDJ,oBACI,WAAA,eAKJ,GACI,aAAA,EAMJ,oBACI,WAAA,MAGJ,qBAEQ,OAAA,MAAA,KAAA,MAAA,MACA,KAAA,EAIR,mBAEQ,KAAA,KACA,MAAA,KAKR,iCAEQ,aAAA,OACA,YAAA,EAHR,gCAOQ,YAAA,OACA,aAAA,EAOR,mBACI,aAAA,WACA,cAAA,QAFJ,0BAKQ,KAAA,EACA,MAAA,KAOR,kCAEQ,cAAA,MACA,aAAA,EAHR,0CAKY,aAAA,MACA,cAAA,EAQZ,kBACI,cAAA,YACA,aAAA,kBAFJ,oCAKM,MAAA,KACA,KAAA,EACA,aAAA,EAKJ,sBACE,aAAA,iBACA,cAAA,YAFF,4CAKI,QAAA,MALJ,kDAAA,mDAOM,MAAA,KACA,KAAA,QARN,4CAaI,MAAA,KACA,KAAA,EAON,YACI,aAAA,EACA,cAAA,QAGJ,kBACI,YAAA,EACA,aAAA,SAGJ,mBACI,YAAA,OACA,aAAA,EAGJ,gBACI,cAAA,OACA,aAAA,EAGJ,uBACI,aAAA,EACA,YAAA,KAGJ,8BAEQ,KAAA,KACA,MAAA,QAHR,6BAQQ,KAAA,KACA,MAAA,QAIR,eACI,cAAA,QACA,aAAA,EAFJ,6CAMY,MAAA,SACA,KAAA,KAPZ,4CAWY,MAAA,qBACA,KAAA,KAZZ,0EAkBY,kBAAA,oBAAA,UAAA,oBAKZ,0BAEQ,MAAA,KACA,KAAA,EACA,aAAA,QAIR,kBACI,cAAA,KACA,aAAA,EAFJ,+CAKY,KAAA,KACA,MAAA,MANZ,8CAUY,MAAA,kBACA,KAAA,KAXZ,6EAiBQ,kBAAA,qBAAA,UAAA,qBAKR,kBACI,cAAA,QACA,aAAA,EAFJ,+CAKY,KAAA,KACA,MAAA,SANZ,8CAUY,MAAA,qBACA,KAAA,KAXZ,6EAiBQ,kBAAA,oBAAA,UAAA,oBAMR,mFAEQ,KAAA,KACA,MAAA,MAKR,gFAEQ,KAAA,KACA,MAAA,MAMR,qBACI,YAAA,KACA,aAAA,EAGJ,oBACI,aAAA,KACA,YAAA,EvDgyGJ,6CACA,4CAHA,wFACA,+EAHA,uDACA,oEuD1xGA,uCvDwxGA,oDuDhxGI,wBAAA,OACA,2BAAA,OACA,uBAAA,EACA,0BAAA,EvD+xGJ,8CACA,6CuD7xGA,sCvDuxGA,mDAGA,qEACA,kFAHA,yDACA,sEuDjxGI,uBAAA,OACA,0BAAA,OACA,wBAAA,EACA,2BAAA,EE3WI,KAAgC,OAAA,YAChC,MzDsoHR,MyDpoHU,WAAA,YAEF,MACE,YAAA,YACA,aAAA,YAEF,MzDsoHR,MyDpoHU,cAAA,YAEF,MACE,aAAA,YACA,YAAA,YAfF,KAAgC,OAAA,iBAChC,MzDypHR,MyDvpHU,WAAA,iBAEF,MACE,YAAA,iBACA,aAAA,YAEF,MzDypHR,MyDvpHU,cAAA,iBAEF,MACE,aAAA,iBACA,YAAA,YAfF,KAAgC,OAAA,gBAChC,MzD4qHR,MyD1qHU,WAAA,gBAEF,MACE,YAAA,gBACA,aAAA,YAEF,MzD4qHR,MyD1qHU,cAAA,gBAEF,MACE,aAAA,gBACA,YAAA,YAfF,KAAgC,OAAA,eAChC,MzD+rHR,MyD7rHU,WAAA,eAEF,MACE,YAAA,eACA,aAAA,YAEF,MzD+rHR,MyD7rHU,cAAA,eAEF,MACE,aAAA,eACA,YAAA,YAfF,KAAgC,OAAA,iBAChC,MzDktHR,MyDhtHU,WAAA,iBAEF,MACE,YAAA,iBACA,aAAA,YAEF,MzDktHR,MyDhtHU,cAAA,iBAEF,MACE,aAAA,iBACA,YAAA,YAfF,KAAgC,OAAA,eAChC,MzDquHR,MyDnuHU,WAAA,eAEF,MACE,YAAA,eACA,aAAA,YAEF,MzDquHR,MyDnuHU,cAAA,eAEF,MACE,aAAA,eACA,YAAA,YAfF,KAAgC,QAAA,YAChC,MzDwvHR,MyDtvHU,YAAA,YAEF,MACE,aAAA,YACA,cAAA,YAEF,MzDwvHR,MyDtvHU,eAAA,YAEF,MACE,cAAA,YACA,aAAA,YAfF,KAAgC,QAAA,iBAChC,MzD2wHR,MyDzwHU,YAAA,iBAEF,MACE,aAAA,iBACA,cAAA,YAEF,MzD2wHR,MyDzwHU,eAAA,iBAEF,MACE,cAAA,iBACA,aAAA,YAfF,KAAgC,QAAA,gBAChC,MzD8xHR,MyD5xHU,YAAA,gBAEF,MACE,aAAA,gBACA,cAAA,YAEF,MzD8xHR,MyD5xHU,eAAA,gBAEF,MACE,cAAA,gBACA,aAAA,YAfF,KAAgC,QAAA,eAChC,MzDizHR,MyD/yHU,YAAA,eAEF,MACE,aAAA,eACA,cAAA,YAEF,MzDizHR,MyD/yHU,eAAA,eAEF,MACE,cAAA,eACA,aAAA,YAfF,KAAgC,QAAA,iBAChC,MzDo0HR,MyDl0HU,YAAA,iBAEF,MACE,aAAA,iBACA,cAAA,YAEF,MzDo0HR,MyDl0HU,eAAA,iBAEF,MACE,cAAA,iBACA,aAAA,YAfF,KAAgC,QAAA,eAChC,MzDu1HR,MyDr1HU,YAAA,eAEF,MACE,aAAA,eACA,cAAA,YAEF,MzDu1HR,MyDr1HU,eAAA,eAEF,MACE,cAAA,eACA,aAAA,YAQF,MAAwB,OAAA,kBACxB,OzDm1HR,OyDj1HU,WAAA,kBAEF,OzDm1HR,OyDj1HU,aAAA,kBAEF,OzDm1HR,OyDj1HU,cAAA,kBAEF,OzDm1HR,OyDj1HU,YAAA,kBAfF,MAAwB,OAAA,iBACxB,OzDs2HR,OyDp2HU,WAAA,iBAEF,OzDs2HR,OyDp2HU,aAAA,iBAEF,OzDs2HR,OyDp2HU,cAAA,iBAEF,OzDs2HR,OyDp2HU,YAAA,iBAfF,MAAwB,OAAA,gBACxB,OzDy3HR,OyDv3HU,WAAA,gBAEF,OzDy3HR,OyDv3HU,aAAA,gBAEF,OzDy3HR,OyDv3HU,cAAA,gBAEF,OzDy3HR,OyDv3HU,YAAA,gBAfF,MAAwB,OAAA,kBACxB,OzD44HR,OyD14HU,WAAA,kBAEF,OzD44HR,OyD14HU,aAAA,kBAEF,OzD44HR,OyD14HU,cAAA,kBAEF,OzD44HR,OyD14HU,YAAA,kBAfF,MAAwB,OAAA,gBACxB,OzD+5HR,OyD75HU,WAAA,gBAEF,OzD+5HR,OyD75HU,aAAA,gBAEF,OzD+5HR,OyD75HU,cAAA,gBAEF,OzD+5HR,OyD75HU,YAAA,gBAMN,QAAmB,OAAA,eACnB,SzD65HJ,SyD35HM,WAAA,eAEF,SzD65HJ,SyD35HM,YAAA,eACA,aAAA,kBAEF,SzD65HJ,SyD35HM,cAAA,eAEF,SACE,aAAA,eACA,YAAA,eAEF,SACE,aAAA,eACA,YAAA,YrDdF,yBqDlDI,QAAgC,OAAA,YAChC,SzDi+HN,SyD/9HQ,WAAA,YAEF,SACE,YAAA,YACA,aAAA,YAEF,SzD+9HN,SyD79HQ,cAAA,YAEF,SACE,aAAA,YACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,SzD++HN,SyD7+HQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,SzD6+HN,SyD3+HQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,gBAChC,SzD6/HN,SyD3/HQ,WAAA,gBAEF,SACE,YAAA,gBACA,aAAA,YAEF,SzD2/HN,SyDz/HQ,cAAA,gBAEF,SACE,aAAA,gBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,SzD2gIN,SyDzgIQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,SzDygIN,SyDvgIQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,SzDyhIN,SyDvhIQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,SzDuhIN,SyDrhIQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,SzDuiIN,SyDriIQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,SzDqiIN,SyDniIQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,QAAA,YAChC,SzDqjIN,SyDnjIQ,YAAA,YAEF,SACE,aAAA,YACA,cAAA,YAEF,SzDmjIN,SyDjjIQ,eAAA,YAEF,SACE,cAAA,YACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,SzDmkIN,SyDjkIQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,SzDikIN,SyD/jIQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,gBAChC,SzDilIN,SyD/kIQ,YAAA,gBAEF,SACE,aAAA,gBACA,cAAA,YAEF,SzD+kIN,SyD7kIQ,eAAA,gBAEF,SACE,cAAA,gBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,SzD+lIN,SyD7lIQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,SzD6lIN,SyD3lIQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,SzD6mIN,SyD3mIQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,SzD2mIN,SyDzmIQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,SzD2nIN,SyDznIQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,SzDynIN,SyDvnIQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAQF,SAAwB,OAAA,kBACxB,UzDknIN,UyDhnIQ,WAAA,kBAEF,UzDinIN,UyD/mIQ,aAAA,kBAEF,UzDgnIN,UyD9mIQ,cAAA,kBAEF,UzD+mIN,UyD7mIQ,YAAA,kBAfF,SAAwB,OAAA,iBACxB,UzDgoIN,UyD9nIQ,WAAA,iBAEF,UzD+nIN,UyD7nIQ,aAAA,iBAEF,UzD8nIN,UyD5nIQ,cAAA,iBAEF,UzD6nIN,UyD3nIQ,YAAA,iBAfF,SAAwB,OAAA,gBACxB,UzD8oIN,UyD5oIQ,WAAA,gBAEF,UzD6oIN,UyD3oIQ,aAAA,gBAEF,UzD4oIN,UyD1oIQ,cAAA,gBAEF,UzD2oIN,UyDzoIQ,YAAA,gBAfF,SAAwB,OAAA,kBACxB,UzD4pIN,UyD1pIQ,WAAA,kBAEF,UzD2pIN,UyDzpIQ,aAAA,kBAEF,UzD0pIN,UyDxpIQ,cAAA,kBAEF,UzDypIN,UyDvpIQ,YAAA,kBAfF,SAAwB,OAAA,gBACxB,UzD0qIN,UyDxqIQ,WAAA,gBAEF,UzDyqIN,UyDvqIQ,aAAA,gBAEF,UzDwqIN,UyDtqIQ,cAAA,gBAEF,UzDuqIN,UyDrqIQ,YAAA,gBAMN,WAAmB,OAAA,eACnB,YzDmqIF,YyDjqII,WAAA,eAEF,YzDkqIF,YyDhqII,YAAA,eACA,aAAA,kBAEF,YzDiqIF,YyD/pII,cAAA,eAEF,YACE,aAAA,eACA,YAAA,eAEF,YACE,aAAA,eACA,YAAA,arDdF,yBqDlDI,QAAgC,OAAA,YAChC,SzDmuIN,SyDjuIQ,WAAA,YAEF,SACE,YAAA,YACA,aAAA,YAEF,SzDiuIN,SyD/tIQ,cAAA,YAEF,SACE,aAAA,YACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,SzDivIN,SyD/uIQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,SzD+uIN,SyD7uIQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,gBAChC,SzD+vIN,SyD7vIQ,WAAA,gBAEF,SACE,YAAA,gBACA,aAAA,YAEF,SzD6vIN,SyD3vIQ,cAAA,gBAEF,SACE,aAAA,gBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,SzD6wIN,SyD3wIQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,SzD2wIN,SyDzwIQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,SzD2xIN,SyDzxIQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,SzDyxIN,SyDvxIQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,SzDyyIN,SyDvyIQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,SzDuyIN,SyDryIQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,QAAA,YAChC,SzDuzIN,SyDrzIQ,YAAA,YAEF,SACE,aAAA,YACA,cAAA,YAEF,SzDqzIN,SyDnzIQ,eAAA,YAEF,SACE,cAAA,YACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,SzDq0IN,SyDn0IQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,SzDm0IN,SyDj0IQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,gBAChC,SzDm1IN,SyDj1IQ,YAAA,gBAEF,SACE,aAAA,gBACA,cAAA,YAEF,SzDi1IN,SyD/0IQ,eAAA,gBAEF,SACE,cAAA,gBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,SzDi2IN,SyD/1IQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,SzD+1IN,SyD71IQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,SzD+2IN,SyD72IQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,SzD62IN,SyD32IQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,SzD63IN,SyD33IQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,SzD23IN,SyDz3IQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAQF,SAAwB,OAAA,kBACxB,UzDo3IN,UyDl3IQ,WAAA,kBAEF,UzDm3IN,UyDj3IQ,aAAA,kBAEF,UzDk3IN,UyDh3IQ,cAAA,kBAEF,UzDi3IN,UyD/2IQ,YAAA,kBAfF,SAAwB,OAAA,iBACxB,UzDk4IN,UyDh4IQ,WAAA,iBAEF,UzDi4IN,UyD/3IQ,aAAA,iBAEF,UzDg4IN,UyD93IQ,cAAA,iBAEF,UzD+3IN,UyD73IQ,YAAA,iBAfF,SAAwB,OAAA,gBACxB,UzDg5IN,UyD94IQ,WAAA,gBAEF,UzD+4IN,UyD74IQ,aAAA,gBAEF,UzD84IN,UyD54IQ,cAAA,gBAEF,UzD64IN,UyD34IQ,YAAA,gBAfF,SAAwB,OAAA,kBACxB,UzD85IN,UyD55IQ,WAAA,kBAEF,UzD65IN,UyD35IQ,aAAA,kBAEF,UzD45IN,UyD15IQ,cAAA,kBAEF,UzD25IN,UyDz5IQ,YAAA,kBAfF,SAAwB,OAAA,gBACxB,UzD46IN,UyD16IQ,WAAA,gBAEF,UzD26IN,UyDz6IQ,aAAA,gBAEF,UzD06IN,UyDx6IQ,cAAA,gBAEF,UzDy6IN,UyDv6IQ,YAAA,gBAMN,WAAmB,OAAA,eACnB,YzDq6IF,YyDn6II,WAAA,eAEF,YzDo6IF,YyDl6II,YAAA,eACA,aAAA,kBAEF,YzDm6IF,YyDj6II,cAAA,eAEF,YACE,aAAA,eACA,YAAA,eAEF,YACE,aAAA,eACA,YAAA,arDdF,yBqDlDI,QAAgC,OAAA,YAChC,SzDq+IN,SyDn+IQ,WAAA,YAEF,SACE,YAAA,YACA,aAAA,YAEF,SzDm+IN,SyDj+IQ,cAAA,YAEF,SACE,aAAA,YACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,SzDm/IN,SyDj/IQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,SzDi/IN,SyD/+IQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,gBAChC,SzDigJN,SyD//IQ,WAAA,gBAEF,SACE,YAAA,gBACA,aAAA,YAEF,SzD+/IN,SyD7/IQ,cAAA,gBAEF,SACE,aAAA,gBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,SzD+gJN,SyD7gJQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,SzD6gJN,SyD3gJQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,SzD6hJN,SyD3hJQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,SzD2hJN,SyDzhJQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,SzD2iJN,SyDziJQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,SzDyiJN,SyDviJQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,QAAA,YAChC,SzDyjJN,SyDvjJQ,YAAA,YAEF,SACE,aAAA,YACA,cAAA,YAEF,SzDujJN,SyDrjJQ,eAAA,YAEF,SACE,cAAA,YACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,SzDukJN,SyDrkJQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,SzDqkJN,SyDnkJQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,gBAChC,SzDqlJN,SyDnlJQ,YAAA,gBAEF,SACE,aAAA,gBACA,cAAA,YAEF,SzDmlJN,SyDjlJQ,eAAA,gBAEF,SACE,cAAA,gBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,SzDmmJN,SyDjmJQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,SzDimJN,SyD/lJQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,SzDinJN,SyD/mJQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,SzD+mJN,SyD7mJQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,SzD+nJN,SyD7nJQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,SzD6nJN,SyD3nJQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAQF,SAAwB,OAAA,kBACxB,UzDsnJN,UyDpnJQ,WAAA,kBAEF,UzDqnJN,UyDnnJQ,aAAA,kBAEF,UzDonJN,UyDlnJQ,cAAA,kBAEF,UzDmnJN,UyDjnJQ,YAAA,kBAfF,SAAwB,OAAA,iBACxB,UzDooJN,UyDloJQ,WAAA,iBAEF,UzDmoJN,UyDjoJQ,aAAA,iBAEF,UzDkoJN,UyDhoJQ,cAAA,iBAEF,UzDioJN,UyD/nJQ,YAAA,iBAfF,SAAwB,OAAA,gBACxB,UzDkpJN,UyDhpJQ,WAAA,gBAEF,UzDipJN,UyD/oJQ,aAAA,gBAEF,UzDgpJN,UyD9oJQ,cAAA,gBAEF,UzD+oJN,UyD7oJQ,YAAA,gBAfF,SAAwB,OAAA,kBACxB,UzDgqJN,UyD9pJQ,WAAA,kBAEF,UzD+pJN,UyD7pJQ,aAAA,kBAEF,UzD8pJN,UyD5pJQ,cAAA,kBAEF,UzD6pJN,UyD3pJQ,YAAA,kBAfF,SAAwB,OAAA,gBACxB,UzD8qJN,UyD5qJQ,WAAA,gBAEF,UzD6qJN,UyD3qJQ,aAAA,gBAEF,UzD4qJN,UyD1qJQ,cAAA,gBAEF,UzD2qJN,UyDzqJQ,YAAA,gBAMN,WAAmB,OAAA,eACnB,YzDuqJF,YyDrqJI,WAAA,eAEF,YzDsqJF,YyDpqJI,YAAA,eACA,aAAA,kBAEF,YzDqqJF,YyDnqJI,cAAA,eAEF,YACE,aAAA,eACA,YAAA,eAEF,YACE,aAAA,eACA,YAAA,arDdF,0BqDlDI,QAAgC,OAAA,YAChC,SzDuuJN,SyDruJQ,WAAA,YAEF,SACE,YAAA,YACA,aAAA,YAEF,SzDquJN,SyDnuJQ,cAAA,YAEF,SACE,aAAA,YACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,SzDqvJN,SyDnvJQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,SzDmvJN,SyDjvJQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,gBAChC,SzDmwJN,SyDjwJQ,WAAA,gBAEF,SACE,YAAA,gBACA,aAAA,YAEF,SzDiwJN,SyD/vJQ,cAAA,gBAEF,SACE,aAAA,gBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,SzDixJN,SyD/wJQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,SzD+wJN,SyD7wJQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,SzD+xJN,SyD7xJQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,SzD6xJN,SyD3xJQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,SzD6yJN,SyD3yJQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,SzD2yJN,SyDzyJQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,QAAA,YAChC,SzD2zJN,SyDzzJQ,YAAA,YAEF,SACE,aAAA,YACA,cAAA,YAEF,SzDyzJN,SyDvzJQ,eAAA,YAEF,SACE,cAAA,YACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,SzDy0JN,SyDv0JQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,SzDu0JN,SyDr0JQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,gBAChC,SzDu1JN,SyDr1JQ,YAAA,gBAEF,SACE,aAAA,gBACA,cAAA,YAEF,SzDq1JN,SyDn1JQ,eAAA,gBAEF,SACE,cAAA,gBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,SzDq2JN,SyDn2JQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,SzDm2JN,SyDj2JQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,SzDm3JN,SyDj3JQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,SzDi3JN,SyD/2JQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,SzDi4JN,SyD/3JQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,SzD+3JN,SyD73JQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAQF,SAAwB,OAAA,kBACxB,UzDw3JN,UyDt3JQ,WAAA,kBAEF,UzDu3JN,UyDr3JQ,aAAA,kBAEF,UzDs3JN,UyDp3JQ,cAAA,kBAEF,UzDq3JN,UyDn3JQ,YAAA,kBAfF,SAAwB,OAAA,iBACxB,UzDs4JN,UyDp4JQ,WAAA,iBAEF,UzDq4JN,UyDn4JQ,aAAA,iBAEF,UzDo4JN,UyDl4JQ,cAAA,iBAEF,UzDm4JN,UyDj4JQ,YAAA,iBAfF,SAAwB,OAAA,gBACxB,UzDo5JN,UyDl5JQ,WAAA,gBAEF,UzDm5JN,UyDj5JQ,aAAA,gBAEF,UzDk5JN,UyDh5JQ,cAAA,gBAEF,UzDi5JN,UyD/4JQ,YAAA,gBAfF,SAAwB,OAAA,kBACxB,UzDk6JN,UyDh6JQ,WAAA,kBAEF,UzDi6JN,UyD/5JQ,aAAA,kBAEF,UzDg6JN,UyD95JQ,cAAA,kBAEF,UzD+5JN,UyD75JQ,YAAA,kBAfF,SAAwB,OAAA,gBACxB,UzDg7JN,UyD96JQ,WAAA,gBAEF,UzD+6JN,UyD76JQ,aAAA,gBAEF,UzD86JN,UyD56JQ,cAAA,gBAEF,UzD66JN,UyD36JQ,YAAA,gBAMN,WAAmB,OAAA,eACnB,YzDy6JF,YyDv6JI,WAAA,eAEF,YzDw6JF,YyDt6JI,YAAA,eACA,aAAA,kBAEF,YzDu6JF,YyDr6JI,cAAA,eAEF,YACE,aAAA,eACA,YAAA,eAEF,YACE,aAAA,eACA,YAAA,aCpEF,YAAwB,MAAA,gBACxB,aAAwB,MAAA,eACxB,YAAwB,MAAA,etDoDxB,yBsDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBtDoDxB,yBsDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBtDoDxB,yBsDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBtDoDxB,0BsDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBCKxB,WAAwB,WAAA,gBACxB,YAAwB,WAAA,eACxB,aAAwB,WAAA,iBvD6CxB,yBuD/CA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBvD6CxB,yBuD/CA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBvD6CxB,yBuD/CA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBvD6CxB,0BuD/CA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBCT5B,eACI,QAAA,EAAA,EAAA,EAAA,eAGJ,kBAEQ,MAAA,KACA,KAAA,IAIR,cACI,YAAA,EACA,aAAA,MAGJ,QACI,KAAA,EACA,MAAA,MAKJ,+BAGY,MAAA,KAHZ,qCAWwB,QAAA,MAAA,OAAA,MAAA,OAXxB,oDAkBgC,QAAA,MAAA,OAAA,MAAA,OACA,UAAA,OAUhC,iCAEQ,YAAA,EACA,aAAA,KAHR,2BAOQ,KAAA,EACA,MAAA,KARR,0DAiB4B,YAAA,EACA,aAAA,IAlB5B,6DAsB4B,cAAA,KACA,aAAA,EAvB5B,+DA6B4B,KAAA,EACA,MAAA,KA9B5B,kEAoCoB,KAAA,KACA,MAAA,MAOpB,yBAEI,cACI,aAAA,YAEJ,QACI,MAAA,GAOR,WACI,MAAA,eACA,KAAA,OACA,MAAA,KAHJ,0CAQgB,MAAA,EACA,KAAA,KAMhB,8BAEQ,KAAA,EACA,MAAA,KAOR,mEAO4B,cAAA,OAP5B,kFAcoC,cAAA,OAdpC,6EA2BgB,WAAA,MA3BhB,8DAgCY,YAAA,EACA,aAAA,KAjCZ,wGAqCY,KAAA,KArCZ,4CA0CQ,aAAA,MACA,YAAA,EAMR,2CAEQ,aAAA,YAFR,qCAMQ,MAAA,YxDvIJ,yBwD4IA,sDAKoB,aAAA,OACA,cAAA,EANpB,iCAcY,MAAA,EACA,KAAA,MAMhB,kBAEQ,YAAA,EACA,aAAA,KAIR,2BAEQ,WAAA,MAIR,yBACI,oDAIgB,MAAA,KACA,KAAA,KACA,kBAAA,cAAA,iBAAA,UAAA,cAAA,iBANhB,0DAWoB,MAAA,KACA,KAAA,MAQxB,4BACI,iCAGY,aAAA,EACA,cAAA,MC9PhB,oBACI,aAAA,EACA,YAAA,IAGJ,2DACI,aAAA,EACA,YAAA,IAGJ,yCACI,QAAA,IAAA,IAAA,EAAA,EAKJ,aACI,cAAA,cACA,aAAA,KAKJ,2EAGY,cAAA,KAHZ,wEAOY,KAAA,IACA,MAAA,KARZ,2EAcY,MAAA,MACA,YAAA,IACA,aAAA,EAhBZ,0CAqBQ,WAAA,MArBR,2CAyBQ,MAAA,MAMR,0CAGY,MAAA,KAHZ,kDAOY,WAAA,MAPZ,wDAWY,cAAA,EACA,aAAA,QAOZ,uCAEQ,WAAA,eAFR,6CAIY,YAAA,YACA,aAAA,KAOZ,sDACI,aAAA,IACA,cAAA,EAIJ,6DAIgB,aAAA,EACA,YAAA,KAMhB,kDAGY,YAAA,EACA,aAAA,KAMZ,wCAEQ,MAAA,KAFR,4CAOgB,YAAA,MACA,IAAA,KAOhB,wBAEQ,aAAA,EACA,cAAA,KAHR,+BAMY,KAAA,KACA,MAAA,IAWJ,yBAFR,oDAIgB,WAAA,OAJhB,yDAWY,MAAA,KAQZ,kBACI,YAAA,EACA,aAAA,IAFJ,mCAKQ,YAAA,EACA,aAAA,ICxKR,wBAEI,KAAA,eAOJ,kBAEI,YAAA,KACA,aAAA,EASJ,a9DuwKE,S8DtwKA,c9DuwKA,K8DpwKI,aAAA,GAKN,gBACE,KAAA,EACA,MAAA,KACA,cAAA,EAAA,IAAA,IAAA,EChCF,gBACI,YAAA,EACA,aAAA,IAAA,OAAA,QAFJ,4BAIQ,QAAA,EAAA,KAAA,KAAA,EAJR,gDAMY,KAAA,KACA,MAAA,KAOZ,eACI,MAAA,MAGJ,gBACI,aAAA,MACA,YAAA,EAGJ,2BAGY,MAAA,MAHZ,oDAAA,kCAAA,0CAOgB,MAAA,MAPhB,oDAUgB,OAAA,KAAA,KAAA,EAAA,KAVhB,0CAagB,aAAA,IAbhB,oCAiBgB,MAAA,MACA,KAAA,EAlBhB,6BAsBY,MAAA,MACA,KAAA,EAvBZ,sCA0BgB,MAAA,EACA,KAAA,MA3BhB,mCA+BgB,KAAA,EACA,MAAA,KACA,cAAA,KACA,aAAA,EAlChB,kDA0CgB,MAAA,IAMhB,yBACI,eACI,MAAA,eACA,MAAA,KAEJ,gBACI,aAAA,GAMR,UACI,SAAA,SACA,KAAA,KACA,MAAA,KAMJ,uBACI,cAAA,KACA,aAAA,EAMJ,gDAGY,MAAA,KAHZ,kDAOY,cAAA,IAAA,IAAA,EAAA,IAPZ,0BAYQ,MAAA,KAZR,yDAegB,WAAA,KACA,cAAA,IAAA,IAAA,IAAA,EAhBhB,uDAoBgB,MAAA,MApBhB,wDA0BQ,MAAA,KACA,KAAA,EAIR,YACI,aAAA,MACA,cAAA,KAGJ,kBACI,MAAA,KACA,KAAA,KAGJ,yBACI,gBACI,SAAA,SADJ,sBAGQ,MAAA,KACA,KAAA", "file": "app-rtl.min.css", "sourcesContent": ["//\r\n// Google font - Poppins\r\n//\r\n\r\n@import url('https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap');", "// \r\n// _header.scss\r\n// \r\n\r\n#page-topbar {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    left: 0;\r\n    z-index: 1002;\r\n    background-color: $header-bg;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.navbar-header {\r\n    display: flex;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    height: $header-height;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2) 0 0;\r\n\r\n    .dropdown.show {\r\n        .header-item {\r\n            background-color: $gray-100;\r\n        }\r\n    }\r\n}\r\n\r\n.navbar-brand-box {\r\n    padding: 0 1.5rem;\r\n    text-align: center;\r\n    width: $navbar-brand-box-width;\r\n}\r\n\r\n.logo {\r\n    line-height: 70px;\r\n\r\n    .logo-sm {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.logo-light {\r\n    display: none;\r\n}\r\n\r\n/* Search */\r\n\r\n.app-search {\r\n    padding: calc(#{$header-height - 38px} / 2) 0;\r\n\r\n    .form-control {\r\n        border: none;\r\n        height: 38px;\r\n        padding-left: 40px;\r\n        padding-right: 20px;\r\n        background-color: $topbar-search-bg;\r\n        box-shadow: none;\r\n        border-radius: 30px;\r\n    }\r\n    span {\r\n        position: absolute;\r\n        z-index: 10;\r\n        font-size: 16px;\r\n        line-height: 38px;\r\n        left: 13px;\r\n        top: 0;\r\n        color: $gray-600;\r\n    }\r\n}\r\n\r\n// Mega menu\r\n\r\n.megamenu-list {\r\n    li{\r\n        position: relative;\r\n        padding: 5px 0px;\r\n        a{\r\n            color: $dropdown-color;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n\r\n    .logo {\r\n\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.page-content {\r\n    padding: calc(#{$header-height} + #{$grid-gutter-width}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n}\r\n\r\n.header-item {\r\n    height: $header-height;\r\n    box-shadow: none !important;\r\n    color: $header-item-color;\r\n    border: 0;\r\n    border-radius: 0px;\r\n\r\n    &:hover {\r\n        color: $header-item-color;\r\n    }\r\n}\r\n\r\n.header-profile-user {\r\n    height: 36px;\r\n    width: 36px;\r\n    background-color: $gray-300;\r\n    padding: 3px;\r\n}\r\n\r\n.noti-icon {\r\n    i {\r\n        font-size: 22px;\r\n        color: $header-item-color;\r\n    }\r\n\r\n    .badge {\r\n        position: absolute;\r\n        top: 12px;\r\n        right: 4px;\r\n    }\r\n}\r\n\r\n.notification-item {\r\n    .media {\r\n        padding: 0.75rem 1rem;\r\n\r\n        &:hover {\r\n            background-color: $gray-300;\r\n        }\r\n    }\r\n}\r\n\r\n// Dropdown with Icons\r\n.dropdown-icon-item {\r\n    display: block;\r\n    border-radius: 3px;\r\n    line-height: 34px;\r\n    text-align: center;\r\n    padding: 15px 0 9px;\r\n    display: block;\r\n    border: 1px solid transparent;\r\n    color: $gray-600;\r\n\r\n    img {\r\n        height: 24px;\r\n    }\r\n\r\n    span {\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n    }\r\n\r\n    &:hover {\r\n        border-color: $gray-200;\r\n    }\r\n}\r\n\r\n// Full Screen\r\n.fullscreen-enable {\r\n    [data-toggle=\"fullscreen\"] {\r\n        .bx-fullscreen::before {\r\n            content: \"\\ea3f\";\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-topbar=\"dark\"] {\r\n    #page-topbar { \r\n        background-color: $header-dark-bg;\r\n    }\r\n    .navbar-header {\r\n        .dropdown.show {\r\n            .header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color: $header-dark-item-color;\r\n    \r\n        &:hover {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba($topbar-search-bg,0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n    .navbar-brand-box {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@media (max-width: 600px) {\r\n    .navbar-header {\r\n        .dropdown {\r\n            position: static;\r\n\r\n            .dropdown-menu {\r\n                left: 10px !important;\r\n                right: 10px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 380px) {\r\n    .navbar-brand-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n    .page-content {\r\n        margin-top: $header-height;\r\n        padding: calc(55px + #{$grid-gutter-width}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n    }    \r\n}\r\n\r\n@media (max-width: 992px) { \r\n    body[data-layout=\"horizontal\"] {\r\n        .page-content {\r\n            margin-top: 15px;\r\n        }    \r\n    }\r\n}", "/*\r\nTemplate Name: Skote - Responsive Bootstrap 4 Admin Dashboard\r\nAuthor: Themesbrand\r\nVersion: 2.0\r\nWebsite: https://themesbrand.com/\r\nContact: <EMAIL>\r\nFile: Main RTL Css File\r\n*/\n@import url(\"https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap\");\n#page-topbar {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1002;\n  background-color: #ffffff;\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03); }\n\n.navbar-header {\n  display: flex;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0 auto;\n  height: 70px;\n  padding: 0 calc(24px / 2) 0 0; }\n  .navbar-header .dropdown.show .header-item {\n    background-color: #f8f9fa; }\n\n.navbar-brand-box {\n  padding: 0 1.5rem;\n  text-align: center;\n  width: 250px; }\n\n.logo {\n  line-height: 70px; }\n  .logo .logo-sm {\n    display: none; }\n\n.logo-light {\n  display: none; }\n\n/* Search */\n.app-search {\n  padding: calc(32px / 2) 0; }\n  .app-search .form-control {\n    border: none;\n    height: 38px;\n    padding-left: 40px;\n    padding-right: 20px;\n    background-color: #f3f3f9;\n    box-shadow: none;\n    border-radius: 30px; }\n  .app-search span {\n    position: absolute;\n    z-index: 10;\n    font-size: 16px;\n    line-height: 38px;\n    left: 13px;\n    top: 0;\n    color: #74788d; }\n\n.megamenu-list li {\n  position: relative;\n  padding: 5px 0px; }\n  .megamenu-list li a {\n    color: #495057; }\n\n@media (max-width: 992px) {\n  .navbar-brand-box {\n    width: auto; }\n  .logo span.logo-lg {\n    display: none; }\n  .logo span.logo-sm {\n    display: inline-block; } }\n\n.page-content {\n  padding: calc(70px + 24px) calc(24px / 2) 60px calc(24px / 2); }\n\n.header-item {\n  height: 70px;\n  box-shadow: none !important;\n  color: #555b6d;\n  border: 0;\n  border-radius: 0px; }\n  .header-item:hover {\n    color: #555b6d; }\n\n.header-profile-user {\n  height: 36px;\n  width: 36px;\n  background-color: #f6f6f6;\n  padding: 3px; }\n\n.noti-icon i {\n  font-size: 22px;\n  color: #555b6d; }\n\n.noti-icon .badge {\n  position: absolute;\n  top: 12px;\n  right: 4px; }\n\n.notification-item .media {\n  padding: 0.75rem 1rem; }\n  .notification-item .media:hover {\n    background-color: #f6f6f6; }\n\n.dropdown-icon-item {\n  display: block;\n  border-radius: 3px;\n  line-height: 34px;\n  text-align: center;\n  padding: 15px 0 9px;\n  display: block;\n  border: 1px solid transparent;\n  color: #74788d; }\n  .dropdown-icon-item img {\n    height: 24px; }\n  .dropdown-icon-item span {\n    display: block;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap; }\n  .dropdown-icon-item:hover {\n    border-color: #eff2f7; }\n\n.fullscreen-enable [data-toggle=\"fullscreen\"] .bx-fullscreen::before {\n  content: \"\\ea3f\"; }\n\nbody[data-topbar=\"dark\"] #page-topbar {\n  background-color: #2a3042; }\n\nbody[data-topbar=\"dark\"] .navbar-header .dropdown.show .header-item {\n  background-color: rgba(255, 255, 255, 0.05); }\n\nbody[data-topbar=\"dark\"] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\nbody[data-topbar=\"dark\"] .header-item {\n  color: #e9ecef; }\n  body[data-topbar=\"dark\"] .header-item:hover {\n    color: #e9ecef; }\n\nbody[data-topbar=\"dark\"] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25); }\n\nbody[data-topbar=\"dark\"] .noti-icon i {\n  color: #e9ecef; }\n\nbody[data-topbar=\"dark\"] .logo-dark {\n  display: none; }\n\nbody[data-topbar=\"dark\"] .logo-light {\n  display: block; }\n\nbody[data-topbar=\"dark\"] .app-search .form-control {\n  background-color: rgba(243, 243, 249, 0.07);\n  color: #fff; }\n\nbody[data-topbar=\"dark\"] .app-search span,\nbody[data-topbar=\"dark\"] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5); }\n\nbody[data-sidebar=\"dark\"] .navbar-brand-box {\n  background: #2a3042; }\n\nbody[data-sidebar=\"dark\"] .logo-dark {\n  display: none; }\n\nbody[data-sidebar=\"dark\"] .logo-light {\n  display: block; }\n\n@media (max-width: 600px) {\n  .navbar-header .dropdown {\n    position: static; }\n    .navbar-header .dropdown .dropdown-menu {\n      left: 10px !important;\n      right: 10px !important; } }\n\n@media (max-width: 380px) {\n  .navbar-brand-box {\n    display: none; } }\n\nbody[data-layout=\"horizontal\"] .navbar-brand-box {\n  width: auto; }\n\nbody[data-layout=\"horizontal\"] .page-content {\n  margin-top: 70px;\n  padding: calc(55px + 24px) calc(24px / 2) 60px calc(24px / 2); }\n\n@media (max-width: 992px) {\n  body[data-layout=\"horizontal\"] .page-content {\n    margin-top: 15px; } }\n\n.page-title-box {\n  padding-bottom: 24px; }\n  .page-title-box .breadcrumb {\n    background-color: transparent;\n    padding: 0; }\n  .page-title-box h4 {\n    text-transform: uppercase;\n    font-weight: 600;\n    font-size: 16px !important; }\n\n.footer {\n  bottom: 0;\n  padding: 20px calc(24px / 2);\n  position: absolute;\n  right: 0;\n  color: #74788d;\n  left: 250px;\n  height: 60px;\n  background-color: #f2f2f5; }\n\n@media (max-width: 992px) {\n  .footer {\n    left: 0; } }\n\n.vertical-collpsed .footer {\n  left: 70px; }\n\nbody[data-layout=\"horizontal\"] .footer {\n  left: 0 !important; }\n\n.right-bar {\n  background-color: #fff;\n  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n  display: block;\n  position: fixed;\n  transition: all 200ms ease-out;\n  width: 280px;\n  z-index: 9999;\n  float: right !important;\n  right: -290px;\n  top: 0;\n  bottom: 0; }\n  .right-bar .right-bar-toggle {\n    background-color: #444c54;\n    height: 24px;\n    width: 24px;\n    line-height: 24px;\n    color: #eff2f7;\n    text-align: center;\n    border-radius: 50%; }\n    .right-bar .right-bar-toggle:hover {\n      background-color: #4b545c; }\n\n.rightbar-overlay {\n  background-color: rgba(52, 58, 64, 0.55);\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  display: none;\n  z-index: 9998;\n  transition: all .2s ease-out; }\n\n.right-bar-enabled .right-bar {\n  right: 0; }\n\n.right-bar-enabled .rightbar-overlay {\n  display: block; }\n\n@media (max-width: 767.98px) {\n  .right-bar {\n    overflow: auto; }\n    .right-bar .slimscroll-menu {\n      height: auto !important; } }\n\n.metismenu {\n  margin: 0; }\n  .metismenu li {\n    display: block;\n    width: 100%; }\n  .metismenu .mm-collapse {\n    display: none; }\n    .metismenu .mm-collapse:not(.mm-show) {\n      display: none; }\n    .metismenu .mm-collapse.mm-show {\n      display: block; }\n  .metismenu .mm-collapsing {\n    position: relative;\n    height: 0;\n    overflow: hidden;\n    transition-timing-function: ease;\n    transition-duration: .35s;\n    transition-property: height, visibility; }\n\n.vertical-menu {\n  width: 250px;\n  z-index: 1001;\n  background: #ffffff;\n  bottom: 0;\n  margin-top: 0;\n  position: fixed;\n  top: 70px;\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03); }\n\n.main-content {\n  margin-left: 250px;\n  overflow: hidden; }\n  .main-content .content {\n    padding: 0 15px 10px 15px;\n    margin-top: 70px; }\n\n#sidebar-menu {\n  padding: 10px 0 30px 0; }\n  #sidebar-menu .mm-active > .has-arrow:after {\n    transform: rotate(-180deg); }\n  #sidebar-menu .has-arrow:after {\n    content: \"\\F0140\";\n    font-family: 'Material Design Icons';\n    display: block;\n    float: right;\n    transition: transform .2s;\n    font-size: 1rem; }\n  #sidebar-menu ul li a {\n    display: block;\n    padding: .625rem 1.5rem;\n    color: #545a6d;\n    position: relative;\n    font-size: 13px;\n    transition: all .4s; }\n    #sidebar-menu ul li a i {\n      display: inline-block;\n      min-width: 1.75rem;\n      padding-bottom: .125em;\n      font-size: 1.25rem;\n      line-height: 1.40625rem;\n      vertical-align: middle;\n      color: #7f8387;\n      transition: all .4s; }\n    #sidebar-menu ul li a:hover {\n      color: #383c40; }\n      #sidebar-menu ul li a:hover i {\n        color: #383c40; }\n  #sidebar-menu ul li .badge {\n    margin-top: 4px; }\n  #sidebar-menu ul li ul.sub-menu {\n    padding: 0; }\n    #sidebar-menu ul li ul.sub-menu li a {\n      padding: .4rem 1.5rem .4rem 3.5rem;\n      font-size: 13px;\n      color: #545a6d; }\n    #sidebar-menu ul li ul.sub-menu li ul.sub-menu {\n      padding: 0; }\n      #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n        padding: .4rem 1.5rem .4rem 4.5rem;\n        font-size: 13.5px; }\n\n.menu-title {\n  padding: 12px 20px !important;\n  letter-spacing: .05em;\n  pointer-events: none;\n  cursor: default;\n  font-size: 11px;\n  text-transform: uppercase;\n  color: #7f8387;\n  font-weight: 600; }\n\n.mm-active {\n  color: #556ee6 !important; }\n  .mm-active > a {\n    color: #556ee6 !important; }\n    .mm-active > a i {\n      color: #556ee6 !important; }\n  .mm-active .active {\n    color: #556ee6 !important; }\n    .mm-active .active i {\n      color: #556ee6 !important; }\n  .mm-active > i {\n    color: #556ee6 !important; }\n\n@media (max-width: 992px) {\n  .vertical-menu {\n    display: none; }\n  .main-content {\n    margin-left: 0 !important; }\n  body.sidebar-enable .vertical-menu {\n    display: block; } }\n\n.vertical-collpsed .main-content {\n  margin-left: 70px; }\n\n.vertical-collpsed .navbar-brand-box {\n  width: 70px !important; }\n\n.vertical-collpsed .logo span.logo-lg {\n  display: none; }\n\n.vertical-collpsed .logo span.logo-sm {\n  display: block; }\n\n.vertical-collpsed .vertical-menu {\n  position: absolute;\n  width: 70px !important;\n  z-index: 5; }\n  .vertical-collpsed .vertical-menu .simplebar-mask,\n  .vertical-collpsed .vertical-menu .simplebar-content-wrapper {\n    overflow: visible !important; }\n  .vertical-collpsed .vertical-menu .simplebar-scrollbar {\n    display: none !important; }\n  .vertical-collpsed .vertical-menu .simplebar-offset {\n    bottom: 0 !important; }\n  .vertical-collpsed .vertical-menu #sidebar-menu .menu-title,\n  .vertical-collpsed .vertical-menu #sidebar-menu .badge,\n  .vertical-collpsed .vertical-menu #sidebar-menu .collapse.in {\n    display: none !important; }\n  .vertical-collpsed .vertical-menu #sidebar-menu .nav.collapse {\n    height: inherit !important; }\n  .vertical-collpsed .vertical-menu #sidebar-menu .has-arrow:after {\n    display: none; }\n  .vertical-collpsed .vertical-menu #sidebar-menu > ul > li {\n    position: relative;\n    white-space: nowrap; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a {\n      padding: 15px 20px;\n      min-height: 55px;\n      transition: none; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:hover, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:active, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:focus {\n        color: #383c40; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n        font-size: 1.45rem;\n        margin-left: 4px; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a span {\n        display: none;\n        padding-left: 25px; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n      position: relative;\n      width: calc(190px + 70px);\n      color: #556ee6;\n      background-color: whitesmoke;\n      transition: none; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n        color: #556ee6; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a span {\n        display: inline; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul {\n      display: block;\n      left: 70px;\n      position: absolute;\n      width: 190px;\n      height: auto !important;\n      box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1); }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul ul {\n        box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1); }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n        box-shadow: none;\n        padding: 8px 20px;\n        position: relative;\n        width: 190px;\n        z-index: 6;\n        color: #545a6d; }\n        .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n          color: #383c40; }\n  .vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n    padding: 5px 0;\n    z-index: 9999;\n    display: none;\n    background-color: #ffffff; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li:hover > ul {\n      display: block;\n      left: 190px;\n      height: auto !important;\n      margin-top: -36px;\n      position: absolute;\n      width: 190px; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li > a span.pull-right {\n      position: absolute;\n      right: 20px;\n      top: 12px;\n      transform: rotate(270deg); }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li.active a {\n      color: #f8f9fa; }\n\nbody[data-sidebar=\"dark\"] .vertical-menu {\n  background: #2a3042; }\n\nbody[data-sidebar=\"dark\"] #sidebar-menu ul li a {\n  color: #a6b0cf; }\n  body[data-sidebar=\"dark\"] #sidebar-menu ul li a i {\n    color: #6a7187; }\n  body[data-sidebar=\"dark\"] #sidebar-menu ul li a:hover {\n    color: #ffffff; }\n    body[data-sidebar=\"dark\"] #sidebar-menu ul li a:hover i {\n      color: #ffffff; }\n\nbody[data-sidebar=\"dark\"] #sidebar-menu ul li ul.sub-menu li a {\n  color: #79829c; }\n  body[data-sidebar=\"dark\"] #sidebar-menu ul li ul.sub-menu li a:hover {\n    color: #ffffff; }\n\nbody[data-sidebar=\"dark\"].vertical-collpsed {\n  min-height: 1760px; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n    background: #2e3548;\n    color: #ffffff; }\n    body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n      color: #ffffff; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n    color: #79829c; }\n    body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n      color: #ffffff; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n    background-color: #2a3042; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul li.mm-active .active {\n    color: #ffffff !important; }\n    body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul li.mm-active .active i {\n      color: #ffffff !important; }\n\nbody[data-sidebar=\"dark\"] .mm-active {\n  color: #ffffff !important; }\n  body[data-sidebar=\"dark\"] .mm-active > a {\n    color: #ffffff !important; }\n    body[data-sidebar=\"dark\"] .mm-active > a i {\n      color: #ffffff !important; }\n  body[data-sidebar=\"dark\"] .mm-active > i {\n    color: #ffffff !important; }\n  body[data-sidebar=\"dark\"] .mm-active .active {\n    color: #ffffff !important; }\n    body[data-sidebar=\"dark\"] .mm-active .active i {\n      color: #ffffff !important; }\n\nbody[data-sidebar=\"dark\"] .menu-title {\n  color: #6a7187; }\n\nbody[data-layout=\"horizontal\"] .main-content {\n  margin-left: 0 !important; }\n\nbody[data-sidebar-size=\"small\"] .navbar-brand-box {\n  width: 160px; }\n\nbody[data-sidebar-size=\"small\"] .vertical-menu {\n  width: 160px;\n  text-align: center; }\n  body[data-sidebar-size=\"small\"] .vertical-menu .has-arrow:after,\n  body[data-sidebar-size=\"small\"] .vertical-menu .badge {\n    display: none !important; }\n\nbody[data-sidebar-size=\"small\"] .main-content {\n  margin-left: 160px; }\n\nbody[data-sidebar-size=\"small\"] .footer {\n  left: 160px; }\n\nbody[data-sidebar-size=\"small\"] #sidebar-menu ul li.menu-title {\n  background-color: #2e3548; }\n\nbody[data-sidebar-size=\"small\"] #sidebar-menu ul li a i {\n  display: block; }\n\nbody[data-sidebar-size=\"small\"] #sidebar-menu ul li ul.sub-menu li a {\n  padding-left: 1.5rem; }\n\nbody[data-sidebar-size=\"small\"] #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding-left: 1.5rem; }\n\nbody[data-sidebar-size=\"small\"].vertical-collpsed .main-content {\n  margin-left: 70px; }\n\nbody[data-sidebar-size=\"small\"].vertical-collpsed .vertical-menu #sidebar-menu {\n  text-align: left; }\n  body[data-sidebar-size=\"small\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n    display: inline-block; }\n\nbody[data-sidebar-size=\"small\"].vertical-collpsed .footer {\n  left: 70px; }\n\nbody[data-sidebar=\"colored\"] .vertical-menu {\n  background-color: #556ee6; }\n\nbody[data-sidebar=\"colored\"] .navbar-brand-box {\n  background-color: #556ee6; }\n  body[data-sidebar=\"colored\"] .navbar-brand-box .logo-dark {\n    display: none; }\n  body[data-sidebar=\"colored\"] .navbar-brand-box .logo-light {\n    display: block; }\n\nbody[data-sidebar=\"colored\"] .mm-active {\n  color: #fff !important; }\n  body[data-sidebar=\"colored\"] .mm-active > a {\n    color: #fff !important; }\n    body[data-sidebar=\"colored\"] .mm-active > a i {\n      color: #fff !important; }\n  body[data-sidebar=\"colored\"] .mm-active > i, body[data-sidebar=\"colored\"] .mm-active .active {\n    color: #fff !important; }\n\nbody[data-sidebar=\"colored\"] #sidebar-menu ul li.menu-title {\n  color: rgba(255, 255, 255, 0.6); }\n\nbody[data-sidebar=\"colored\"] #sidebar-menu ul li a {\n  color: rgba(255, 255, 255, 0.6); }\n  body[data-sidebar=\"colored\"] #sidebar-menu ul li a i {\n    color: rgba(255, 255, 255, 0.6); }\n  body[data-sidebar=\"colored\"] #sidebar-menu ul li a.waves-effect .waves-ripple {\n    background: rgba(255, 255, 255, 0.1); }\n  body[data-sidebar=\"colored\"] #sidebar-menu ul li a:hover {\n    color: #fff; }\n    body[data-sidebar=\"colored\"] #sidebar-menu ul li a:hover i {\n      color: #fff; }\n\nbody[data-sidebar=\"colored\"] #sidebar-menu ul li ul.sub-menu li a {\n  color: rgba(255, 255, 255, 0.5); }\n  body[data-sidebar=\"colored\"] #sidebar-menu ul li ul.sub-menu li a:hover {\n    color: #fff; }\n\nbody[data-sidebar=\"colored\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  background-color: #5e76e7;\n  color: #fff; }\n  body[data-sidebar=\"colored\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n    color: #fff; }\n\nbody[data-sidebar=\"colored\"].vertical-collpsed .vertical-menu #sidebar-menu ul li.mm-active .active {\n  color: #556ee6 !important; }\n\nbody[data-sidebar=\"colored\"].vertical-collpsed .vertical-menu #sidebar-menu ul li ul.sub-menu li a:hover {\n  color: #556ee6; }\n\nbody[data-sidebar=\"colored\"].vertical-collpsed .vertical-menu #sidebar-menu ul li ul.sub-menu li.mm-active {\n  color: #556ee6 !important; }\n  body[data-sidebar=\"colored\"].vertical-collpsed .vertical-menu #sidebar-menu ul li ul.sub-menu li.mm-active > a {\n    color: #556ee6 !important; }\n    body[data-sidebar=\"colored\"].vertical-collpsed .vertical-menu #sidebar-menu ul li ul.sub-menu li.mm-active > a i {\n      color: #556ee6 !important; }\n\n.topnav {\n  background: #fff;\n  padding: 0 calc(24px / 2);\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n  margin-top: 70px;\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 100; }\n  .topnav .topnav-menu {\n    margin: 0;\n    padding: 0; }\n  .topnav .navbar-nav .nav-link {\n    font-size: 14px;\n    position: relative;\n    padding: 1rem 1.3rem;\n    color: #545a6d; }\n    .topnav .navbar-nav .nav-link i {\n      font-size: 15px; }\n    .topnav .navbar-nav .nav-link:focus, .topnav .navbar-nav .nav-link:hover {\n      color: #556ee6;\n      background-color: transparent; }\n  .topnav .navbar-nav .dropdown-item {\n    color: #545a6d; }\n    .topnav .navbar-nav .dropdown-item.active, .topnav .navbar-nav .dropdown-item:hover {\n      color: #556ee6; }\n  .topnav .navbar-nav .nav-item .nav-link.active {\n    color: #556ee6; }\n  .topnav .navbar-nav .dropdown.active > a {\n    color: #556ee6;\n    background-color: transparent; }\n\n@media (min-width: 1200px) {\n  body[data-layout=\"horizontal\"] .container-fluid,\n  body[data-layout=\"horizontal\"] .navbar-header {\n    max-width: 85%; } }\n\n@media (min-width: 992px) {\n  .topnav .navbar-nav .nav-item:first-of-type .nav-link {\n    padding-left: 0; }\n  .topnav .dropdown-item {\n    padding: .5rem 1.5rem;\n    min-width: 180px; }\n  .topnav .dropdown.mega-dropdown .mega-dropdown-menu {\n    left: 0px;\n    right: auto; }\n  .topnav .dropdown .dropdown-menu {\n    margin-top: 0;\n    border-radius: 0 0 0.25rem 0.25rem; }\n    .topnav .dropdown .dropdown-menu .arrow-down::after {\n      right: 15px;\n      transform: rotate(-135deg) translateY(-50%);\n      position: absolute; }\n    .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {\n      position: absolute;\n      top: 0 !important;\n      left: 100%;\n      display: none; }\n  .topnav .dropdown:hover > .dropdown-menu {\n    display: block; }\n  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-menu {\n    display: block; }\n  .navbar-toggle {\n    display: none; } }\n\n.arrow-down {\n  display: inline-block; }\n  .arrow-down:after {\n    border-color: initial;\n    border-style: solid;\n    border-width: 0 0 1px 1px;\n    content: \"\";\n    height: .4em;\n    display: inline-block;\n    right: 5px;\n    top: 50%;\n    margin-left: 10px;\n    transform: rotate(-45deg) translateY(-50%);\n    transform-origin: top;\n    transition: all .3s ease-out;\n    width: .4em; }\n\n@media (max-width: 1199.98px) {\n  .topnav-menu .navbar-nav li:last-of-type .dropdown .dropdown-menu {\n    right: 100%;\n    left: auto; } }\n\n@media (max-width: 991.98px) {\n  .navbar-brand-box .logo-dark {\n    display: block; }\n    .navbar-brand-box .logo-dark span.logo-sm {\n      display: block; }\n  .navbar-brand-box .logo-light {\n    display: none; }\n  .topnav {\n    max-height: 360px;\n    overflow-y: auto;\n    padding: 0; }\n    .topnav .navbar-nav .nav-link {\n      padding: 0.75rem 1.1rem; }\n    .topnav .dropdown .dropdown-menu {\n      background-color: transparent;\n      border: none;\n      box-shadow: none;\n      padding-left: 15px; }\n      .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl {\n        width: auto; }\n        .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl .row {\n          margin: 0px; }\n    .topnav .dropdown .dropdown-item {\n      position: relative;\n      background-color: transparent; }\n      .topnav .dropdown .dropdown-item.active, .topnav .dropdown .dropdown-item:active {\n        color: #556ee6; }\n    .topnav .arrow-down::after {\n      right: 15px;\n      position: absolute; } }\n\n@media (min-width: 992px) {\n  body[data-layout=\"horizontal\"][data-topbar=\"light\"] .navbar-brand-box .logo-dark {\n    display: block; }\n  body[data-layout=\"horizontal\"][data-topbar=\"light\"] .navbar-brand-box .logo-light {\n    display: none; }\n  body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav {\n    background-color: #556ee6; }\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav .nav-link {\n      color: rgba(255, 255, 255, 0.6); }\n      body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav .nav-link:focus, body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav .nav-link:hover {\n        color: rgba(255, 255, 255, 0.9); }\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav > .dropdown.active > a {\n      color: rgba(255, 255, 255, 0.9) !important; } }\n\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] #page-topbar {\n  background-color: #556ee6;\n  box-shadow: none; }\n\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] .logo-dark {\n  display: none; }\n\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] .logo-light {\n  display: block; }\n\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] .app-search .form-control {\n  background-color: rgba(243, 243, 249, 0.07);\n  color: #fff; }\n\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] .app-search span,\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5); }\n\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] .header-item {\n  color: #e9ecef; }\n  body[data-layout=\"horizontal\"][data-topbar=\"colored\"] .header-item:hover {\n    color: #e9ecef; }\n\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] .navbar-header .dropdown.show .header-item {\n  background-color: rgba(255, 255, 255, 0.1); }\n\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] .noti-icon i {\n  color: #e9ecef; }\n\n@media (min-width: 992px) {\n  body[data-layout=\"horizontal\"][data-topbar=\"colored\"] .topnav {\n    background-color: #556ee6; }\n    body[data-layout=\"horizontal\"][data-topbar=\"colored\"] .topnav .navbar-nav .nav-link {\n      color: rgba(255, 255, 255, 0.6); }\n      body[data-layout=\"horizontal\"][data-topbar=\"colored\"] .topnav .navbar-nav .nav-link:focus, body[data-layout=\"horizontal\"][data-topbar=\"colored\"] .topnav .navbar-nav .nav-link:hover {\n        color: rgba(255, 255, 255, 0.9); }\n    body[data-layout=\"horizontal\"][data-topbar=\"colored\"] .topnav .navbar-nav > .dropdown.active > a {\n      color: rgba(255, 255, 255, 0.9) !important; } }\n\nbody[data-layout-size=\"boxed\"] {\n  background-color: #ebebf4; }\n  body[data-layout-size=\"boxed\"] #layout-wrapper {\n    background-color: #f8f8fb;\n    max-width: 1300px;\n    margin: 0 auto;\n    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03); }\n  body[data-layout-size=\"boxed\"] #page-topbar {\n    max-width: 1300px;\n    margin: 0 auto; }\n  body[data-layout-size=\"boxed\"] .footer {\n    margin: 0 auto;\n    max-width: calc(1300px - 250px); }\n  body[data-layout-size=\"boxed\"].vertical-collpsed .footer {\n    max-width: calc(1300px - 70px); }\n\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] #page-topbar, body[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] #layout-wrapper, body[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] .footer {\n  max-width: 100%; }\n\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] .container-fluid, body[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] .navbar-header {\n  max-width: 1300px; }\n\n@media (min-width: 992px) {\n  body[data-layout-scrollable=\"true\"] #page-topbar, body[data-layout-scrollable=\"true\"] .vertical-menu {\n    position: absolute; } }\n\n@media (min-width: 992px) {\n  body[data-layout-scrollable=\"true\"][data-layout=\"horizontal\"] #page-topbar, body[data-layout-scrollable=\"true\"][data-layout=\"horizontal\"] .topnav {\n    position: absolute; } }\n\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent; }\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none; }\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2); }\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important; }\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1; }\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em; }\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em; }\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom; }\n\n.waves-input-wrapper.waves-button {\n  padding: 0; }\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1; }\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%; }\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms; }\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }\n\n.waves-block {\n  display: block; }\n\n.waves-effect.waves-light .waves-ripple {\n  background-color: rgba(255, 255, 255, 0.4); }\n\n.waves-effect.waves-primary .waves-ripple {\n  background-color: rgba(85, 110, 230, 0.4); }\n\n.waves-effect.waves-success .waves-ripple {\n  background-color: rgba(52, 195, 143, 0.4); }\n\n.waves-effect.waves-info .waves-ripple {\n  background-color: rgba(80, 165, 241, 0.4); }\n\n.waves-effect.waves-warning .waves-ripple {\n  background-color: rgba(241, 180, 76, 0.4); }\n\n.waves-effect.waves-danger .waves-ripple {\n  background-color: rgba(244, 106, 106, 0.4); }\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem; }\n\n.avatar-sm {\n  height: 3rem;\n  width: 3rem; }\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem; }\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem; }\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem; }\n\n.avatar-title {\n  align-items: center;\n  background-color: #556ee6;\n  color: #fff;\n  display: flex;\n  font-weight: 500;\n  height: 100%;\n  justify-content: center;\n  width: 100%; }\n\n.custom-accordion .accordion-list {\n  display: flex;\n  border-radius: 7px;\n  background-color: #f6f6f6;\n  padding: 12px 20px;\n  color: #495057;\n  font-weight: 600;\n  align-items: center;\n  justify-content: space-between; }\n  .custom-accordion .accordion-list.collapsed i.accor-plus-icon:before {\n    content: \"\\F0415\"; }\n  .custom-accordion .accordion-list .accor-plus-icon {\n    display: inline-block;\n    font-size: 16px;\n    height: 24px;\n    width: 24px;\n    line-height: 22px;\n    background-color: #fff;\n    text-align: center;\n    border-radius: 50%; }\n\n.custom-accordion a.collapsed i.accor-down-icon:before {\n  content: \"\\F0140\"; }\n\n.custom-accordion .card-body {\n  color: #74788d; }\n\n.font-size-10 {\n  font-size: 10px !important; }\n\n.font-size-11 {\n  font-size: 11px !important; }\n\n.font-size-12 {\n  font-size: 12px !important; }\n\n.font-size-13 {\n  font-size: 13px !important; }\n\n.font-size-14 {\n  font-size: 14px !important; }\n\n.font-size-15 {\n  font-size: 15px !important; }\n\n.font-size-16 {\n  font-size: 16px !important; }\n\n.font-size-17 {\n  font-size: 17px !important; }\n\n.font-size-18 {\n  font-size: 18px !important; }\n\n.font-size-20 {\n  font-size: 20px !important; }\n\n.font-size-22 {\n  font-size: 22px !important; }\n\n.font-size-24 {\n  font-size: 24px !important; }\n\n.font-weight-medium {\n  font-weight: 500; }\n\n.font-weight-semibold {\n  font-weight: 600; }\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 4px);\n  display: block;\n  border: 2px solid #adb5bd;\n  border-radius: 50%;\n  color: #adb5bd;\n  text-align: center;\n  transition: all 0.4s; }\n  .social-list-item:hover {\n    color: #74788d;\n    background-color: #eff2f7; }\n\n.w-xs {\n  min-width: 80px; }\n\n.w-sm {\n  min-width: 95px; }\n\n.w-md {\n  min-width: 110px; }\n\n.w-lg {\n  min-width: 140px; }\n\n.w-xl {\n  min-width: 160px; }\n\n#preloader {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  z-index: 9999; }\n\n#status {\n  width: 40px;\n  height: 40px;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  margin: -20px 0 0 -20px; }\n\n.spinner-chase {\n  margin: 0 auto;\n  width: 40px;\n  height: 40px;\n  position: relative;\n  animation: spinner-chase 2.5s infinite linear both; }\n\n.chase-dot {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  left: 0;\n  top: 0;\n  animation: chase-dot 2.0s infinite ease-in-out both; }\n  .chase-dot:before {\n    content: '';\n    display: block;\n    width: 25%;\n    height: 25%;\n    background-color: #556ee6;\n    border-radius: 100%;\n    animation: chase-dot-before 2.0s infinite ease-in-out both; }\n  .chase-dot:nth-child(1) {\n    animation-delay: -1.1s; }\n    .chase-dot:nth-child(1):before {\n      animation-delay: -1.1s; }\n  .chase-dot:nth-child(2) {\n    animation-delay: -1.0s; }\n    .chase-dot:nth-child(2):before {\n      animation-delay: -1.0s; }\n  .chase-dot:nth-child(3) {\n    animation-delay: -0.9s; }\n    .chase-dot:nth-child(3):before {\n      animation-delay: -0.9s; }\n  .chase-dot:nth-child(4) {\n    animation-delay: -0.8s; }\n    .chase-dot:nth-child(4):before {\n      animation-delay: -0.8s; }\n  .chase-dot:nth-child(5) {\n    animation-delay: -0.7s; }\n    .chase-dot:nth-child(5):before {\n      animation-delay: -0.7s; }\n  .chase-dot:nth-child(6) {\n    animation-delay: -0.6s; }\n    .chase-dot:nth-child(6):before {\n      animation-delay: -0.6s; }\n\n@keyframes spinner-chase {\n  100% {\n    transform: rotate(360deg); } }\n\n@keyframes chase-dot {\n  80%, 100% {\n    transform: rotate(360deg); } }\n\n@keyframes chase-dot-before {\n  50% {\n    transform: scale(0.4); }\n  100%, 0% {\n    transform: scale(1); } }\n\n.form-check-right {\n  padding-left: 0;\n  display: inline-block;\n  padding-right: 1.25rem; }\n  .form-check-right .form-check-input {\n    right: 0;\n    margin-left: 0; }\n  .form-check-right .form-check-label {\n    display: block; }\n\n.custom-control-right {\n  padding-left: 0;\n  padding-right: 1.5rem;\n  display: inline-block; }\n  .custom-control-right .custom-control-label {\n    display: inline-block; }\n    .custom-control-right .custom-control-label:before, .custom-control-right .custom-control-label:after {\n      left: auto;\n      right: -1.5rem; }\n  .custom-control-right .custom-control-input {\n    left: auto; }\n\n.custom-checkbox-outline .custom-control-label::before {\n  border-width: 2px; }\n\n.custom-checkbox-outline .custom-control-input:checked ~ .custom-control-label:after {\n  background-image: none;\n  content: '\\F012C';\n  font-family: \"Material Design Icons\";\n  font-size: 20px;\n  top: -8px;\n  left: -22px; }\n\n.custom-checkbox-outline .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: transparent !important; }\n\n.custom-radio-outline .custom-control-input:checked ~ .custom-control-label:after {\n  background-image: none;\n  content: '\\F0765';\n  font-family: \"Material Design Icons\";\n  font-size: 8px;\n  top: 4px;\n  left: -20px; }\n\n.custom-radio-outline .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: transparent !important; }\n\n.custom-checkbox-primary .custom-control-input:checked ~ .custom-control-label:before, .custom-radio-primary .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: #556ee6;\n  border-color: #556ee6; }\n\n.custom-radio-outline.custom-radio-primary .custom-control-input:checked ~ .custom-control-label:after {\n  color: #556ee6; }\n\n.custom-checkbox-secondary .custom-control-input:checked ~ .custom-control-label:before, .custom-radio-secondary .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: #74788d;\n  border-color: #74788d; }\n\n.custom-radio-outline.custom-radio-secondary .custom-control-input:checked ~ .custom-control-label:after {\n  color: #74788d; }\n\n.custom-checkbox-success .custom-control-input:checked ~ .custom-control-label:before, .custom-radio-success .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: #34c38f;\n  border-color: #34c38f; }\n\n.custom-radio-outline.custom-radio-success .custom-control-input:checked ~ .custom-control-label:after {\n  color: #34c38f; }\n\n.custom-checkbox-info .custom-control-input:checked ~ .custom-control-label:before, .custom-radio-info .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: #50a5f1;\n  border-color: #50a5f1; }\n\n.custom-radio-outline.custom-radio-info .custom-control-input:checked ~ .custom-control-label:after {\n  color: #50a5f1; }\n\n.custom-checkbox-warning .custom-control-input:checked ~ .custom-control-label:before, .custom-radio-warning .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: #f1b44c;\n  border-color: #f1b44c; }\n\n.custom-radio-outline.custom-radio-warning .custom-control-input:checked ~ .custom-control-label:after {\n  color: #f1b44c; }\n\n.custom-checkbox-danger .custom-control-input:checked ~ .custom-control-label:before, .custom-radio-danger .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: #f46a6a;\n  border-color: #f46a6a; }\n\n.custom-radio-outline.custom-radio-danger .custom-control-input:checked ~ .custom-control-label:after {\n  color: #f46a6a; }\n\n.custom-checkbox-pink .custom-control-input:checked ~ .custom-control-label:before, .custom-radio-pink .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: #e83e8c;\n  border-color: #e83e8c; }\n\n.custom-radio-outline.custom-radio-pink .custom-control-input:checked ~ .custom-control-label:after {\n  color: #e83e8c; }\n\n.custom-checkbox-light .custom-control-input:checked ~ .custom-control-label:before, .custom-radio-light .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: #eff2f7;\n  border-color: #eff2f7; }\n\n.custom-radio-outline.custom-radio-light .custom-control-input:checked ~ .custom-control-label:after {\n  color: #eff2f7; }\n\n.custom-checkbox-dark .custom-control-input:checked ~ .custom-control-label:before, .custom-radio-dark .custom-control-input:checked ~ .custom-control-label:before {\n  background-color: #343a40;\n  border-color: #343a40; }\n\n.custom-radio-outline.custom-radio-dark .custom-control-input:checked ~ .custom-control-label:after {\n  color: #343a40; }\n\n.custom-checkbox-dark .custom-control-input:checked ~ .custom-control-label:before, .custom-radio-dark .custom-control-input:checked ~ .custom-control-label:before {\n  color: #343a40; }\n\n.custom-control-label {\n  cursor: pointer; }\n\n.custom-switch-md {\n  padding-left: 3rem; }\n  .custom-switch-md .custom-control-label {\n    line-height: 20px; }\n    .custom-switch-md .custom-control-label:before {\n      width: 40px;\n      height: 20px;\n      border-radius: 30px;\n      left: -3rem; }\n    .custom-switch-md .custom-control-label:after {\n      width: calc(20px - 4px);\n      height: calc(20px - 4px);\n      left: calc(-3rem + 2px); }\n  .custom-switch-md .custom-control-input:checked ~ .custom-control-label::after {\n    transform: translateX(1.25rem); }\n\n.custom-switch-lg {\n  padding-left: 3.75rem; }\n  .custom-switch-lg .custom-control-label {\n    line-height: 24px; }\n    .custom-switch-lg .custom-control-label:before {\n      width: 48px;\n      height: 24px;\n      border-radius: 30px;\n      left: -3.75rem; }\n    .custom-switch-lg .custom-control-label:after {\n      width: calc(24px - 4px);\n      height: calc(24px - 4px);\n      left: calc(-3.75rem + 2px);\n      border-radius: 50%; }\n  .custom-switch-lg .custom-control-input:checked ~ .custom-control-label::after {\n    transform: translateX(1.5rem); }\n\n.custom-control-label::before {\n  background-color: #fff; }\n\n.mini-stats-wid .mini-stat-icon {\n  overflow: hidden;\n  position: relative; }\n  .mini-stats-wid .mini-stat-icon:before, .mini-stats-wid .mini-stat-icon:after {\n    content: \"\";\n    position: absolute;\n    width: 8px;\n    height: 54px;\n    background-color: rgba(255, 255, 255, 0.1);\n    left: 16px;\n    transform: rotate(32deg);\n    top: -5px;\n    transition: all 0.4s; }\n  .mini-stats-wid .mini-stat-icon::after {\n    left: -12px;\n    width: 12px;\n    transition: all 0.2s; }\n\n.mini-stats-wid:hover .mini-stat-icon::after {\n  left: 60px; }\n\n.button-items {\n  margin-left: -8px;\n  margin-bottom: -12px; }\n  .button-items .btn {\n    margin-bottom: 12px;\n    margin-left: 8px; }\n\n.mfp-popup-form {\n  max-width: 1140px; }\n\n.bs-example-modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block; }\n\n.icon-demo-content {\n  text-align: center;\n  color: #adb5bd; }\n  .icon-demo-content i {\n    display: block;\n    font-size: 24px;\n    margin-bottom: 16px;\n    color: #74788d;\n    transition: all 0.4s; }\n  .icon-demo-content .col-lg-4 {\n    margin-top: 24px; }\n    .icon-demo-content .col-lg-4:hover i {\n      color: #556ee6;\n      transform: scale(1.5); }\n\n.grid-structure .grid-container {\n  background-color: #f8f9fa;\n  margin-top: 10px;\n  font-size: .8rem;\n  font-weight: 500;\n  padding: 10px 20px; }\n\n.card-radio {\n  background-color: #fff;\n  border: 2px solid #f6f6f6;\n  border-radius: 0.25rem;\n  padding: 1rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap; }\n  .card-radio:hover {\n    cursor: pointer; }\n\n.card-radio-label {\n  display: block; }\n\n.card-radio-input {\n  display: none; }\n  .card-radio-input:checked + .card-radio {\n    border-color: #556ee6 !important; }\n\n.navs-carousel .owl-nav {\n  margin-top: 16px; }\n  .navs-carousel .owl-nav button {\n    width: 30px;\n    height: 30px;\n    line-height: 28px !important;\n    font-size: 20px !important;\n    border-radius: 50% !important;\n    background-color: rgba(85, 110, 230, 0.25) !important;\n    color: #556ee6 !important;\n    margin: 4px 8px !important; }\n\n@media print {\n  .vertical-menu,\n  .right-bar,\n  .page-title-box,\n  .navbar-header,\n  .footer {\n    display: none !important; }\n  .card-body,\n  .main-content,\n  .right-bar,\n  .page-content,\n  body {\n    padding: 0;\n    margin: 0; }\n  .card {\n    border: 0; } }\n\n[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start; }\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit; }\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0; }\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch; }\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%;\n  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto;\n  /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%;\n  /* Not required for horizontal scroll to trigger */\n  max-height: 100%;\n  /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important; }\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none; }\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table; }\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none; }\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0; }\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1; }\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden; }\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none; }\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all; }\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 4px;\n  min-height: 10px; }\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear; }\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear; }\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px; }\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px; }\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px; }\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px; }\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto; }\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0; }\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll; }\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none; }\n\n.custom-scroll {\n  height: 100%; }\n\n/* ==============\r\n  Calendar\r\n===================*/\n.lnb-calendars-item {\n  display: inline-block;\n  margin-right: 7px; }\n\n.tui-full-calendar-layout, .tui-full-calendar-timegrid-timezone {\n  background-color: #fff !important; }\n\n.tui-full-calendar-dayname-container,\n.tui-full-calendar-left,\n.tui-full-calendar-splitter,\n.tui-full-calendar-time-date,\n.tui-full-calendar-weekday-grid-line,\n.tui-full-calendar-timegrid-timezone,\n.tui-full-calendar-timegrid-gridline {\n  border-color: #f6f6f6 !important; }\n\n.tui-full-calendar-weekday-exceed-in-week {\n  text-align: center;\n  width: 30px;\n  height: 30px;\n  line-height: 28px;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #495057;\n  border-color: #eff2f7; }\n\n.tui-full-calendar-timegrid-hour {\n  color: #495057 !important; }\n\n.tui-full-calendar-weekday-schedule-title, .tui-full-calendar-time-schedule {\n  font-weight: 600; }\n\n/* ==============\r\n  Druafula\r\n===================*/\n.task-box {\n  border: 1px solid #f6f6f6; }\n\n.gu-transit {\n  border: 1px dashed #74788d !important;\n  background-color: #eff2f7 !important; }\n\n#session-timeout-dialog .close {\n  display: none; }\n\n#session-timeout-dialog .countdown-holder {\n  color: #f46a6a;\n  font-weight: 500; }\n\n#session-timeout-dialog .btn-default {\n  background-color: #fff;\n  color: #f46a6a;\n  box-shadow: none; }\n\n.irs--square .irs-bar, .irs--square .irs-to, .irs--square .irs-from, .irs--square .irs-single {\n  background: #556ee6 !important;\n  font-size: 11px; }\n\n.irs--square .irs-to:before, .irs--square .irs-from:before, .irs--square .irs-single:before {\n  border-top-color: #556ee6; }\n\n.irs--square .irs-line {\n  background: #f6f6f6;\n  border-color: #f6f6f6; }\n\n.irs--square .irs-grid-text {\n  font-size: 11px;\n  color: #ced4da; }\n\n.irs--square .irs-min, .irs--square .irs-max {\n  color: #ced4da;\n  background: #f6f6f6;\n  font-size: 11px; }\n\n.irs--square .irs-handle {\n  border: 2px solid #556ee6;\n  width: 12px;\n  height: 12px;\n  top: 26px;\n  background-color: #fff !important; }\n\n.swal2-container .swal2-title {\n  font-size: 20px;\n  font-weight: 500; }\n\n.swal2-modal {\n  font-size: 14px; }\n\n.swal2-icon.swal2-question {\n  border-color: #50a5f1;\n  color: #50a5f1; }\n\n.swal2-icon.swal2-success [class^=swal2-success-line] {\n  background-color: #34c38f; }\n\n.swal2-icon.swal2-success .swal2-success-ring {\n  border-color: rgba(52, 195, 143, 0.3); }\n\n.swal2-icon.swal2-warning {\n  border-color: #f1b44c;\n  color: #f1b44c; }\n\n.swal2-styled:focus {\n  box-shadow: none; }\n\n.swal2-progress-steps .swal2-progress-step {\n  background: #556ee6; }\n  .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {\n    background: #556ee6; }\n    .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step, .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {\n      background: rgba(85, 110, 230, 0.3); }\n\n.swal2-progress-steps .swal2-progress-step-line {\n  background: #556ee6; }\n\n.swal2-loader {\n  border-color: #556ee6 transparent #556ee6 transparent; }\n\n.symbol {\n  border-color: #fff; }\n\n.rating-symbol-background, .rating-symbol-foreground {\n  font-size: 24px; }\n\n.rating-symbol-foreground {\n  top: 0px; }\n\n/* =============\r\n   Notification\r\n============= */\n#toast-container > div {\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n  opacity: 1; }\n  #toast-container > div:hover {\n    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n    opacity: 0.9; }\n\n#toast-container.toast-top-full-width > div, #toast-container.toast-bottom-full-width > div {\n  min-width: 96%;\n  margin: 4px auto; }\n\n.toast-primary {\n  border: 2px solid #556ee6 !important;\n  background-color: rgba(85, 110, 230, 0.8) !important; }\n\n.toast-secondary {\n  border: 2px solid #74788d !important;\n  background-color: rgba(116, 120, 141, 0.8) !important; }\n\n.toast-success {\n  border: 2px solid #34c38f !important;\n  background-color: rgba(52, 195, 143, 0.8) !important; }\n\n.toast-info {\n  border: 2px solid #50a5f1 !important;\n  background-color: rgba(80, 165, 241, 0.8) !important; }\n\n.toast-warning {\n  border: 2px solid #f1b44c !important;\n  background-color: rgba(241, 180, 76, 0.8) !important; }\n\n.toast-danger {\n  border: 2px solid #f46a6a !important;\n  background-color: rgba(244, 106, 106, 0.8) !important; }\n\n.toast-pink {\n  border: 2px solid #e83e8c !important;\n  background-color: rgba(232, 62, 140, 0.8) !important; }\n\n.toast-light {\n  border: 2px solid #eff2f7 !important;\n  background-color: rgba(239, 242, 247, 0.8) !important; }\n\n.toast-dark {\n  border: 2px solid #343a40 !important;\n  background-color: rgba(52, 58, 64, 0.8) !important; }\n\n.toast-error {\n  background-color: rgba(244, 106, 106, 0.8);\n  border: 2px solid #f46a6a; }\n\n.toastr-options {\n  padding: 24px;\n  background-color: #f6f8fa;\n  margin-bottom: 0;\n  border: 1px solid #eff2f7; }\n\n/* ==============\r\n  Cropperjs\r\n===================*/\n.image-crop-preview .img-preview {\n  float: left;\n  margin-bottom: .5rem;\n  margin-right: .5rem;\n  overflow: hidden;\n  background-color: #f6f6f6;\n  text-align: center;\n  width: 100%; }\n  .image-crop-preview .img-preview > img {\n    max-width: 100%; }\n\n.image-crop-preview .preview-lg {\n  height: 9rem;\n  width: 16rem; }\n\n.image-crop-preview .preview-md {\n  height: 4.5rem;\n  width: 8rem; }\n\n.image-crop-preview .preview-sm {\n  height: 2.25rem;\n  width: 4rem; }\n\n.image-crop-preview .preview-xs {\n  height: 1.125rem;\n  margin-right: 0;\n  width: 2rem; }\n\n.img-crop-preview-btns .btn .docs-tooltip, .img-crop-preview-toggles .btn .docs-tooltip {\n  display: block;\n  margin: -0.47rem -0.75rem;\n  padding: 0.47rem 0.75rem; }\n\n.error {\n  color: #f46a6a; }\n\n.parsley-error {\n  border-color: #f46a6a; }\n\n.parsley-errors-list {\n  display: none;\n  margin: 0;\n  padding: 0; }\n  .parsley-errors-list.filled {\n    display: block; }\n  .parsley-errors-list > li {\n    font-size: 12px;\n    list-style: none;\n    color: #f46a6a;\n    margin-top: 5px; }\n\n.select2-container .select2-selection--single {\n  background-color: #fff;\n  border: 1px solid #ced4da;\n  height: 38px; }\n  .select2-container .select2-selection--single:focus {\n    outline: none; }\n  .select2-container .select2-selection--single .select2-selection__rendered {\n    line-height: 36px;\n    padding-left: 12px;\n    color: #495057; }\n  .select2-container .select2-selection--single .select2-selection__arrow {\n    height: 34px;\n    width: 34px;\n    right: 3px; }\n    .select2-container .select2-selection--single .select2-selection__arrow b {\n      border-color: #adb5bd transparent transparent transparent;\n      border-width: 6px 6px 0 6px; }\n  .select2-container .select2-selection--single .select2-selection__placeholder {\n    color: #495057; }\n\n.select2-container--open .select2-selection--single .select2-selection__arrow b {\n  border-color: transparent transparent #adb5bd transparent !important;\n  border-width: 0 6px 6px 6px !important; }\n\n.select2-container--default .select2-search--dropdown {\n  padding: 10px;\n  background-color: #fff; }\n  .select2-container--default .select2-search--dropdown .select2-search__field {\n    border: 1px solid #ced4da;\n    background-color: #fff;\n    color: #74788d;\n    outline: none; }\n\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: #556ee6; }\n\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #f8f9fa;\n  color: #16181b; }\n  .select2-container--default .select2-results__option[aria-selected=true]:hover {\n    background-color: #556ee6;\n    color: #fff; }\n\n.select2-results__option {\n  padding: 6px 12px; }\n\n.select2-dropdown {\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  background-color: #fff;\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03); }\n\n.select2-search input {\n  border: 1px solid #f6f6f6; }\n\n.select2-container .select2-selection--multiple {\n  min-height: 38px;\n  background-color: #fff;\n  border: 1px solid #ced4da !important; }\n  .select2-container .select2-selection--multiple .select2-selection__rendered {\n    padding: 2px 10px; }\n  .select2-container .select2-selection--multiple .select2-search__field {\n    border: 0;\n    color: #495057; }\n    .select2-container .select2-selection--multiple .select2-search__field::placeholder {\n      color: #495057; }\n  .select2-container .select2-selection--multiple .select2-selection__choice {\n    background-color: #eff2f7;\n    border: 1px solid #f6f6f6;\n    border-radius: 1px;\n    padding: 0 7px; }\n\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #ced4da; }\n\n.select2-container--default .select2-results__group {\n  font-weight: 600; }\n\n.select2-result-repository__avatar {\n  float: left;\n  width: 60px;\n  margin-right: 10px; }\n  .select2-result-repository__avatar img {\n    width: 100%;\n    height: auto;\n    border-radius: 2px; }\n\n.select2-result-repository__statistics {\n  margin-top: 7px; }\n\n.select2-result-repository__forks,\n.select2-result-repository__stargazers,\n.select2-result-repository__watchers {\n  display: inline-block;\n  font-size: 11px;\n  margin-right: 1em;\n  color: #adb5bd; }\n  .select2-result-repository__forks .fa,\n  .select2-result-repository__stargazers .fa,\n  .select2-result-repository__watchers .fa {\n    margin-right: 4px; }\n    .select2-result-repository__forks .fa.fa-flash::before,\n    .select2-result-repository__stargazers .fa.fa-flash::before,\n    .select2-result-repository__watchers .fa.fa-flash::before {\n      content: \"\\f0e7\";\n      font-family: 'Font Awesome 5 Free'; }\n\n.select2-results__option--highlighted .select2-result-repository__forks,\n.select2-results__option--highlighted .select2-result-repository__stargazers,\n.select2-results__option--highlighted .select2-result-repository__watchers {\n  color: rgba(255, 255, 255, 0.8); }\n\n.select2-result-repository__meta {\n  overflow: hidden; }\n\n.img-flag {\n  margin-right: 7px;\n  height: 15px;\n  width: 18px; }\n\n/* CSS Switch */\ninput[switch] {\n  display: none; }\n  input[switch] + label {\n    font-size: 1em;\n    line-height: 1;\n    width: 56px;\n    height: 24px;\n    background-color: #ced4da;\n    background-image: none;\n    border-radius: 2rem;\n    padding: 0.16667rem;\n    cursor: pointer;\n    display: inline-block;\n    text-align: center;\n    position: relative;\n    font-weight: 500;\n    transition: all 0.1s ease-in-out; }\n    input[switch] + label:before {\n      color: #343a40;\n      content: attr(data-off-label);\n      display: block;\n      font-family: inherit;\n      font-weight: 500;\n      font-size: 12px;\n      line-height: 21px;\n      position: absolute;\n      right: 1px;\n      margin: 3px;\n      top: -2px;\n      text-align: center;\n      min-width: 1.66667rem;\n      overflow: hidden;\n      transition: all 0.1s ease-in-out; }\n    input[switch] + label:after {\n      content: '';\n      position: absolute;\n      left: 3px;\n      background-color: #eff2f7;\n      box-shadow: none;\n      border-radius: 2rem;\n      height: 20px;\n      width: 20px;\n      top: 2px;\n      transition: all 0.1s ease-in-out; }\n  input[switch]:checked + label {\n    background-color: #556ee6; }\n\ninput[switch]:checked + label {\n  background-color: #556ee6; }\n  input[switch]:checked + label:before {\n    color: #fff;\n    content: attr(data-on-label);\n    right: auto;\n    left: 3px; }\n  input[switch]:checked + label:after {\n    left: 33px;\n    background-color: #eff2f7; }\n\ninput[switch=\"bool\"] + label {\n  background-color: #f46a6a; }\n\ninput[switch=\"bool\"] + label:before, input[switch=\"bool\"]:checked + label:before,\ninput[switch=\"default\"]:checked + label:before {\n  color: #fff; }\n\ninput[switch=\"bool\"]:checked + label {\n  background-color: #34c38f; }\n\ninput[switch=\"default\"]:checked + label {\n  background-color: #a2a2a2; }\n\ninput[switch=\"primary\"]:checked + label {\n  background-color: #556ee6; }\n\ninput[switch=\"success\"]:checked + label {\n  background-color: #34c38f; }\n\ninput[switch=\"info\"]:checked + label {\n  background-color: #50a5f1; }\n\ninput[switch=\"warning\"]:checked + label {\n  background-color: #f1b44c; }\n\ninput[switch=\"danger\"]:checked + label {\n  background-color: #f46a6a; }\n\ninput[switch=\"dark\"]:checked + label {\n  background-color: #343a40; }\n\n.square-switch {\n  margin-right: 7px; }\n  .square-switch input[switch] + label, .square-switch input[switch] + label:after {\n    border-radius: 4px; }\n\n/* Timepicker */\n.bootstrap-timepicker-widget table td a {\n  color: #495057; }\n  .bootstrap-timepicker-widget table td a:hover {\n    background-color: transparent;\n    border-color: transparent;\n    border-radius: 4px;\n    color: #556ee6;\n    text-decoration: none; }\n\n.bootstrap-timepicker-widget table td input {\n  width: 32px;\n  height: 32px;\n  border: 0;\n  color: #495057;\n  border: 1px solid #eff2f7;\n  background-color: #fff; }\n\n.bootstrap-timepicker-widget.dropdown-menu:after {\n  border-bottom-color: #eff2f7; }\n\n.bootstrap-timepicker-widget.timepicker-orient-bottom:after {\n  border-top-color: #eff2f7; }\n\n.datepicker {\n  border: 1px solid #eff2f7;\n  padding: 8px;\n  z-index: 999 !important; }\n  .datepicker table tr th {\n    font-weight: 500; }\n  .datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td .active.disabled, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.selected, .datepicker table tr td.selected:hover, .datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover {\n    background-color: #556ee6 !important;\n    background-image: none;\n    box-shadow: none;\n    color: #fff !important; }\n  .datepicker table tr td.day.focused, .datepicker table tr td.day:hover,\n  .datepicker table tr td span.focused,\n  .datepicker table tr td span:hover {\n    background: #eff2f7; }\n  .datepicker table tr td.new, .datepicker table tr td.old,\n  .datepicker table tr td span.new,\n  .datepicker table tr td span.old {\n    color: #adb5bd;\n    opacity: 0.6; }\n  .datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {\n    background-color: #f6f6f6; }\n\n.table-condensed > thead > tr > th, .table-condensed > tbody > tr > td {\n  padding: 7px; }\n\n.bootstrap-datepicker-inline .datepicker-inline {\n  width: auto !important;\n  display: inline-block; }\n\n.datepicker-container {\n  border: 1px solid #eff2f7;\n  box-shadow: none;\n  background-color: #fff; }\n  .datepicker-container.datepicker-inline {\n    width: 212px; }\n\n.datepicker-panel > ul > li {\n  background-color: #fff;\n  border-radius: 4px; }\n  .datepicker-panel > ul > li.picked, .datepicker-panel > ul > li.picked:hover {\n    background-color: rgba(85, 110, 230, 0.25);\n    color: #556ee6; }\n  .datepicker-panel > ul > li.highlighted, .datepicker-panel > ul > li.highlighted:hover, .datepicker-panel > ul > li:hover {\n    background-color: #556ee6;\n    color: #fff; }\n  .datepicker-panel > ul > li.muted, .datepicker-panel > ul > li.muted:hover {\n    color: #adb5bd;\n    opacity: 0.6; }\n\n.datepicker-panel > ul[data-view=week] > li {\n  font-weight: 500; }\n\n.datepicker-panel > ul[data-view=week] > li, .datepicker-panel > ul[data-view=week] > li:hover {\n  background-color: #fff; }\n\n.tox-tinymce {\n  border: 1px solid #ced4da !important; }\n\n.tox .tox-statusbar {\n  border-top: 1px solid #eff2f7 !important; }\n\n.tox .tox-menubar, .tox .tox-edit-area__iframe, .tox .tox-statusbar {\n  background-color: #fff !important;\n  background: none !important; }\n\n.tox .tox-mbtn {\n  color: #495057 !important; }\n  .tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {\n    background-color: #eff2f7 !important; }\n\n.tox .tox-tbtn:hover {\n  background-color: #eff2f7 !important; }\n\n.tox .tox-toolbar, .tox .tox-toolbar__overflow, .tox .tox-toolbar__primary {\n  background: #eff2f7 !important; }\n\n.tox .tox-toolbar__primary {\n  border-top-color: #eff2f7 !important; }\n\n.tox .tox-tbtn {\n  color: #495057 !important; }\n  .tox .tox-tbtn svg {\n    fill: #495057 !important; }\n\n.tox .tox-edit-area__iframe {\n  background-color: #fff !important; }\n\n.tox .tox-statusbar a, .tox .tox-statusbar__path-item, .tox .tox-statusbar__wordcount {\n  color: #495057 !important; }\n\n.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\n  border-right: 1px solid #dee4ef !important; }\n\n.note-editor.note-frame {\n  border: 1px solid #ced4da;\n  box-shadow: none;\n  margin: 0; }\n  .note-editor.note-frame .note-statusbar {\n    background-color: #eff2f7;\n    border-top: 1px solid #eff2f7; }\n  .note-editor.note-frame .note-editing-area .note-editable, .note-editor.note-frame .note-editing-area .note-codable {\n    border: none;\n    color: #adb5bd;\n    background-color: transparent; }\n\n.note-btn-group .note-btn {\n  background-color: #f6f6f6 !important;\n  border-color: #f6f6f6 !important; }\n  .note-btn-group .note-btn.dropdown-toggle:after {\n    display: inline-block;\n    margin-left: .255em;\n    vertical-align: .255em;\n    content: \"\";\n    border-top: .3em solid;\n    border-right: .3em solid transparent;\n    border-bottom: 0;\n    border-left: .3em solid transparent; }\n\n.note-btn-group .note-btn {\n  background-color: #eff2f7 !important;\n  border-color: #eff2f7 !important; }\n\n.note-status-output {\n  display: none; }\n\n.note-editable p:last-of-type {\n  margin-bottom: 0; }\n\n.note-popover .popover-content .note-color .dropdown-menu,\n.card-header.note-toolbar .note-color .dropdown-menu {\n  min-width: 344px; }\n\n.note-popover {\n  border-color: #eff2f7; }\n\n.note-popover .popover-content,\n.card-header.note-toolbar {\n  background-color: #eff2f7; }\n\n.note-toolbar {\n  padding: 0 0 5px 5px !important; }\n\n/* Dropzone */\n.dropzone {\n  min-height: 230px;\n  border: 2px dashed #ced4da;\n  background: #fff;\n  border-radius: 6px; }\n  .dropzone .dz-message {\n    font-size: 24px;\n    width: 100%; }\n\n.twitter-bs-wizard .twitter-bs-wizard-nav {\n  padding: 4px;\n  background-color: rgba(85, 110, 230, 0.1); }\n  .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n    display: inline-block;\n    width: 38px;\n    height: 38px;\n    line-height: 34px;\n    border: 2px solid #556ee6;\n    color: #556ee6;\n    text-align: center;\n    border-radius: 50%; }\n    @media (max-width: 991.98px) {\n      .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n        display: block;\n        margin: 0 auto 8px !important; } }\n  @media (min-width: 992px) {\n    .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link {\n      text-align: left; } }\n  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active {\n    background-color: transparent;\n    color: #495057; }\n    .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active .step-number {\n      background-color: #556ee6;\n      color: #fff; }\n\n.twitter-bs-wizard .twitter-bs-wizard-pager-link {\n  padding-top: 24px;\n  padding-left: 0;\n  list-style: none;\n  margin-bottom: 0; }\n  .twitter-bs-wizard .twitter-bs-wizard-pager-link li {\n    display: inline-block; }\n    .twitter-bs-wizard .twitter-bs-wizard-pager-link li a {\n      display: inline-block;\n      padding: .47rem .75rem;\n      background-color: #556ee6;\n      color: #fff;\n      border-radius: .25rem; }\n    .twitter-bs-wizard .twitter-bs-wizard-pager-link li.disabled a {\n      cursor: not-allowed;\n      background-color: #798ceb; }\n    .twitter-bs-wizard .twitter-bs-wizard-pager-link li.next {\n      float: right; }\n\n.twitter-bs-wizard-tab-content {\n  padding-top: 24px;\n  min-height: 262px; }\n\n.table-rep-plugin .btn-toolbar {\n  display: block; }\n\n.table-rep-plugin .table-responsive {\n  border: none !important; }\n\n.table-rep-plugin .btn-group .btn-default {\n  background-color: #74788d;\n  color: #eff2f7;\n  border: 1px solid #74788d; }\n  .table-rep-plugin .btn-group .btn-default.btn-primary {\n    background-color: #556ee6;\n    border-color: #556ee6;\n    color: #fff;\n    box-shadow: 0 0 0 2px rgba(85, 110, 230, 0.5); }\n\n.table-rep-plugin .btn-group.pull-right {\n  float: right; }\n  .table-rep-plugin .btn-group.pull-right .dropdown-menu {\n    right: 0;\n    transform: none !important;\n    top: 100% !important; }\n\n.table-rep-plugin tbody th {\n  font-size: 14px;\n  font-weight: normal; }\n\n.table-rep-plugin .checkbox-row {\n  padding-left: 40px;\n  color: #495057 !important; }\n  .table-rep-plugin .checkbox-row:hover {\n    background-color: #f6f8fa !important; }\n  .table-rep-plugin .checkbox-row label {\n    display: inline-block;\n    padding-left: 5px;\n    position: relative; }\n    .table-rep-plugin .checkbox-row label::before {\n      -o-transition: 0.3s ease-in-out;\n      -webkit-transition: 0.3s ease-in-out;\n      background-color: #fff;\n      border-radius: 3px;\n      border: 1px solid #f6f6f6;\n      content: \"\";\n      display: inline-block;\n      height: 17px;\n      left: 0;\n      margin-left: -20px;\n      position: absolute;\n      transition: 0.3s ease-in-out;\n      width: 17px;\n      outline: none !important; }\n    .table-rep-plugin .checkbox-row label::after {\n      color: #eff2f7;\n      display: inline-block;\n      font-size: 11px;\n      height: 16px;\n      left: 0;\n      margin-left: -20px;\n      padding-left: 3px;\n      padding-top: 1px;\n      position: absolute;\n      top: -1px;\n      width: 16px; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"] {\n    cursor: pointer;\n    opacity: 0;\n    z-index: 1;\n    outline: none !important; }\n    .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label {\n      opacity: 0.65; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:focus + label::before {\n    outline-offset: -2px;\n    outline: none; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    content: \"\\f00c\";\n    font-family: 'Font Awesome 5 Free';\n    font-weight: 900; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label::before {\n    background-color: #f8f9fa;\n    cursor: not-allowed; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::before {\n    background-color: #556ee6;\n    border-color: #556ee6; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    color: #fff; }\n\n.table-rep-plugin .fixed-solution .sticky-table-header {\n  top: 70px !important;\n  background-color: #556ee6; }\n  .table-rep-plugin .fixed-solution .sticky-table-header table {\n    color: #fff; }\n\n.table-rep-plugin table.focus-on tbody tr.focused th,\n.table-rep-plugin table.focus-on tbody tr.focused td,\n.table-rep-plugin .sticky-table-header {\n  background: #556ee6;\n  border-color: #556ee6;\n  color: #fff; }\n  .table-rep-plugin table.focus-on tbody tr.focused th table,\n  .table-rep-plugin table.focus-on tbody tr.focused td table,\n  .table-rep-plugin .sticky-table-header table {\n    color: #fff; }\n\n@media (min-width: 992px) {\n  body[data-layout=\"horizontal\"] .fixed-solution .sticky-table-header {\n    top: 120px !important; } }\n\n.table-editable .editable-input .form-control {\n  height: 2rem; }\n\n.table-editable a.editable {\n  color: #495057; }\n\n.table-editable .editable-buttons .btn.btn-sm {\n  font-size: 12px; }\n\n.table-editable tbody td.focus {\n  box-shadow: inset 0 0 1px 1px #556ee6 !important; }\n\n.glyphicon {\n  display: inline-block;\n  font-family: \"Material Design Icons\";\n  font-size: inherit;\n  font-weight: 600;\n  font-style: inherit; }\n\n.glyphicon-ok:before {\n  content: \"\\F012C\"; }\n\n.glyphicon-remove:before {\n  content: \"\\F0156\"; }\n\n.dt-autofill-list {\n  border: none !important;\n  background-color: #fff !important; }\n  .dt-autofill-list .dt-autofill-question, .dt-autofill-list .dt-autofill-button {\n    border-bottom-color: #f6f6f6 !important; }\n  .dt-autofill-list ul li:hover {\n    background-color: #f6f6f6 !important; }\n\n.apex-charts {\n  min-height: 10px !important; }\n  .apex-charts text {\n    font-family: \"Poppins\", sans-serif !important;\n    fill: #adb5bd; }\n  .apex-charts .apexcharts-canvas {\n    margin: 0 auto; }\n\n.apexcharts-tooltip-title,\n.apexcharts-tooltip-text {\n  font-family: \"Poppins\", sans-serif !important; }\n\n.apexcharts-legend-series {\n  font-weight: 500; }\n\n.apexcharts-gridline {\n  pointer-events: none;\n  stroke: #f8f9fa; }\n\n.apexcharts-legend-text {\n  color: #74788d !important;\n  font-family: \"Poppins\", sans-serif !important;\n  font-size: 13px !important; }\n\n.apexcharts-pie-label {\n  fill: #fff !important; }\n\n.apexcharts-yaxis text,\n.apexcharts-xaxis text {\n  font-family: \"Poppins\", sans-serif !important;\n  fill: #adb5bd; }\n\n.e-charts {\n  height: 350px; }\n\n/* Flot chart */\n.flot-charts-height {\n  height: 320px; }\n\n.flotTip {\n  padding: 8px 12px;\n  background-color: rgba(52, 58, 64, 0.9);\n  z-index: 100;\n  color: #f8f9fa;\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n  border-radius: 4px; }\n\n.legendLabel {\n  color: #adb5bd; }\n\n.jqstooltip {\n  box-sizing: content-box;\n  width: auto !important;\n  height: auto !important;\n  background-color: #343a40 !important;\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  padding: 5px 10px !important;\n  border-radius: 3px;\n  border-color: #212529 !important; }\n\n.jqsfield {\n  color: #eff2f7 !important;\n  font-size: 12px !important;\n  line-height: 18px !important;\n  font-family: \"Poppins\", sans-serif !important;\n  font-weight: 500 !important; }\n\n.gmaps, .gmaps-panaroma {\n  height: 300px;\n  background: #f8f9fa;\n  border-radius: 3px; }\n\n.gmaps-overlay {\n  display: block;\n  text-align: center;\n  color: #fff;\n  font-size: 16px;\n  line-height: 40px;\n  background: #556ee6;\n  border-radius: 4px;\n  padding: 10px 20px; }\n\n.gmaps-overlay_arrow {\n  left: 50%;\n  margin-left: -16px;\n  width: 0;\n  height: 0;\n  position: absolute; }\n  .gmaps-overlay_arrow.above {\n    bottom: -15px;\n    border-left: 16px solid transparent;\n    border-right: 16px solid transparent;\n    border-top: 16px solid #556ee6; }\n  .gmaps-overlay_arrow.below {\n    top: -15px;\n    border-left: 16px solid transparent;\n    border-right: 16px solid transparent;\n    border-bottom: 16px solid #556ee6; }\n\n.jvectormap-label {\n  border: none;\n  background: #343a40;\n  color: #f8f9fa;\n  font-family: \"Poppins\", sans-serif;\n  font-size: 0.8125rem;\n  padding: 5px 8px; }\n\n.leaflet-map {\n  height: 300px; }\n  .leaflet-map.leaflet-container {\n    z-index: 99; }\n\n.home-btn {\n  position: absolute;\n  top: 15px;\n  right: 25px; }\n\n.auth-logo .auth-logo-dark {\n  display: block; }\n\n.auth-logo .auth-logo-light {\n  display: none; }\n\n.auth-body-bg {\n  background-color: #fff; }\n\n.auth-full-bg {\n  background-color: rgba(85, 110, 230, 0.25);\n  display: flex; }\n  @media (min-width: 1200px) {\n    .auth-full-bg {\n      height: 100vh; } }\n  .auth-full-bg::before {\n    content: \"\";\n    position: absolute;\n    width: 300px;\n    height: 300px;\n    border-radius: 50%; }\n  .auth-full-bg .bg-overlay {\n    background: url(\"../images/bg-auth-overlay.png\");\n    background-size: cover;\n    background-repeat: no-repeat;\n    background-position: center; }\n\n.auth-full-page-content {\n  display: flex; }\n  @media (min-width: 1200px) {\n    .auth-full-page-content {\n      min-height: 100vh; } }\n\n.auth-review-carousel.owl-theme .owl-dots .owl-dot span {\n  background-color: rgba(85, 110, 230, 0.25); }\n\n.auth-review-carousel.owl-theme .owl-dots .owl-dot.active span, .auth-review-carousel.owl-theme .owl-dots .owl-dot:hover span {\n  background-color: #556ee6; }\n\n.search-box .form-control {\n  border-radius: 30px;\n  padding-left: 40px; }\n\n.search-box .search-icon {\n  font-size: 16px;\n  position: absolute;\n  left: 13px;\n  top: 0;\n  line-height: 38px; }\n\n.product-list li a {\n  display: block;\n  padding: 4px 0px;\n  color: #495057; }\n\n.product-view-nav.nav-pills .nav-item {\n  margin-left: 4px; }\n\n.product-view-nav.nav-pills .nav-link {\n  width: 36px;\n  height: 36px;\n  font-size: 16px;\n  padding: 0;\n  line-height: 36px;\n  text-align: center;\n  border-radius: 50%; }\n\n.product-ribbon {\n  position: absolute;\n  right: 0px;\n  top: 0px; }\n\n.product-detai-imgs .nav .nav-link {\n  margin: 7px 0px; }\n  .product-detai-imgs .nav .nav-link.active {\n    background-color: #f6f6f6; }\n\n.product-color a {\n  display: inline-block;\n  text-align: center;\n  color: #495057; }\n  .product-color a .product-color-item {\n    margin: 7px; }\n  .product-color a.active, .product-color a:hover {\n    color: #556ee6; }\n    .product-color a.active .product-color-item, .product-color a:hover .product-color-item {\n      border-color: #556ee6 !important; }\n\n.visa-card .visa-logo {\n  line-height: 0.5; }\n\n.visa-card .visa-pattern {\n  position: absolute;\n  font-size: 385px;\n  color: rgba(255, 255, 255, 0.05);\n  line-height: 0.4;\n  right: 0px;\n  bottom: 0px; }\n\n.checkout-tabs .nav-pills .nav-link {\n  margin-bottom: 24px;\n  text-align: center;\n  background-color: #fff;\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03); }\n  .checkout-tabs .nav-pills .nav-link.active {\n    background-color: #556ee6; }\n  .checkout-tabs .nav-pills .nav-link .check-nav-icon {\n    font-size: 36px; }\n\n/* ==============\r\n  Email\r\n===================*/\n.email-leftbar {\n  width: 236px;\n  float: left;\n  padding: 20px;\n  border-radius: 5px; }\n\n.email-rightbar {\n  margin-left: 260px; }\n\n.chat-user-box p.user-title {\n  color: #343a40;\n  font-weight: 500; }\n\n.chat-user-box p {\n  font-size: 12px; }\n\n@media (max-width: 767px) {\n  .email-leftbar {\n    float: none;\n    width: 100%; }\n  .email-rightbar {\n    margin: 0; } }\n\n.mail-list a {\n  display: block;\n  color: #74788d;\n  line-height: 24px;\n  padding: 8px 5px; }\n  .mail-list a.active {\n    color: #f46a6a;\n    font-weight: 500; }\n\n.message-list {\n  display: block;\n  padding-left: 0; }\n  .message-list li {\n    position: relative;\n    display: block;\n    height: 50px;\n    line-height: 50px;\n    cursor: default;\n    transition-duration: .3s; }\n    .message-list li a {\n      color: #74788d; }\n    .message-list li:hover {\n      background: #f6f6f6;\n      transition-duration: .05s; }\n    .message-list li .col-mail {\n      float: left;\n      position: relative; }\n    .message-list li .col-mail-1 {\n      width: 320px; }\n      .message-list li .col-mail-1 .star-toggle,\n      .message-list li .col-mail-1 .checkbox-wrapper-mail,\n      .message-list li .col-mail-1 .dot {\n        display: block;\n        float: left; }\n      .message-list li .col-mail-1 .dot {\n        border: 4px solid transparent;\n        border-radius: 100px;\n        margin: 22px 26px 0;\n        height: 0;\n        width: 0;\n        line-height: 0;\n        font-size: 0; }\n      .message-list li .col-mail-1 .checkbox-wrapper-mail {\n        margin: 15px 10px 0 20px; }\n      .message-list li .col-mail-1 .star-toggle {\n        margin-top: 18px;\n        margin-left: 5px; }\n      .message-list li .col-mail-1 .title {\n        position: absolute;\n        top: 0;\n        left: 110px;\n        right: 0;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n        margin-bottom: 0; }\n    .message-list li .col-mail-2 {\n      position: absolute;\n      top: 0;\n      left: 320px;\n      right: 0;\n      bottom: 0; }\n      .message-list li .col-mail-2 .subject,\n      .message-list li .col-mail-2 .date {\n        position: absolute;\n        top: 0; }\n      .message-list li .col-mail-2 .subject {\n        left: 0;\n        right: 200px;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap; }\n      .message-list li .col-mail-2 .date {\n        right: 0;\n        width: 170px;\n        padding-left: 80px; }\n    .message-list li.active, .message-list li.active:hover {\n      box-shadow: inset 3px 0 0 #556ee6; }\n    .message-list li.unread {\n      background-color: #f6f6f6;\n      font-weight: 500;\n      color: #292d32; }\n      .message-list li.unread a {\n        color: #292d32;\n        font-weight: 500; }\n  .message-list .checkbox-wrapper-mail {\n    cursor: pointer;\n    height: 20px;\n    width: 20px;\n    position: relative;\n    display: inline-block;\n    box-shadow: inset 0 0 0 1px #ced4da;\n    border-radius: 1px; }\n    .message-list .checkbox-wrapper-mail input {\n      opacity: 0;\n      cursor: pointer; }\n    .message-list .checkbox-wrapper-mail input:checked ~ label {\n      opacity: 1; }\n    .message-list .checkbox-wrapper-mail label {\n      position: absolute;\n      height: 20px;\n      width: 20px;\n      left: 0;\n      cursor: pointer;\n      opacity: 0;\n      margin-bottom: 0;\n      transition-duration: .05s;\n      top: 0; }\n      .message-list .checkbox-wrapper-mail label:before {\n        content: \"\\F012C\";\n        font-family: \"Material Design Icons\";\n        top: 0;\n        height: 20px;\n        color: #292d32;\n        width: 20px;\n        position: absolute;\n        margin-top: -16px;\n        left: 4px;\n        font-size: 13px; }\n\n@media (max-width: 575.98px) {\n  .message-list li .col-mail-1 {\n    width: 200px; } }\n\n@media (min-width: 1200px) {\n  .filemanager-sidebar {\n    min-width: 230px;\n    max-width: 230px; } }\n\n@media (min-width: 1366px) {\n  .filemanager-sidebar {\n    min-width: 280px;\n    max-width: 280px; } }\n\n.categories-list {\n  padding: 4px 0; }\n  .categories-list li a {\n    display: block;\n    padding: 8px 12px;\n    color: #495057;\n    font-weight: 500; }\n  .categories-list li.active a {\n    color: #556ee6; }\n  .categories-list li ul {\n    padding-left: 16px; }\n    .categories-list li ul li a {\n      padding: 4px 12px;\n      color: #74788d;\n      font-size: 13px;\n      font-weight: 400; }\n\n@media (min-width: 992px) {\n  .chat-leftsidebar {\n    min-width: 380px; } }\n\n.chat-leftsidebar .chat-leftsidebar-nav .nav {\n  background-color: #fff; }\n\n.chat-leftsidebar .chat-leftsidebar-nav .tab-content {\n  min-height: 488px; }\n\n.chat-noti-dropdown.active:before {\n  content: \"\";\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background-color: #f46a6a;\n  border-radius: 50%;\n  right: 0; }\n\n.chat-noti-dropdown .btn {\n  padding: 6px;\n  box-shadow: none;\n  font-size: 20px; }\n\n.chat-search-box .form-control {\n  border: 0; }\n\n.chat-list {\n  margin: 0; }\n  .chat-list li.active a {\n    background-color: #fff;\n    border-color: transparent;\n    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03); }\n  .chat-list li a {\n    display: block;\n    padding: 14px 16px;\n    color: #74788d;\n    transition: all 0.4s;\n    border-top: 1px solid #eff2f7;\n    border-radius: 4px; }\n    .chat-list li a:hover {\n      background-color: #fff;\n      border-color: transparent;\n      box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03); }\n\n.user-chat-nav .dropdown .nav-btn {\n  height: 40px;\n  width: 40px;\n  line-height: 40px;\n  box-shadow: none;\n  padding: 0;\n  font-size: 16px;\n  background-color: #eff2f7;\n  border-radius: 50%; }\n\n.user-chat-nav .dropdown .dropdown-menu {\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n  border: 1px solid #eff2f7; }\n\n.chat-conversation li {\n  clear: both; }\n\n.chat-conversation .chat-day-title {\n  position: relative;\n  text-align: center;\n  margin-bottom: 24px; }\n  .chat-conversation .chat-day-title .title {\n    background-color: #fff;\n    position: relative;\n    z-index: 1;\n    padding: 6px 24px; }\n  .chat-conversation .chat-day-title:before {\n    content: \"\";\n    position: absolute;\n    width: 100%;\n    height: 1px;\n    left: 0;\n    right: 0;\n    background-color: #eff2f7;\n    top: 10px; }\n  .chat-conversation .chat-day-title .badge {\n    font-size: 12px; }\n\n.chat-conversation .conversation-list {\n  margin-bottom: 24px;\n  display: inline-block;\n  position: relative; }\n  .chat-conversation .conversation-list .ctext-wrap {\n    padding: 12px 24px;\n    background-color: rgba(85, 110, 230, 0.1);\n    border-radius: 8px 8px 8px 0px;\n    overflow: hidden; }\n    .chat-conversation .conversation-list .ctext-wrap .conversation-name {\n      font-weight: 600;\n      color: #556ee6;\n      margin-bottom: 4px; }\n  .chat-conversation .conversation-list .dropdown {\n    float: right; }\n    .chat-conversation .conversation-list .dropdown .dropdown-toggle {\n      font-size: 18px;\n      padding: 4px;\n      color: #74788d; }\n      @media (max-width: 575.98px) {\n        .chat-conversation .conversation-list .dropdown .dropdown-toggle {\n          display: none; } }\n    .chat-conversation .conversation-list .dropdown .dropdown-menu {\n      box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n      border: 1px solid #eff2f7; }\n  .chat-conversation .conversation-list .chat-time {\n    font-size: 12px; }\n\n.chat-conversation .right .conversation-list {\n  float: right; }\n  .chat-conversation .right .conversation-list .ctext-wrap {\n    background-color: #eff2f7;\n    text-align: right;\n    border-radius: 8px 8px 0px 8px; }\n  .chat-conversation .right .conversation-list .dropdown {\n    float: left; }\n  .chat-conversation .right .conversation-list.last-chat .conversation-list:before {\n    right: 0;\n    left: auto; }\n\n.chat-conversation .last-chat .conversation-list:before {\n  content: \"\\F0009\";\n  font-family: \"Material Design Icons\";\n  position: absolute;\n  color: #556ee6;\n  right: 0;\n  bottom: 0;\n  font-size: 16px; }\n  @media (max-width: 575.98px) {\n    .chat-conversation .last-chat .conversation-list:before {\n      display: none; } }\n\n.chat-input-section {\n  border-top: 1px solid #eff2f7; }\n\n.chat-input {\n  border-radius: 30px;\n  background-color: #eff2f7 !important;\n  border-color: #eff2f7 !important;\n  padding-right: 120px; }\n\n.chat-input-links {\n  position: absolute;\n  right: 16px;\n  top: 50%;\n  transform: translateY(-50%); }\n  .chat-input-links li a {\n    font-size: 16px;\n    line-height: 36px;\n    padding: 0px 4px;\n    display: inline-block; }\n\n@media (max-width: 575.98px) {\n  .chat-send {\n    min-width: auto; } }\n\n.project-list-table {\n  border-collapse: separate;\n  border-spacing: 0 12px; }\n  .project-list-table tr {\n    background-color: #fff; }\n\n.contact-links a {\n  color: #495057; }\n\n.profile-user-wid {\n  margin-top: -26px; }\n\n@media (min-width: 576px) {\n  .currency-value {\n    position: relative; }\n    .currency-value:after {\n      content: \"\\F04E1\";\n      font-family: \"Material Design Icons\";\n      font-size: 24px;\n      position: absolute;\n      width: 45px;\n      height: 45px;\n      line-height: 45px;\n      border-radius: 50%;\n      text-align: center;\n      right: 0;\n      top: 50%;\n      transform: translateY(-50%);\n      background-color: #556ee6;\n      color: #fff;\n      z-index: 9;\n      right: -34px; } }\n\n.crypto-buy-sell-nav-content {\n  border: 2px solid #f6f6f6;\n  border-top: 0; }\n\n.kyc-doc-verification .dropzone {\n  min-height: 180px; }\n  .kyc-doc-verification .dropzone .dz-message {\n    margin: 24px 0px; }\n\n/******************\r\n    Ico Landing\r\n*******************/\n.section {\n  position: relative;\n  padding-top: 80px;\n  padding-bottom: 80px; }\n  .section.bg-white {\n    background-color: #fff !important; }\n\n.small-title {\n  color: #74788d;\n  margin-bottom: 8px; }\n\n.navigation {\n  padding: 0 16px;\n  width: 100%;\n  z-index: 999;\n  margin-bottom: 0px;\n  transition: all 0.5s ease-in-out;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1); }\n  @media (max-width: 991.98px) {\n    .navigation {\n      background-color: #fff; } }\n  .navigation .navbar-logo {\n    line-height: 70px;\n    transition: all 0.4s; }\n    .navigation .navbar-logo .logo-dark {\n      display: none; }\n      @media (max-width: 991.98px) {\n        .navigation .navbar-logo .logo-dark {\n          display: block; } }\n    .navigation .navbar-logo .logo-light {\n      display: block; }\n      @media (max-width: 991.98px) {\n        .navigation .navbar-logo .logo-light {\n          display: none; } }\n  .navigation .navbar-nav .nav-item .nav-link {\n    color: rgba(255, 255, 255, 0.6);\n    line-height: 58px;\n    padding: 6px 16px;\n    font-weight: 500;\n    transition: all 0.4s; }\n    @media (max-width: 991.98px) {\n      .navigation .navbar-nav .nav-item .nav-link {\n        color: #555b6d; } }\n    .navigation .navbar-nav .nav-item .nav-link:hover, .navigation .navbar-nav .nav-item .nav-link.active {\n      color: rgba(255, 255, 255, 0.9); }\n      @media (max-width: 991.98px) {\n        .navigation .navbar-nav .nav-item .nav-link:hover, .navigation .navbar-nav .nav-item .nav-link.active {\n          color: #556ee6; } }\n    @media (max-width: 991.98px) {\n      .navigation .navbar-nav .nav-item .nav-link {\n        line-height: 28px !important; } }\n  .navigation.nav-sticky {\n    background-color: #fff;\n    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03); }\n    .navigation.nav-sticky .navbar-logo {\n      line-height: 60px; }\n      .navigation.nav-sticky .navbar-logo .logo-dark {\n        display: block; }\n      .navigation.nav-sticky .navbar-logo .logo-light {\n        display: none; }\n    .navigation.nav-sticky .navbar-nav .nav-item .nav-link {\n      line-height: 48px;\n      color: #555b6d; }\n      .navigation.nav-sticky .navbar-nav .nav-item .nav-link:hover, .navigation.nav-sticky .navbar-nav .nav-item .nav-link.active {\n        color: #556ee6; }\n\n.bg-overlay {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  top: 0;\n  opacity: 0.7;\n  background-color: #000; }\n\n.hero-section {\n  padding-top: 220px;\n  padding-bottom: 190px; }\n  .hero-section.bg-ico-hero {\n    background-image: url(\"../images/crypto/bg-ico-hero.jpg\");\n    background-size: cover;\n    background-position: top; }\n  @media (max-width: 575.98px) {\n    .hero-section {\n      padding-top: 140px;\n      padding-bottom: 80px; } }\n  .hero-section .hero-title {\n    font-size: 42px; }\n    @media (max-width: 575.98px) {\n      .hero-section .hero-title {\n        font-size: 26px; } }\n  .hero-section .ico-countdown {\n    font-size: 22px;\n    margin-right: -12px;\n    margin-left: -12px; }\n    @media (max-width: 575.98px) {\n      .hero-section .ico-countdown {\n        display: block; } }\n    .hero-section .ico-countdown .coming-box {\n      margin-right: 12px;\n      margin-left: 12px;\n      border: 1px solid #eff2f7;\n      border-radius: 4px;\n      padding: 8px;\n      background-color: #fff; }\n      @media (max-width: 575.98px) {\n        .hero-section .ico-countdown .coming-box {\n          display: inline-block;\n          width: 40%;\n          margin-bottom: 24px; } }\n      .hero-section .ico-countdown .coming-box span {\n        background-color: #eff2f7;\n        font-size: 12px;\n        padding: 4px;\n        margin-top: 8px; }\n  .hero-section .softcap-progress {\n    overflow: visible; }\n    .hero-section .softcap-progress .progress-bar {\n      overflow: visible; }\n    .hero-section .softcap-progress .progress-label {\n      position: relative;\n      text-align: right;\n      color: #495057;\n      bottom: 20px;\n      font-size: 12px;\n      font-weight: 500; }\n\n.currency-price {\n  position: relative;\n  bottom: 40px; }\n\n.client-images img {\n  max-height: 34px;\n  width: auto !important;\n  margin: 12px auto;\n  opacity: 0.7;\n  transition: all 0.4s; }\n\n.features-number {\n  opacity: 0.1; }\n\n.team-box .team-social-links a {\n  color: #495057;\n  font-size: 14px; }\n\n.blog-box .blog-badge {\n  position: absolute;\n  top: 12px;\n  right: 12px; }\n\n.landing-footer {\n  padding: 80px 0 40px;\n  background-color: #2a3042;\n  color: rgba(255, 255, 255, 0.5); }\n  .landing-footer .footer-list-title {\n    color: rgba(255, 255, 255, 0.9); }\n  .landing-footer .footer-list-menu li a {\n    display: block;\n    color: rgba(255, 255, 255, 0.5);\n    margin-bottom: 14px;\n    transition: all 0.4s; }\n    .landing-footer .footer-list-menu li a:hover {\n      color: rgba(255, 255, 255, 0.8); }\n  .landing-footer .blog-post .post {\n    display: block;\n    color: rgba(255, 255, 255, 0.5);\n    padding: 16px 0px;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1); }\n    .landing-footer .blog-post .post .post-title {\n      color: rgba(255, 255, 255, 0.8);\n      font-size: 14px; }\n    .landing-footer .blog-post .post:first-of-type {\n      padding-top: 0; }\n    .landing-footer .blog-post .post:last-of-type {\n      padding-bottom: 0;\n      border-bottom: 0; }\n  .landing-footer .footer-border {\n    border-color: rgba(255, 255, 255, 0.1); }\n\n.counter-number {\n  font-size: 32px;\n  font-weight: 600;\n  text-align: center;\n  display: flex; }\n  .counter-number span {\n    font-size: 16px;\n    font-weight: 400;\n    display: block;\n    padding-top: 5px; }\n\n.coming-box {\n  width: 25%; }\n\n/************** Horizontal timeline **************/\n.hori-timeline .events .event-list {\n  text-align: center;\n  display: block; }\n  .hori-timeline .events .event-list .event-down-icon {\n    position: relative; }\n    .hori-timeline .events .event-list .event-down-icon::before {\n      content: \"\";\n      position: absolute;\n      width: 100%;\n      top: 16px;\n      left: 0;\n      right: 0;\n      border-bottom: 3px dashed #f6f6f6; }\n    .hori-timeline .events .event-list .event-down-icon .down-arrow-icon {\n      position: relative;\n      background-color: #fff;\n      padding: 4px; }\n  .hori-timeline .events .event-list:hover .down-arrow-icon {\n    animation: fade-down 1.5s infinite linear; }\n  .hori-timeline .events .event-list.active .down-arrow-icon {\n    animation: fade-down 1.5s infinite linear; }\n    .hori-timeline .events .event-list.active .down-arrow-icon:before {\n      content: \"\\ec4c\"; }\n\n/************** vertical timeline **************/\n.verti-timeline {\n  border-left: 3px dashed #f6f6f6;\n  margin: 0 10px; }\n  .verti-timeline .event-list {\n    position: relative;\n    padding: 0px 0px 40px 30px; }\n    .verti-timeline .event-list .event-timeline-dot {\n      position: absolute;\n      left: -9px;\n      top: 0px;\n      z-index: 9;\n      font-size: 16px; }\n    .verti-timeline .event-list .event-content {\n      position: relative;\n      border: 2px solid #eff2f7;\n      border-radius: 7px; }\n    .verti-timeline .event-list.active .event-timeline-dot {\n      color: #556ee6; }\n    .verti-timeline .event-list:last-child {\n      padding-bottom: 0px; }\n\n.plan-box .plan-btn {\n  position: relative; }\n  .plan-box .plan-btn::before {\n    content: \"\";\n    position: absolute;\n    width: 100%;\n    height: 2px;\n    background: #f6f6f6;\n    left: 0px;\n    right: 0px;\n    top: 12px; }\n\n.blog-play-icon {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  margin: 0px auto; }\n\nhtml {\n  direction: rtl; }\n\nbody {\n  text-align: right; }\n\n.dropdown-menu.show {\n  text-align: right;\n  left: auto !important;\n  right: 0;\n  bottom: auto; }\n\n.dropdown-menu-right {\n  right: auto !important;\n  left: 0 !important; }\n  .dropdown-menu-right.show {\n    left: 0 !important; }\n\nul {\n  padding-right: 0; }\n\n.list-inline-item:not(:last-child) {\n  margin-left: 6px;\n  margin-right: 0px; }\n\n.border-right {\n  border-left: 1px solid #eff2f7 !important; }\n\n.border-left {\n  border-right: 1px solid #eff2f7 !important; }\n\n.btn-label {\n  padding-right: 44px;\n  padding-left: 12px; }\n  .btn-label .label-icon {\n    left: auto;\n    right: 0;\n    border-right: 0;\n    border-left: 1px solid rgba(255, 255, 255, 0.4); }\n  .btn-label.btn-light .label-icon {\n    border-right: 0;\n    border-left: 1px solid rgba(52, 58, 64, 0.2); }\n\n.btn-group,\n.btn-group-vertical {\n  direction: ltr; }\n\n.pagination .page-item:first-child .page-link {\n  margin-right: 0;\n  border-top-left-radius: 0px;\n  border-bottom-left-radius: 0px;\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem; }\n\n.pagination .page-item:last-child .page-link {\n  border-top-right-radius: 0px;\n  border-bottom-right-radius: 0px;\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem; }\n\n.blockquote-reverse {\n  text-align: left !important; }\n\ndd {\n  margin-right: 0; }\n\n.custom-modal-title {\n  text-align: right; }\n\n.modal-header .close {\n  margin: -1rem auto -1rem -1rem;\n  left: 0px; }\n\n.modal-demo .close {\n  left: 25px;\n  right: auto; }\n\n.modal-footer > :not(:first-child) {\n  margin-right: .25rem;\n  margin-left: 0; }\n\n.modal-footer > :not(:last-child) {\n  margin-left: .25rem;\n  margin-right: 0; }\n\n.alert-dismissible {\n  padding-left: 3.71875rem;\n  padding-right: 1.25rem; }\n  .alert-dismissible .close {\n    left: 0;\n    right: auto; }\n\n.breadcrumb-item + .breadcrumb-item {\n  padding-right: 0.5rem;\n  padding-left: 0px; }\n  .breadcrumb-item + .breadcrumb-item::before {\n    padding-left: 0.5rem;\n    padding-right: 0px; }\n\n.form-check-right {\n  padding-right: 0 !important;\n  padding-left: 1.25rem !important; }\n  .form-check-right .form-check-input {\n    right: auto;\n    left: 0;\n    margin-right: 0; }\n\n.custom-control-right {\n  padding-left: 1.5rem !important;\n  padding-right: 0 !important; }\n  .custom-control-right .custom-control-label {\n    display: block; }\n    .custom-control-right .custom-control-label:before, .custom-control-right .custom-control-label:after {\n      right: auto;\n      left: -1.5rem; }\n  .custom-control-right .custom-control-input {\n    right: auto;\n    left: 0; }\n\n.form-check {\n  padding-left: 0;\n  padding-right: 1.25rem; }\n\n.form-check-input {\n  margin-left: 0;\n  margin-right: -1.25rem; }\n\n.form-check-inline {\n  margin-left: .75rem;\n  margin-right: 0; }\n\n.custom-control {\n  padding-right: 1.5rem;\n  padding-left: 0; }\n\n.custom-control-inline {\n  margin-right: 0;\n  margin-left: 1rem; }\n\n.custom-control-label::before {\n  left: auto;\n  right: -1.5rem; }\n\n.custom-control-label::after {\n  left: auto;\n  right: -1.5rem; }\n\n.custom-switch {\n  padding-right: 2.25rem;\n  padding-left: 0; }\n  .custom-switch .custom-control-label::before {\n    right: -2.25rem;\n    left: auto; }\n  .custom-switch .custom-control-label::after {\n    right: calc(-2.25rem + 2px);\n    left: auto; }\n  .custom-switch .custom-control-input:checked ~ .custom-control-label::after {\n    transform: translateX(-0.75rem); }\n\n.custom-file-label::after {\n  right: auto;\n  left: 0;\n  border-right: inherit; }\n\n.custom-switch-md {\n  padding-right: 3rem;\n  padding-left: 0; }\n  .custom-switch-md .custom-control-label:before {\n    left: auto;\n    right: -3rem; }\n  .custom-switch-md .custom-control-label:after {\n    right: calc(-3rem + 2px);\n    left: auto; }\n  .custom-switch-md .custom-control-input:checked ~ .custom-control-label::after {\n    transform: translateX(-1.25rem); }\n\n.custom-switch-lg {\n  padding-right: 3.75rem;\n  padding-left: 0; }\n  .custom-switch-lg .custom-control-label:before {\n    left: auto;\n    right: -3.75rem; }\n  .custom-switch-lg .custom-control-label:after {\n    right: calc(-3.75rem + 2px);\n    left: auto; }\n  .custom-switch-lg .custom-control-input:checked ~ .custom-control-label::after {\n    transform: translateX(-1.5rem); }\n\n.custom-checkbox-outline .custom-control-input:checked ~ .custom-control-label:after {\n  left: auto;\n  right: -28px; }\n\n.custom-radio-outline .custom-control-input:checked ~ .custom-control-label:after {\n  left: auto;\n  right: -20px; }\n\n.input-group-prepend {\n  margin-left: -1px;\n  margin-right: 0; }\n\n.input-group-append {\n  margin-right: -1px;\n  margin-left: 0; }\n\n.input-group > .input-group-prepend > .btn,\n.input-group > .input-group-prepend > .input-group-text,\n.input-group > .input-group-append:not(:last-child) > .btn,\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child),\n.input-group > .custom-select:not(:last-child),\n.input-group > .form-control:not(:last-child) {\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0; }\n\n.input-group > .input-group-append > .btn,\n.input-group > .input-group-append > .input-group-text,\n.input-group > .input-group-prepend:not(:first-child) > .btn,\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child),\n.input-group > .custom-select:not(:first-child),\n.input-group > .form-control:not(:first-child) {\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0; }\n\n.m-0 {\n  margin: 0 !important; }\n\n.mt-0,\n.my-0 {\n  margin-top: 0 !important; }\n\n.mr-0 {\n  margin-left: 0 !important;\n  margin-right: 0 !important; }\n\n.mb-0,\n.my-0 {\n  margin-bottom: 0 !important; }\n\n.ml-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important; }\n\n.m-1 {\n  margin: 0.25rem !important; }\n\n.mt-1,\n.my-1 {\n  margin-top: 0.25rem !important; }\n\n.mr-1 {\n  margin-left: 0.25rem !important;\n  margin-right: 0 !important; }\n\n.mb-1,\n.my-1 {\n  margin-bottom: 0.25rem !important; }\n\n.ml-1 {\n  margin-right: 0.25rem !important;\n  margin-left: 0 !important; }\n\n.m-2 {\n  margin: 0.5rem !important; }\n\n.mt-2,\n.my-2 {\n  margin-top: 0.5rem !important; }\n\n.mr-2 {\n  margin-left: 0.5rem !important;\n  margin-right: 0 !important; }\n\n.mb-2,\n.my-2 {\n  margin-bottom: 0.5rem !important; }\n\n.ml-2 {\n  margin-right: 0.5rem !important;\n  margin-left: 0 !important; }\n\n.m-3 {\n  margin: 1rem !important; }\n\n.mt-3,\n.my-3 {\n  margin-top: 1rem !important; }\n\n.mr-3 {\n  margin-left: 1rem !important;\n  margin-right: 0 !important; }\n\n.mb-3,\n.my-3 {\n  margin-bottom: 1rem !important; }\n\n.ml-3 {\n  margin-right: 1rem !important;\n  margin-left: 0 !important; }\n\n.m-4 {\n  margin: 1.5rem !important; }\n\n.mt-4,\n.my-4 {\n  margin-top: 1.5rem !important; }\n\n.mr-4 {\n  margin-left: 1.5rem !important;\n  margin-right: 0 !important; }\n\n.mb-4,\n.my-4 {\n  margin-bottom: 1.5rem !important; }\n\n.ml-4 {\n  margin-right: 1.5rem !important;\n  margin-left: 0 !important; }\n\n.m-5 {\n  margin: 3rem !important; }\n\n.mt-5,\n.my-5 {\n  margin-top: 3rem !important; }\n\n.mr-5 {\n  margin-left: 3rem !important;\n  margin-right: 0 !important; }\n\n.mb-5,\n.my-5 {\n  margin-bottom: 3rem !important; }\n\n.ml-5 {\n  margin-right: 3rem !important;\n  margin-left: 0 !important; }\n\n.p-0 {\n  padding: 0 !important; }\n\n.pt-0,\n.py-0 {\n  padding-top: 0 !important; }\n\n.pr-0 {\n  padding-left: 0 !important;\n  padding-right: 0 !important; }\n\n.pb-0,\n.py-0 {\n  padding-bottom: 0 !important; }\n\n.pl-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important; }\n\n.p-1 {\n  padding: 0.25rem !important; }\n\n.pt-1,\n.py-1 {\n  padding-top: 0.25rem !important; }\n\n.pr-1 {\n  padding-left: 0.25rem !important;\n  padding-right: 0 !important; }\n\n.pb-1,\n.py-1 {\n  padding-bottom: 0.25rem !important; }\n\n.pl-1 {\n  padding-right: 0.25rem !important;\n  padding-left: 0 !important; }\n\n.p-2 {\n  padding: 0.5rem !important; }\n\n.pt-2,\n.py-2 {\n  padding-top: 0.5rem !important; }\n\n.pr-2 {\n  padding-left: 0.5rem !important;\n  padding-right: 0 !important; }\n\n.pb-2,\n.py-2 {\n  padding-bottom: 0.5rem !important; }\n\n.pl-2 {\n  padding-right: 0.5rem !important;\n  padding-left: 0 !important; }\n\n.p-3 {\n  padding: 1rem !important; }\n\n.pt-3,\n.py-3 {\n  padding-top: 1rem !important; }\n\n.pr-3 {\n  padding-left: 1rem !important;\n  padding-right: 0 !important; }\n\n.pb-3,\n.py-3 {\n  padding-bottom: 1rem !important; }\n\n.pl-3 {\n  padding-right: 1rem !important;\n  padding-left: 0 !important; }\n\n.p-4 {\n  padding: 1.5rem !important; }\n\n.pt-4,\n.py-4 {\n  padding-top: 1.5rem !important; }\n\n.pr-4 {\n  padding-left: 1.5rem !important;\n  padding-right: 0 !important; }\n\n.pb-4,\n.py-4 {\n  padding-bottom: 1.5rem !important; }\n\n.pl-4 {\n  padding-right: 1.5rem !important;\n  padding-left: 0 !important; }\n\n.p-5 {\n  padding: 3rem !important; }\n\n.pt-5,\n.py-5 {\n  padding-top: 3rem !important; }\n\n.pr-5 {\n  padding-left: 3rem !important;\n  padding-right: 0 !important; }\n\n.pb-5,\n.py-5 {\n  padding-bottom: 3rem !important; }\n\n.pl-5 {\n  padding-right: 3rem !important;\n  padding-left: 0 !important; }\n\n.m-n1 {\n  margin: -0.25rem !important; }\n\n.mt-n1,\n.my-n1 {\n  margin-top: -0.25rem !important; }\n\n.mr-n1,\n.mx-n1 {\n  margin-right: -0.25rem !important; }\n\n.mb-n1,\n.my-n1 {\n  margin-bottom: -0.25rem !important; }\n\n.ml-n1,\n.mx-n1 {\n  margin-left: -0.25rem !important; }\n\n.m-n2 {\n  margin: -0.5rem !important; }\n\n.mt-n2,\n.my-n2 {\n  margin-top: -0.5rem !important; }\n\n.mr-n2,\n.mx-n2 {\n  margin-right: -0.5rem !important; }\n\n.mb-n2,\n.my-n2 {\n  margin-bottom: -0.5rem !important; }\n\n.ml-n2,\n.mx-n2 {\n  margin-left: -0.5rem !important; }\n\n.m-n3 {\n  margin: -1rem !important; }\n\n.mt-n3,\n.my-n3 {\n  margin-top: -1rem !important; }\n\n.mr-n3,\n.mx-n3 {\n  margin-right: -1rem !important; }\n\n.mb-n3,\n.my-n3 {\n  margin-bottom: -1rem !important; }\n\n.ml-n3,\n.mx-n3 {\n  margin-left: -1rem !important; }\n\n.m-n4 {\n  margin: -1.5rem !important; }\n\n.mt-n4,\n.my-n4 {\n  margin-top: -1.5rem !important; }\n\n.mr-n4,\n.mx-n4 {\n  margin-right: -1.5rem !important; }\n\n.mb-n4,\n.my-n4 {\n  margin-bottom: -1.5rem !important; }\n\n.ml-n4,\n.mx-n4 {\n  margin-left: -1.5rem !important; }\n\n.m-n5 {\n  margin: -3rem !important; }\n\n.mt-n5,\n.my-n5 {\n  margin-top: -3rem !important; }\n\n.mr-n5,\n.mx-n5 {\n  margin-right: -3rem !important; }\n\n.mb-n5,\n.my-n5 {\n  margin-bottom: -3rem !important; }\n\n.ml-n5,\n.mx-n5 {\n  margin-left: -3rem !important; }\n\n.m-auto {\n  margin: auto !important; }\n\n.mt-auto,\n.my-auto {\n  margin-top: auto !important; }\n\n.mr-auto,\n.mx-auto {\n  margin-left: auto !important;\n  margin-right: inherit !important; }\n\n.mb-auto,\n.my-auto {\n  margin-bottom: auto !important; }\n\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important; }\n\n.ml-auto {\n  margin-right: auto !important;\n  margin-left: 0 !important; }\n\n@media (min-width: 576px) {\n  .m-sm-0 {\n    margin: 0 !important; }\n  .mt-sm-0,\n  .my-sm-0 {\n    margin-top: 0 !important; }\n  .mr-sm-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-sm-0,\n  .my-sm-0 {\n    margin-bottom: 0 !important; }\n  .ml-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-sm-1 {\n    margin: 0.25rem !important; }\n  .mt-sm-1,\n  .my-sm-1 {\n    margin-top: 0.25rem !important; }\n  .mr-sm-1 {\n    margin-left: 0.25rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-1,\n  .my-sm-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-sm-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0 !important; }\n  .m-sm-2 {\n    margin: 0.5rem !important; }\n  .mt-sm-2,\n  .my-sm-2 {\n    margin-top: 0.5rem !important; }\n  .mr-sm-2 {\n    margin-left: 0.5rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-2,\n  .my-sm-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-sm-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0 !important; }\n  .m-sm-3 {\n    margin: 1rem !important; }\n  .mt-sm-3,\n  .my-sm-3 {\n    margin-top: 1rem !important; }\n  .mr-sm-3 {\n    margin-left: 1rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-3,\n  .my-sm-3 {\n    margin-bottom: 1rem !important; }\n  .ml-sm-3 {\n    margin-right: 1rem !important;\n    margin-left: 0 !important; }\n  .m-sm-4 {\n    margin: 1.5rem !important; }\n  .mt-sm-4,\n  .my-sm-4 {\n    margin-top: 1.5rem !important; }\n  .mr-sm-4 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-4,\n  .my-sm-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-sm-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-sm-5 {\n    margin: 3rem !important; }\n  .mt-sm-5,\n  .my-sm-5 {\n    margin-top: 3rem !important; }\n  .mr-sm-5 {\n    margin-left: 3rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-5,\n  .my-sm-5 {\n    margin-bottom: 3rem !important; }\n  .ml-sm-5 {\n    margin-right: 3rem !important;\n    margin-left: 0 !important; }\n  .p-sm-0 {\n    padding: 0 !important; }\n  .pt-sm-0,\n  .py-sm-0 {\n    padding-top: 0 !important; }\n  .pr-sm-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-sm-0,\n  .py-sm-0 {\n    padding-bottom: 0 !important; }\n  .pl-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-sm-1 {\n    padding: 0.25rem !important; }\n  .pt-sm-1,\n  .py-sm-1 {\n    padding-top: 0.25rem !important; }\n  .pr-sm-1 {\n    padding-left: 0.25rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-1,\n  .py-sm-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-sm-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0 !important; }\n  .p-sm-2 {\n    padding: 0.5rem !important; }\n  .pt-sm-2,\n  .py-sm-2 {\n    padding-top: 0.5rem !important; }\n  .pr-sm-2 {\n    padding-left: 0.5rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-2,\n  .py-sm-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-sm-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0 !important; }\n  .p-sm-3 {\n    padding: 1rem !important; }\n  .pt-sm-3,\n  .py-sm-3 {\n    padding-top: 1rem !important; }\n  .pr-sm-3 {\n    padding-left: 1rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-3,\n  .py-sm-3 {\n    padding-bottom: 1rem !important; }\n  .pl-sm-3 {\n    padding-right: 1rem !important;\n    padding-left: 0 !important; }\n  .p-sm-4 {\n    padding: 1.5rem !important; }\n  .pt-sm-4,\n  .py-sm-4 {\n    padding-top: 1.5rem !important; }\n  .pr-sm-4 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-4,\n  .py-sm-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-sm-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-sm-5 {\n    padding: 3rem !important; }\n  .pt-sm-5,\n  .py-sm-5 {\n    padding-top: 3rem !important; }\n  .pr-sm-5 {\n    padding-left: 3rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-5,\n  .py-sm-5 {\n    padding-bottom: 3rem !important; }\n  .pl-sm-5 {\n    padding-right: 3rem !important;\n    padding-left: 0 !important; }\n  .m-sm-n1 {\n    margin: -0.25rem !important; }\n  .mt-sm-n1,\n  .my-sm-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-sm-n1,\n  .mx-sm-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-sm-n1,\n  .my-sm-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-sm-n1,\n  .mx-sm-n1 {\n    margin-left: -0.25rem !important; }\n  .m-sm-n2 {\n    margin: -0.5rem !important; }\n  .mt-sm-n2,\n  .my-sm-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-sm-n2,\n  .mx-sm-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-sm-n2,\n  .my-sm-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-sm-n2,\n  .mx-sm-n2 {\n    margin-left: -0.5rem !important; }\n  .m-sm-n3 {\n    margin: -1rem !important; }\n  .mt-sm-n3,\n  .my-sm-n3 {\n    margin-top: -1rem !important; }\n  .mr-sm-n3,\n  .mx-sm-n3 {\n    margin-right: -1rem !important; }\n  .mb-sm-n3,\n  .my-sm-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-sm-n3,\n  .mx-sm-n3 {\n    margin-left: -1rem !important; }\n  .m-sm-n4 {\n    margin: -1.5rem !important; }\n  .mt-sm-n4,\n  .my-sm-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-sm-n4,\n  .mx-sm-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-sm-n4,\n  .my-sm-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-sm-n4,\n  .mx-sm-n4 {\n    margin-left: -1.5rem !important; }\n  .m-sm-n5 {\n    margin: -3rem !important; }\n  .mt-sm-n5,\n  .my-sm-n5 {\n    margin-top: -3rem !important; }\n  .mr-sm-n5,\n  .mx-sm-n5 {\n    margin-right: -3rem !important; }\n  .mb-sm-n5,\n  .my-sm-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-sm-n5,\n  .mx-sm-n5 {\n    margin-left: -3rem !important; }\n  .m-sm-auto {\n    margin: auto !important; }\n  .mt-sm-auto,\n  .my-sm-auto {\n    margin-top: auto !important; }\n  .mr-sm-auto,\n  .mx-sm-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-sm-auto,\n  .my-sm-auto {\n    margin-bottom: auto !important; }\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; }\n  .ml-sm-auto {\n    margin-right: auto !important;\n    margin-left: 0 !important; } }\n\n@media (min-width: 768px) {\n  .m-md-0 {\n    margin: 0 !important; }\n  .mt-md-0,\n  .my-md-0 {\n    margin-top: 0 !important; }\n  .mr-md-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-md-0,\n  .my-md-0 {\n    margin-bottom: 0 !important; }\n  .ml-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-md-1 {\n    margin: 0.25rem !important; }\n  .mt-md-1,\n  .my-md-1 {\n    margin-top: 0.25rem !important; }\n  .mr-md-1 {\n    margin-left: 0.25rem !important;\n    margin-right: 0 !important; }\n  .mb-md-1,\n  .my-md-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-md-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0 !important; }\n  .m-md-2 {\n    margin: 0.5rem !important; }\n  .mt-md-2,\n  .my-md-2 {\n    margin-top: 0.5rem !important; }\n  .mr-md-2 {\n    margin-left: 0.5rem !important;\n    margin-right: 0 !important; }\n  .mb-md-2,\n  .my-md-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-md-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0 !important; }\n  .m-md-3 {\n    margin: 1rem !important; }\n  .mt-md-3,\n  .my-md-3 {\n    margin-top: 1rem !important; }\n  .mr-md-3 {\n    margin-left: 1rem !important;\n    margin-right: 0 !important; }\n  .mb-md-3,\n  .my-md-3 {\n    margin-bottom: 1rem !important; }\n  .ml-md-3 {\n    margin-right: 1rem !important;\n    margin-left: 0 !important; }\n  .m-md-4 {\n    margin: 1.5rem !important; }\n  .mt-md-4,\n  .my-md-4 {\n    margin-top: 1.5rem !important; }\n  .mr-md-4 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-md-4,\n  .my-md-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-md-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-md-5 {\n    margin: 3rem !important; }\n  .mt-md-5,\n  .my-md-5 {\n    margin-top: 3rem !important; }\n  .mr-md-5 {\n    margin-left: 3rem !important;\n    margin-right: 0 !important; }\n  .mb-md-5,\n  .my-md-5 {\n    margin-bottom: 3rem !important; }\n  .ml-md-5 {\n    margin-right: 3rem !important;\n    margin-left: 0 !important; }\n  .p-md-0 {\n    padding: 0 !important; }\n  .pt-md-0,\n  .py-md-0 {\n    padding-top: 0 !important; }\n  .pr-md-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-md-0,\n  .py-md-0 {\n    padding-bottom: 0 !important; }\n  .pl-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-md-1 {\n    padding: 0.25rem !important; }\n  .pt-md-1,\n  .py-md-1 {\n    padding-top: 0.25rem !important; }\n  .pr-md-1 {\n    padding-left: 0.25rem !important;\n    padding-right: 0 !important; }\n  .pb-md-1,\n  .py-md-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-md-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0 !important; }\n  .p-md-2 {\n    padding: 0.5rem !important; }\n  .pt-md-2,\n  .py-md-2 {\n    padding-top: 0.5rem !important; }\n  .pr-md-2 {\n    padding-left: 0.5rem !important;\n    padding-right: 0 !important; }\n  .pb-md-2,\n  .py-md-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-md-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0 !important; }\n  .p-md-3 {\n    padding: 1rem !important; }\n  .pt-md-3,\n  .py-md-3 {\n    padding-top: 1rem !important; }\n  .pr-md-3 {\n    padding-left: 1rem !important;\n    padding-right: 0 !important; }\n  .pb-md-3,\n  .py-md-3 {\n    padding-bottom: 1rem !important; }\n  .pl-md-3 {\n    padding-right: 1rem !important;\n    padding-left: 0 !important; }\n  .p-md-4 {\n    padding: 1.5rem !important; }\n  .pt-md-4,\n  .py-md-4 {\n    padding-top: 1.5rem !important; }\n  .pr-md-4 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-md-4,\n  .py-md-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-md-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-md-5 {\n    padding: 3rem !important; }\n  .pt-md-5,\n  .py-md-5 {\n    padding-top: 3rem !important; }\n  .pr-md-5 {\n    padding-left: 3rem !important;\n    padding-right: 0 !important; }\n  .pb-md-5,\n  .py-md-5 {\n    padding-bottom: 3rem !important; }\n  .pl-md-5 {\n    padding-right: 3rem !important;\n    padding-left: 0 !important; }\n  .m-md-n1 {\n    margin: -0.25rem !important; }\n  .mt-md-n1,\n  .my-md-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-md-n1,\n  .mx-md-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-md-n1,\n  .my-md-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-md-n1,\n  .mx-md-n1 {\n    margin-left: -0.25rem !important; }\n  .m-md-n2 {\n    margin: -0.5rem !important; }\n  .mt-md-n2,\n  .my-md-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-md-n2,\n  .mx-md-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-md-n2,\n  .my-md-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-md-n2,\n  .mx-md-n2 {\n    margin-left: -0.5rem !important; }\n  .m-md-n3 {\n    margin: -1rem !important; }\n  .mt-md-n3,\n  .my-md-n3 {\n    margin-top: -1rem !important; }\n  .mr-md-n3,\n  .mx-md-n3 {\n    margin-right: -1rem !important; }\n  .mb-md-n3,\n  .my-md-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-md-n3,\n  .mx-md-n3 {\n    margin-left: -1rem !important; }\n  .m-md-n4 {\n    margin: -1.5rem !important; }\n  .mt-md-n4,\n  .my-md-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-md-n4,\n  .mx-md-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-md-n4,\n  .my-md-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-md-n4,\n  .mx-md-n4 {\n    margin-left: -1.5rem !important; }\n  .m-md-n5 {\n    margin: -3rem !important; }\n  .mt-md-n5,\n  .my-md-n5 {\n    margin-top: -3rem !important; }\n  .mr-md-n5,\n  .mx-md-n5 {\n    margin-right: -3rem !important; }\n  .mb-md-n5,\n  .my-md-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-md-n5,\n  .mx-md-n5 {\n    margin-left: -3rem !important; }\n  .m-md-auto {\n    margin: auto !important; }\n  .mt-md-auto,\n  .my-md-auto {\n    margin-top: auto !important; }\n  .mr-md-auto,\n  .mx-md-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-md-auto,\n  .my-md-auto {\n    margin-bottom: auto !important; }\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; }\n  .ml-md-auto {\n    margin-right: auto !important;\n    margin-left: 0 !important; } }\n\n@media (min-width: 992px) {\n  .m-lg-0 {\n    margin: 0 !important; }\n  .mt-lg-0,\n  .my-lg-0 {\n    margin-top: 0 !important; }\n  .mr-lg-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-lg-0,\n  .my-lg-0 {\n    margin-bottom: 0 !important; }\n  .ml-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-lg-1 {\n    margin: 0.25rem !important; }\n  .mt-lg-1,\n  .my-lg-1 {\n    margin-top: 0.25rem !important; }\n  .mr-lg-1 {\n    margin-left: 0.25rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-1,\n  .my-lg-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-lg-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0 !important; }\n  .m-lg-2 {\n    margin: 0.5rem !important; }\n  .mt-lg-2,\n  .my-lg-2 {\n    margin-top: 0.5rem !important; }\n  .mr-lg-2 {\n    margin-left: 0.5rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-2,\n  .my-lg-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-lg-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0 !important; }\n  .m-lg-3 {\n    margin: 1rem !important; }\n  .mt-lg-3,\n  .my-lg-3 {\n    margin-top: 1rem !important; }\n  .mr-lg-3 {\n    margin-left: 1rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-3,\n  .my-lg-3 {\n    margin-bottom: 1rem !important; }\n  .ml-lg-3 {\n    margin-right: 1rem !important;\n    margin-left: 0 !important; }\n  .m-lg-4 {\n    margin: 1.5rem !important; }\n  .mt-lg-4,\n  .my-lg-4 {\n    margin-top: 1.5rem !important; }\n  .mr-lg-4 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-4,\n  .my-lg-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-lg-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-lg-5 {\n    margin: 3rem !important; }\n  .mt-lg-5,\n  .my-lg-5 {\n    margin-top: 3rem !important; }\n  .mr-lg-5 {\n    margin-left: 3rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-5,\n  .my-lg-5 {\n    margin-bottom: 3rem !important; }\n  .ml-lg-5 {\n    margin-right: 3rem !important;\n    margin-left: 0 !important; }\n  .p-lg-0 {\n    padding: 0 !important; }\n  .pt-lg-0,\n  .py-lg-0 {\n    padding-top: 0 !important; }\n  .pr-lg-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-lg-0,\n  .py-lg-0 {\n    padding-bottom: 0 !important; }\n  .pl-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-lg-1 {\n    padding: 0.25rem !important; }\n  .pt-lg-1,\n  .py-lg-1 {\n    padding-top: 0.25rem !important; }\n  .pr-lg-1 {\n    padding-left: 0.25rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-1,\n  .py-lg-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-lg-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0 !important; }\n  .p-lg-2 {\n    padding: 0.5rem !important; }\n  .pt-lg-2,\n  .py-lg-2 {\n    padding-top: 0.5rem !important; }\n  .pr-lg-2 {\n    padding-left: 0.5rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-2,\n  .py-lg-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-lg-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0 !important; }\n  .p-lg-3 {\n    padding: 1rem !important; }\n  .pt-lg-3,\n  .py-lg-3 {\n    padding-top: 1rem !important; }\n  .pr-lg-3 {\n    padding-left: 1rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-3,\n  .py-lg-3 {\n    padding-bottom: 1rem !important; }\n  .pl-lg-3 {\n    padding-right: 1rem !important;\n    padding-left: 0 !important; }\n  .p-lg-4 {\n    padding: 1.5rem !important; }\n  .pt-lg-4,\n  .py-lg-4 {\n    padding-top: 1.5rem !important; }\n  .pr-lg-4 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-4,\n  .py-lg-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-lg-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-lg-5 {\n    padding: 3rem !important; }\n  .pt-lg-5,\n  .py-lg-5 {\n    padding-top: 3rem !important; }\n  .pr-lg-5 {\n    padding-left: 3rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-5,\n  .py-lg-5 {\n    padding-bottom: 3rem !important; }\n  .pl-lg-5 {\n    padding-right: 3rem !important;\n    padding-left: 0 !important; }\n  .m-lg-n1 {\n    margin: -0.25rem !important; }\n  .mt-lg-n1,\n  .my-lg-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-lg-n1,\n  .mx-lg-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-lg-n1,\n  .my-lg-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-lg-n1,\n  .mx-lg-n1 {\n    margin-left: -0.25rem !important; }\n  .m-lg-n2 {\n    margin: -0.5rem !important; }\n  .mt-lg-n2,\n  .my-lg-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-lg-n2,\n  .mx-lg-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-lg-n2,\n  .my-lg-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-lg-n2,\n  .mx-lg-n2 {\n    margin-left: -0.5rem !important; }\n  .m-lg-n3 {\n    margin: -1rem !important; }\n  .mt-lg-n3,\n  .my-lg-n3 {\n    margin-top: -1rem !important; }\n  .mr-lg-n3,\n  .mx-lg-n3 {\n    margin-right: -1rem !important; }\n  .mb-lg-n3,\n  .my-lg-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-lg-n3,\n  .mx-lg-n3 {\n    margin-left: -1rem !important; }\n  .m-lg-n4 {\n    margin: -1.5rem !important; }\n  .mt-lg-n4,\n  .my-lg-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-lg-n4,\n  .mx-lg-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-lg-n4,\n  .my-lg-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-lg-n4,\n  .mx-lg-n4 {\n    margin-left: -1.5rem !important; }\n  .m-lg-n5 {\n    margin: -3rem !important; }\n  .mt-lg-n5,\n  .my-lg-n5 {\n    margin-top: -3rem !important; }\n  .mr-lg-n5,\n  .mx-lg-n5 {\n    margin-right: -3rem !important; }\n  .mb-lg-n5,\n  .my-lg-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-lg-n5,\n  .mx-lg-n5 {\n    margin-left: -3rem !important; }\n  .m-lg-auto {\n    margin: auto !important; }\n  .mt-lg-auto,\n  .my-lg-auto {\n    margin-top: auto !important; }\n  .mr-lg-auto,\n  .mx-lg-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-lg-auto,\n  .my-lg-auto {\n    margin-bottom: auto !important; }\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; }\n  .ml-lg-auto {\n    margin-right: auto !important;\n    margin-left: 0 !important; } }\n\n@media (min-width: 1200px) {\n  .m-xl-0 {\n    margin: 0 !important; }\n  .mt-xl-0,\n  .my-xl-0 {\n    margin-top: 0 !important; }\n  .mr-xl-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-xl-0,\n  .my-xl-0 {\n    margin-bottom: 0 !important; }\n  .ml-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-xl-1 {\n    margin: 0.25rem !important; }\n  .mt-xl-1,\n  .my-xl-1 {\n    margin-top: 0.25rem !important; }\n  .mr-xl-1 {\n    margin-left: 0.25rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-1,\n  .my-xl-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-xl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0 !important; }\n  .m-xl-2 {\n    margin: 0.5rem !important; }\n  .mt-xl-2,\n  .my-xl-2 {\n    margin-top: 0.5rem !important; }\n  .mr-xl-2 {\n    margin-left: 0.5rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-2,\n  .my-xl-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-xl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0 !important; }\n  .m-xl-3 {\n    margin: 1rem !important; }\n  .mt-xl-3,\n  .my-xl-3 {\n    margin-top: 1rem !important; }\n  .mr-xl-3 {\n    margin-left: 1rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-3,\n  .my-xl-3 {\n    margin-bottom: 1rem !important; }\n  .ml-xl-3 {\n    margin-right: 1rem !important;\n    margin-left: 0 !important; }\n  .m-xl-4 {\n    margin: 1.5rem !important; }\n  .mt-xl-4,\n  .my-xl-4 {\n    margin-top: 1.5rem !important; }\n  .mr-xl-4 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-4,\n  .my-xl-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-xl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-xl-5 {\n    margin: 3rem !important; }\n  .mt-xl-5,\n  .my-xl-5 {\n    margin-top: 3rem !important; }\n  .mr-xl-5 {\n    margin-left: 3rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-5,\n  .my-xl-5 {\n    margin-bottom: 3rem !important; }\n  .ml-xl-5 {\n    margin-right: 3rem !important;\n    margin-left: 0 !important; }\n  .p-xl-0 {\n    padding: 0 !important; }\n  .pt-xl-0,\n  .py-xl-0 {\n    padding-top: 0 !important; }\n  .pr-xl-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-xl-0,\n  .py-xl-0 {\n    padding-bottom: 0 !important; }\n  .pl-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-xl-1 {\n    padding: 0.25rem !important; }\n  .pt-xl-1,\n  .py-xl-1 {\n    padding-top: 0.25rem !important; }\n  .pr-xl-1 {\n    padding-left: 0.25rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-1,\n  .py-xl-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-xl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0 !important; }\n  .p-xl-2 {\n    padding: 0.5rem !important; }\n  .pt-xl-2,\n  .py-xl-2 {\n    padding-top: 0.5rem !important; }\n  .pr-xl-2 {\n    padding-left: 0.5rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-2,\n  .py-xl-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-xl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0 !important; }\n  .p-xl-3 {\n    padding: 1rem !important; }\n  .pt-xl-3,\n  .py-xl-3 {\n    padding-top: 1rem !important; }\n  .pr-xl-3 {\n    padding-left: 1rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-3,\n  .py-xl-3 {\n    padding-bottom: 1rem !important; }\n  .pl-xl-3 {\n    padding-right: 1rem !important;\n    padding-left: 0 !important; }\n  .p-xl-4 {\n    padding: 1.5rem !important; }\n  .pt-xl-4,\n  .py-xl-4 {\n    padding-top: 1.5rem !important; }\n  .pr-xl-4 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-4,\n  .py-xl-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-xl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-xl-5 {\n    padding: 3rem !important; }\n  .pt-xl-5,\n  .py-xl-5 {\n    padding-top: 3rem !important; }\n  .pr-xl-5 {\n    padding-left: 3rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-5,\n  .py-xl-5 {\n    padding-bottom: 3rem !important; }\n  .pl-xl-5 {\n    padding-right: 3rem !important;\n    padding-left: 0 !important; }\n  .m-xl-n1 {\n    margin: -0.25rem !important; }\n  .mt-xl-n1,\n  .my-xl-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-xl-n1,\n  .mx-xl-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-xl-n1,\n  .my-xl-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-xl-n1,\n  .mx-xl-n1 {\n    margin-left: -0.25rem !important; }\n  .m-xl-n2 {\n    margin: -0.5rem !important; }\n  .mt-xl-n2,\n  .my-xl-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-xl-n2,\n  .mx-xl-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-xl-n2,\n  .my-xl-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-xl-n2,\n  .mx-xl-n2 {\n    margin-left: -0.5rem !important; }\n  .m-xl-n3 {\n    margin: -1rem !important; }\n  .mt-xl-n3,\n  .my-xl-n3 {\n    margin-top: -1rem !important; }\n  .mr-xl-n3,\n  .mx-xl-n3 {\n    margin-right: -1rem !important; }\n  .mb-xl-n3,\n  .my-xl-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-xl-n3,\n  .mx-xl-n3 {\n    margin-left: -1rem !important; }\n  .m-xl-n4 {\n    margin: -1.5rem !important; }\n  .mt-xl-n4,\n  .my-xl-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-xl-n4,\n  .mx-xl-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-xl-n4,\n  .my-xl-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-xl-n4,\n  .mx-xl-n4 {\n    margin-left: -1.5rem !important; }\n  .m-xl-n5 {\n    margin: -3rem !important; }\n  .mt-xl-n5,\n  .my-xl-n5 {\n    margin-top: -3rem !important; }\n  .mr-xl-n5,\n  .mx-xl-n5 {\n    margin-right: -3rem !important; }\n  .mb-xl-n5,\n  .my-xl-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-xl-n5,\n  .mx-xl-n5 {\n    margin-left: -3rem !important; }\n  .m-xl-auto {\n    margin: auto !important; }\n  .mt-xl-auto,\n  .my-xl-auto {\n    margin-top: auto !important; }\n  .mr-xl-auto,\n  .mx-xl-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-xl-auto,\n  .my-xl-auto {\n    margin-bottom: auto !important; }\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; }\n  .ml-xl-auto {\n    margin-right: auto !important;\n    margin-left: 0 !important; } }\n\n.float-left {\n  float: right !important; }\n\n.float-right {\n  float: left !important; }\n\n.float-none {\n  float: none !important; }\n\n@media (min-width: 576px) {\n  .float-sm-left {\n    float: right !important; }\n  .float-sm-right {\n    float: left !important; }\n  .float-sm-none {\n    float: none !important; } }\n\n@media (min-width: 768px) {\n  .float-md-left {\n    float: right !important; }\n  .float-md-right {\n    float: left !important; }\n  .float-md-none {\n    float: none !important; } }\n\n@media (min-width: 992px) {\n  .float-lg-left {\n    float: right !important; }\n  .float-lg-right {\n    float: left !important; }\n  .float-lg-none {\n    float: none !important; } }\n\n@media (min-width: 1200px) {\n  .float-xl-left {\n    float: right !important; }\n  .float-xl-right {\n    float: left !important; }\n  .float-xl-none {\n    float: none !important; } }\n\n.text-left {\n  text-align: right !important; }\n\n.text-right {\n  text-align: left !important; }\n\n.text-center {\n  text-align: center !important; }\n\n@media (min-width: 576px) {\n  .text-sm-left {\n    text-align: right !important; }\n  .text-sm-right {\n    text-align: left !important; }\n  .text-sm-center {\n    text-align: center !important; } }\n\n@media (min-width: 768px) {\n  .text-md-left {\n    text-align: right !important; }\n  .text-md-right {\n    text-align: left !important; }\n  .text-md-center {\n    text-align: center !important; } }\n\n@media (min-width: 992px) {\n  .text-lg-left {\n    text-align: right !important; }\n  .text-lg-right {\n    text-align: left !important; }\n  .text-lg-center {\n    text-align: center !important; } }\n\n@media (min-width: 1200px) {\n  .text-xl-left {\n    text-align: right !important; }\n  .text-xl-right {\n    text-align: left !important; }\n  .text-xl-center {\n    text-align: center !important; } }\n\n.navbar-header {\n  padding: 0 0 0 calc(24px / 2); }\n\n.noti-icon .badge {\n  right: auto;\n  left: 4px; }\n\n.main-content {\n  margin-left: 0px;\n  margin-right: 250px; }\n\n.footer {\n  left: 0px;\n  right: 250px; }\n\n#sidebar-menu .has-arrow:after {\n  float: left; }\n\n#sidebar-menu ul li ul.sub-menu li a {\n  padding: .4rem 3.5rem .4rem 1.5rem; }\n\n#sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding: .4rem 4.5rem .4rem 1.5rem;\n  font-size: 13.5px; }\n\n.vertical-collpsed .main-content {\n  margin-left: 0px;\n  margin-right: 70px; }\n\n.vertical-collpsed .footer {\n  left: 0px;\n  right: 70px; }\n\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n  margin-left: 0;\n  margin-right: 4px; }\n\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a span {\n  padding-right: 25px;\n  padding-left: 0; }\n\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul {\n  left: 0;\n  right: 70px; }\n\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li:hover > ul {\n  left: auto;\n  right: 190px; }\n\n@media (max-width: 992px) {\n  .main-content {\n    margin-right: 0 !important; }\n  .footer {\n    right: 0; } }\n\n.right-bar {\n  float: left !important;\n  left: -290px;\n  right: auto; }\n  .right-bar .user-box .user-img .user-edit {\n    right: 0;\n    left: -5px; }\n\n.right-bar-enabled .right-bar {\n  left: 0;\n  right: auto; }\n\nbody[data-sidebar-size=small] #sidebar-menu ul li ul.sub-menu li a {\n  padding-right: 1.5rem; }\n\nbody[data-sidebar-size=small] #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding-right: 1.5rem; }\n\nbody[data-sidebar-size=small].vertical-collpsed .vertical-menu #sidebar-menu {\n  text-align: right; }\n\nbody[data-sidebar-size=small].vertical-collpsed .main-content {\n  margin-left: 0;\n  margin-right: 70px; }\n\nbody[data-sidebar-size=small].vertical-collpsed body[data-sidebar-size=small].vertical-collpsed .footer {\n  left: 70px; }\n\nbody[data-sidebar-size=small] .main-content {\n  margin-right: 160px;\n  margin-left: 0; }\n\nbody[data-layout=horizontal] .main-content {\n  margin-right: 0 !important; }\n\nbody[data-layout=horizontal] .footer {\n  right: 0 !important; }\n\n@media (min-width: 992px) {\n  .topnav .navbar-nav .nav-item:first-of-type .nav-link {\n    padding-left: 1.3rem;\n    padding-right: 0; }\n  .topnav .dropdown .dropdown-menu {\n    right: 0px;\n    left: auto; } }\n\n.arrow-down:after {\n  margin-left: 0px;\n  margin-right: 10px; }\n\n.navbar-nav .dropdown-menu {\n  text-align: right; }\n\n@media (min-width: 992px) {\n  .topnav .dropdown .dropdown-menu .arrow-down::after {\n    right: auto;\n    left: 15px;\n    transform: rotate(45deg) translateY(-50%); }\n  .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {\n    right: 100%;\n    left: auto; } }\n\n@media (max-width: 991.98px) {\n  .topnav .dropdown .dropdown-menu {\n    padding-left: 0px;\n    padding-right: 15px; } }\n\n.lnb-calendars-item {\n  margin-right: 0;\n  margin-left: 7px; }\n\ninput[type=checkbox].tui-full-calendar-checkbox-round + span {\n  margin-right: 0px;\n  margin-left: 8px; }\n\n.tui-full-calendar-time-schedule-content {\n  padding: 1px 3px 0 0; }\n\n.legendLabel {\n  padding-right: 5px !important;\n  padding-left: 20px; }\n\n.select2-container .select2-selection--single .select2-selection__rendered {\n  padding-right: 12px; }\n\n.select2-container .select2-selection--single .select2-selection__arrow {\n  left: 3px;\n  right: auto; }\n\n.select2-container .select2-selection--multiple .select2-selection__choice {\n  float: right;\n  margin-left: 5px;\n  margin-right: 0; }\n\n.select2-container .select2-search__field {\n  text-align: right; }\n\n.select2-container .select2-search--inline {\n  float: right; }\n\n.bootstrap-select .dropdown-toggle:before {\n  float: left; }\n\n.bootstrap-select .dropdown-toggle .filter-option {\n  text-align: right; }\n\n.bootstrap-select .dropdown-toggle .filter-option-inner {\n  padding-right: 0;\n  padding-left: inherit; }\n\n.dataTables_wrapper .dataTables_filter {\n  text-align: left !important; }\n  .dataTables_wrapper .dataTables_filter input {\n    margin-left: 0px !important;\n    margin-right: 0.5em; }\n\n.footable.breakpoint > tbody > tr > td > span.footable-toggle {\n  padding-left: 5px;\n  padding-right: 0; }\n\n.tablesaw-columntoggle-popup .tablesaw-btn-group > label input {\n  margin-right: 0;\n  margin-left: .8em; }\n\n.tablesaw-bar .tablesaw-bar-section .tablesaw-btn {\n  margin-left: 0;\n  margin-right: .4em; }\n\n.table-rep-plugin .btn-group.pull-right {\n  float: left; }\n\n.table-rep-plugin .checkbox-row label:after {\n  margin-left: -22px;\n  top: -2px; }\n\n.parsley-errors-list > li {\n  padding-left: 0;\n  padding-right: 20px; }\n  .parsley-errors-list > li:before {\n    left: auto;\n    right: 2px; }\n\n/* =============\r\n   Form wizard\r\n============= */\n@media (min-width: 992px) {\n  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link {\n    text-align: right; } }\n\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li.next {\n  float: left; }\n\n.editable-buttons {\n  margin-left: 0;\n  margin-right: 7px; }\n  .editable-buttons .editable-cancel {\n    margin-left: 0;\n    margin-right: 7px; }\n\n.dropdown-megamenu.show {\n  left: 20px !important; }\n\n.icon-list-demo i {\n  margin-left: 12px;\n  margin-right: 0; }\n\n@media print {\n  .content-page,\n  .content,\n  body {\n    margin-right: 0; } }\n\n.demos-show-btn {\n  left: 0;\n  right: auto;\n  border-radius: 0 6px 6px 0; }\n\n.verti-timeline {\n  border-left: 0;\n  border-right: 3px dashed #f6f6f6; }\n  .verti-timeline .event-list {\n    padding: 0 30px 40px 0; }\n    .verti-timeline .event-list .event-timeline-dot {\n      left: auto;\n      right: -9px; }\n\n.email-leftbar {\n  float: right; }\n\n.email-rightbar {\n  margin-right: 260px;\n  margin-left: 0px; }\n\n.message-list li .col-mail {\n  float: right; }\n\n.message-list li .col-mail-1 .star-toggle, .message-list li .col-mail-1 .checkbox-wrapper-mail, .message-list li .col-mail-1 .dot {\n  float: right; }\n\n.message-list li .col-mail-1 .checkbox-wrapper-mail {\n  margin: 15px 20px 0 10px; }\n\n.message-list li .col-mail-1 .star-toggle {\n  margin-right: 5px; }\n\n.message-list li .col-mail-1 .title {\n  right: 110px;\n  left: 0; }\n\n.message-list li .col-mail-2 {\n  right: 320px;\n  left: 0; }\n  .message-list li .col-mail-2 .subject {\n    right: 0;\n    left: 200px; }\n  .message-list li .col-mail-2 .date {\n    left: 0;\n    right: auto;\n    padding-right: 80px;\n    padding-left: 0px; }\n\n.message-list .checkbox-wrapper-mail label:before {\n  right: 4px; }\n\n@media (max-width: 767px) {\n  .email-leftbar {\n    float: none !important;\n    width: 100%; }\n  .email-rightbar {\n    margin-right: 0; } }\n\n.home-btn {\n  position: absolute;\n  left: 25px;\n  right: auto; }\n\n.categories-list li ul {\n  padding-right: 16px;\n  padding-left: 0; }\n\n.chat-conversation .conversation-list .dropdown {\n  float: left; }\n\n.chat-conversation .conversation-list .ctext-wrap {\n  border-radius: 8px 8px 0 8px; }\n\n.chat-conversation .right {\n  float: left; }\n  .chat-conversation .right .conversation-list .ctext-wrap {\n    text-align: left;\n    border-radius: 8px 8px 8px 0; }\n  .chat-conversation .right .conversation-list .dropdown {\n    float: right; }\n\n.chat-conversation .last-chat .conversation-list:before {\n  right: auto;\n  left: 0; }\n\n.chat-input {\n  padding-left: 120px;\n  padding-right: 12px; }\n\n.chat-input-links {\n  right: auto;\n  left: 16px; }\n\n@media (min-width: 576px) {\n  .currency-value {\n    position: relative; }\n    .currency-value:after {\n      right: auto;\n      left: -34px; } }\n", "// \r\n// Page-title\r\n// \r\n\r\n.page-title-box {\r\n    padding-bottom: $grid-gutter-width;\r\n\r\n    .breadcrumb {\r\n        background-color: transparent;\r\n        padding: 0;\r\n    }\r\n\r\n    h4 {\r\n        text-transform: uppercase;\r\n        font-weight: 600;\r\n        font-size: 16px !important;\r\n    }\r\n}", "// \r\n// _footer.scss\r\n// \r\n\r\n.footer {\r\n    bottom: 0;\r\n    padding: 20px calc(#{$grid-gutter-width} / 2);\r\n    position: absolute;\r\n    right: 0;\r\n    color: $footer-color;\r\n    left: 250px;\r\n    height: $footer-height;\r\n    background-color: $footer-bg;\r\n}\r\n  \r\n@media (max-width: 992px) {\r\n    .footer {\r\n        left: 0;\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n    .footer {\r\n        left: $sidebar-collapsed-width;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .footer {\r\n        left: 0 !important;\r\n    }  \r\n}", "//\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    background-color: $card-bg;\r\n    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n    display: block;\r\n    position: fixed;\r\n    transition: all 200ms ease-out;\r\n    width: $rightbar-width;\r\n    z-index: 9999;\r\n    float: right !important;\r\n    right: -($rightbar-width + 10px);\r\n    top: 0;\r\n    bottom: 0;\r\n\r\n    .right-bar-toggle {\r\n        background-color: lighten($dark, 7%);\r\n        height: 24px;\r\n        width: 24px;\r\n        line-height: 24px;\r\n        color: $gray-200;\r\n        text-align: center;\r\n        border-radius: 50%;\r\n\r\n        &:hover {\r\n            background-color: lighten($dark, 10%);\r\n        }\r\n    }\r\n}\r\n\r\n// Rightbar overlay\r\n.rightbar-overlay {\r\n    background-color: rgba($dark, 0.55);\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    bottom: 0;\r\n    display: none;\r\n    z-index: 9998;\r\n    transition: all .2s ease-out;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        right: 0;\r\n    }\r\n    .rightbar-overlay {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .right-bar {\r\n        overflow: auto;\r\n        .slimscroll-menu {\r\n            height: auto !important;\r\n        }\r\n    }\r\n}", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\r\n// _menu.scss\r\n// \r\n\r\n.metismenu {\r\n    margin: 0;\r\n\r\n    li {\r\n        display: block;\r\n        width: 100%;\r\n    }\r\n\r\n    .mm-collapse {\r\n        display: none;\r\n\r\n        &:not(.mm-show) {\r\n            display: none;\r\n        }\r\n\r\n        &.mm-show {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .mm-collapsing {\r\n        position: relative;\r\n        height: 0;\r\n        overflow: hidden;\r\n        transition-timing-function: ease;\r\n        transition-duration: .35s;\r\n        transition-property: height, visibility;\r\n    }\r\n}\r\n\r\n\r\n.vertical-menu {\r\n    width: $sidebar-width;\r\n    z-index: 1001;\r\n    background: $sidebar-bg;\r\n    bottom: 0;\r\n    margin-top: 0;\r\n    position: fixed;\r\n    top: $header-height;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.main-content {\r\n    margin-left: $sidebar-width;\r\n    overflow: hidden;\r\n\r\n    .content {\r\n        padding: 0 15px 10px 15px;\r\n        margin-top: $header-height;\r\n    }\r\n}\r\n\r\n\r\n#sidebar-menu {\r\n    padding: 10px 0 30px 0;\r\n\r\n    .mm-active {\r\n        >.has-arrow {\r\n            &:after {\r\n                transform: rotate(-180deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .has-arrow {\r\n        &:after {\r\n            content: \"\\F0140\";\r\n            font-family: 'Material Design Icons';\r\n            display: block;\r\n            float: right;\r\n            transition: transform .2s;\r\n            font-size: 1rem;\r\n        }\r\n    }\r\n\r\n    ul {\r\n        li {\r\n            a {\r\n                display: block;\r\n                padding: .625rem 1.5rem;\r\n                color: $sidebar-menu-item-color;\r\n                position: relative;\r\n                font-size: 13px;\r\n                transition: all .4s;\r\n\r\n                i {\r\n                    display: inline-block;\r\n                    min-width: 1.75rem;\r\n                    padding-bottom: .125em;\r\n                    font-size: 1.25rem;\r\n                    line-height: 1.40625rem;\r\n                    vertical-align: middle;\r\n                    color: $sidebar-menu-item-icon-color;\r\n                    transition: all .4s;\r\n                }\r\n\r\n                &:hover {\r\n                    color: $sidebar-menu-item-hover-color;\r\n\r\n                    i {\r\n                        color: $sidebar-menu-item-hover-color;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .badge {\r\n                margin-top: 4px;\r\n            }\r\n\r\n            ul.sub-menu {\r\n                padding: 0;\r\n\r\n                li {\r\n\r\n                    a {\r\n                        padding: .4rem 1.5rem .4rem 3.5rem;\r\n                        font-size: 13px;\r\n                        color: $sidebar-menu-sub-item-color;\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n                        padding: 0;\r\n\r\n                        li {\r\n                            a {\r\n                                padding: .4rem 1.5rem .4rem 4.5rem;\r\n                                font-size: 13.5px;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n.menu-title {\r\n    padding: 12px 20px !important;\r\n    letter-spacing: .05em;\r\n    pointer-events: none;\r\n    cursor: default;\r\n    font-size: 11px;\r\n    text-transform: uppercase;\r\n    color: $sidebar-menu-item-icon-color;\r\n    font-weight: $font-weight-semibold;\r\n}\r\n\r\n.mm-active {\r\n    color: $sidebar-menu-item-active-color !important;\r\n    > a{\r\n        color: $sidebar-menu-item-active-color !important;\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n    .active {\r\n        color: $sidebar-menu-item-active-color !important;\r\n\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n    > i {\r\n        color: $sidebar-menu-item-active-color !important;\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .vertical-menu {\r\n        display: none;\r\n    }\r\n\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    body.sidebar-enable {\r\n        .vertical-menu {\r\n            display: block;\r\n        }\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n\r\n    .main-content {\r\n        margin-left: $sidebar-collapsed-width;\r\n    }\r\n\r\n    .navbar-brand-box {\r\n        width: $sidebar-collapsed-width !important;\r\n    }\r\n\r\n    .logo {\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    // Side menu\r\n    .vertical-menu {\r\n        position: absolute;\r\n        width: $sidebar-collapsed-width !important;\r\n        z-index: 5;\r\n\r\n        .simplebar-mask,\r\n        .simplebar-content-wrapper {\r\n            overflow: visible !important;\r\n        }\r\n\r\n        .simplebar-scrollbar {\r\n            display: none !important;\r\n        }\r\n\r\n        .simplebar-offset {\r\n            bottom: 0 !important;\r\n        }\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n\r\n            .menu-title,\r\n            .badge,\r\n            .collapse.in {\r\n                display: none !important;\r\n            }\r\n\r\n            .nav.collapse {\r\n                height: inherit !important;\r\n            }\r\n\r\n            .has-arrow {\r\n                &:after {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            > ul {\r\n                > li {\r\n                    position: relative;\r\n                    white-space: nowrap;\r\n\r\n                    > a {\r\n                        padding: 15px 20px;\r\n                        min-height: 55px;\r\n                        transition: none;\r\n                        \r\n                        &:hover,\r\n                        &:active,\r\n                        &:focus {\r\n                            color: $sidebar-menu-item-hover-color;\r\n                        }\r\n\r\n                        i {\r\n                            font-size: 1.45rem;\r\n                            margin-left: 4px;\r\n                        }\r\n\r\n                        span {\r\n                            display: none;\r\n                            padding-left: 25px;\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n                        > a {\r\n                            position: relative;\r\n                            width: calc(190px + #{$sidebar-collapsed-width});\r\n                            color: $primary;\r\n                            background-color: darken($sidebar-bg, 4%);\r\n                            transition: none;\r\n\r\n                            i{\r\n                                color: $primary;\r\n                            }\r\n\r\n                            span {\r\n                                display: inline;\r\n                            }\r\n                        }\r\n\r\n                        >ul {\r\n                            display: block;\r\n                            left: $sidebar-collapsed-width;\r\n                            position: absolute;\r\n                            width: 190px;\r\n                            height: auto !important;\r\n                            box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, .1);\r\n\r\n                            ul {\r\n                                box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, .1);\r\n                            }\r\n\r\n                            a {\r\n                                box-shadow: none;\r\n                                padding: 8px 20px;\r\n                                position: relative;\r\n                                width: 190px;\r\n                                z-index: 6;\r\n                                color: $sidebar-menu-sub-item-color;\r\n\r\n                                &:hover {\r\n                                    color: $sidebar-menu-item-hover-color;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n                    padding: 5px 0;\r\n                    z-index: 9999;\r\n                    display: none;\r\n                    background-color: $sidebar-bg;\r\n\r\n                    li {\r\n                        &:hover {\r\n                            >ul {\r\n                                display: block;\r\n                                left: 190px;\r\n                                height: auto !important;\r\n                                margin-top: -36px;\r\n                                position: absolute;\r\n                                width: 190px;\r\n                            }\r\n                        }\r\n\r\n                        >a {\r\n                            span.pull-right {\r\n                                position: absolute;\r\n                                right: 20px;\r\n                                top: 12px;\r\n                                transform: rotate(270deg);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li.active {\r\n                        a {\r\n                            color: $gray-100;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n    .vertical-menu {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    #sidebar-menu {\r\n    \r\n        ul {\r\n            li {\r\n                a {\r\n                    color: $sidebar-dark-menu-item-color;\r\n\r\n                    i {\r\n                        color: $sidebar-dark-menu-item-icon-color;\r\n                    }\r\n    \r\n                    &:hover {\r\n                        color: $sidebar-dark-menu-item-hover-color;\r\n\r\n                        i {\r\n                            color: $sidebar-dark-menu-item-hover-color;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul.sub-menu {\r\n                    li {\r\n\r\n                        a {\r\n                            color: $sidebar-dark-menu-sub-item-color;\r\n\r\n                            &:hover {\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // Enlarge menu\r\n    &.vertical-collpsed {\r\n        min-height: 1760px;\r\n        // Side menu\r\n        .vertical-menu {\r\n\r\n            // Sidebar Menu\r\n            #sidebar-menu {\r\n\r\n                > ul {\r\n                    > li {\r\n                        \r\n                        &:hover {\r\n                            > a {\r\n                                background: lighten($sidebar-dark-bg, 2%);\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                                i{\r\n                                    color: $sidebar-dark-menu-item-hover-color;\r\n                                }\r\n                            }\r\n\r\n                            >ul {\r\n                                a{\r\n                                    color: $sidebar-dark-menu-sub-item-color;\r\n                                    &:hover{\r\n                                        color: $sidebar-dark-menu-item-hover-color;\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    ul{\r\n                        background-color: $sidebar-dark-bg;\r\n                    }\r\n                    \r\n                }\r\n\r\n                ul{\r\n                    \r\n                    li{\r\n                        &.mm-active .active{\r\n                            color: $sidebar-dark-menu-item-active-color !important;\r\n                            i{\r\n                                color: $sidebar-dark-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n    .mm-active {\r\n        color: $sidebar-dark-menu-item-active-color !important;\r\n        > a{\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n        > i {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n        }\r\n        .active {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        color: $sidebar-dark-menu-item-icon-color;\r\n    }\r\n}\r\n\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n}\r\n\r\n// Compact Sidebar\r\n\r\nbody[data-sidebar-size=\"small\"] {\r\n    .navbar-brand-box{\r\n        width: $sidebar-width-sm;\r\n    }\r\n    .vertical-menu{\r\n        width: $sidebar-width-sm;\r\n        text-align: center;\r\n\r\n        .has-arrow:after,\r\n        .badge {\r\n            display: none !important;\r\n        }\r\n    }\r\n    .main-content {\r\n        margin-left: $sidebar-width-sm;\r\n    }\r\n    .footer {\r\n        left: $sidebar-width-sm;\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul li {\r\n            &.menu-title{\r\n                background-color: lighten($sidebar-dark-bg, 2%);\r\n            }\r\n            a{\r\n                i{\r\n                    display: block;\r\n                }\r\n            }\r\n            ul.sub-menu {\r\n                li {\r\n                    a{\r\n                        padding-left: 1.5rem;\r\n                    }\r\n\r\n                    ul.sub-menu li a {\r\n                        padding-left: 1.5rem;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.vertical-collpsed {\r\n        .main-content {\r\n            margin-left: $sidebar-collapsed-width;\r\n        }\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                text-align: left;\r\n                >ul{\r\n                    >li{\r\n                        >a {\r\n                            i{\r\n                                display: inline-block;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .footer {\r\n            left: $sidebar-collapsed-width;\r\n        }\r\n    }\r\n}\r\n\r\n// colored sidebar\r\n\r\nbody[data-sidebar=\"colored\"] {\r\n    .vertical-menu{\r\n        background-color: $primary;\r\n    }\r\n    .navbar-brand-box{\r\n        background-color: $primary;\r\n        .logo-dark{\r\n            display: none;\r\n        }\r\n        .logo-light{\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    .mm-active {\r\n        color: $white !important;\r\n        > a{\r\n            color: $white !important;\r\n            i {\r\n                color: $white !important;\r\n            }\r\n        }\r\n        > i, .active {\r\n            color: $white !important;\r\n        }\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul {\r\n            li {\r\n                &.menu-title{\r\n                    color: rgba($white, 0.6);\r\n                }\r\n\r\n                a{\r\n                    color: rgba($white, 0.6);\r\n                    i{\r\n                        color: rgba($white, 0.6);\r\n                    }\r\n                    &.waves-effect {\r\n                        .waves-ripple {\r\n                          background: rgba($white, 0.1);\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n                        color: $white;\r\n    \r\n                        i {\r\n                            color: $white;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul.sub-menu {\r\n                    li {\r\n                        a{\r\n                            color: rgba($white,.5);\r\n                            &:hover {\r\n                                color: $white;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &.vertical-collpsed {\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                >ul{\r\n                    >li{\r\n                        &:hover>a{\r\n                            background-color: lighten($primary, 2%);\r\n                            color: $white;\r\n                            i{\r\n                                color: $white;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul{\r\n                    li{\r\n                        &.mm-active {\r\n                            .active{\r\n                                color: $sidebar-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n\r\n                        ul.sub-menu {\r\n                            li {\r\n                                a{\r\n                                    &:hover {\r\n                                        color: $sidebar-menu-item-active-color;\r\n                                    }\r\n                                }\r\n                                &.mm-active {\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                    > a{\r\n                                        color: $sidebar-menu-item-active-color !important;\r\n                                        i {\r\n                                            color: $sidebar-menu-item-active-color !important;\r\n                                        }\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n", "// \r\n// _horizontal.scss\r\n// \r\n\r\n.topnav {\r\n    background: $topnav-bg;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2);\r\n    box-shadow: $box-shadow;\r\n    margin-top: $header-height;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    z-index: 100;\r\n    \r\n    .topnav-menu {\r\n        margin: 0;\r\n        padding: 0;\r\n    }\r\n\r\n    .navbar-nav {\r\n        \r\n        .nav-link {\r\n            font-size: 14px;\r\n            position: relative;\r\n            padding: 1rem 1.3rem;\r\n            color: $menu-item-color;\r\n            i{\r\n                font-size: 15px;\r\n            }\r\n            &:focus, &:hover{\r\n                color: $menu-item-active-color;\r\n                background-color: transparent;\r\n            }\r\n        }\r\n        \r\n        .dropdown-item{\r\n            color: $menu-item-color;\r\n            &.active, &:hover{\r\n                color: $menu-item-active-color;\r\n            }\r\n        }\r\n        \r\n        .nav-item{\r\n            .nav-link.active{\r\n                color: $menu-item-active-color;\r\n            }\r\n        }\r\n\r\n        .dropdown{\r\n            &.active{\r\n              >a {\r\n                    color: $menu-item-active-color;\r\n                    background-color: transparent;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n\r\n    body[data-layout=\"horizontal\"] {\r\n        .container-fluid,\r\n        .navbar-header {\r\n            max-width: 85%;\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n    .topnav {\r\n        .navbar-nav {\r\n            .nav-item {\r\n                &:first-of-type {\r\n                    .nav-link {\r\n                        padding-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown-item {\r\n            padding: .5rem 1.5rem;\r\n            min-width: 180px;\r\n        }\r\n\r\n        .dropdown {\r\n            &.mega-dropdown{\r\n                // position: static;\r\n                .mega-dropdown-menu{\r\n                    left: 0px;\r\n                    right: auto;\r\n                }\r\n            }\r\n            .dropdown-menu {\r\n                margin-top: 0;\r\n                border-radius: 0 0 $dropdown-border-radius $dropdown-border-radius;\r\n\r\n                .arrow-down {\r\n                    &::after {\r\n                        right: 15px;\r\n                        transform: rotate(-135deg) translateY(-50%);\r\n                        position: absolute;\r\n                    }\r\n                }\r\n\r\n                .dropdown {\r\n                    .dropdown-menu {\r\n                        position: absolute;\r\n                        top: 0 !important;\r\n                        left: 100%;\r\n                        display: none\r\n                    }\r\n                }\r\n            }\r\n\r\n            &:hover {\r\n                >.dropdown-menu {\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown:hover>.dropdown-menu>.dropdown:hover>.dropdown-menu {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .navbar-toggle {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.arrow-down {\r\n    display: inline-block;\r\n\r\n    &:after {\r\n        border-color: initial;\r\n        border-style: solid;\r\n        border-width: 0 0 1px 1px;\r\n        content: \"\";\r\n        height: .4em;\r\n        display: inline-block;\r\n        right: 5px;\r\n        top: 50%;\r\n        margin-left: 10px;\r\n        transform: rotate(-45deg) translateY(-50%);\r\n        transform-origin: top;\r\n        transition: all .3s ease-out;\r\n        width: .4em;\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(lg) {\r\n    .topnav-menu {\r\n        .navbar-nav {\r\n            li {\r\n                &:last-of-type {\r\n                    .dropdown {\r\n                        .dropdown-menu {\r\n                            right: 100%;\r\n                            left: auto;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(md) {\r\n\r\n    .navbar-brand-box{\r\n        .logo-dark {\r\n            display: $display-block;\r\n            span.logo-sm{\r\n                display: $display-block;\r\n            }\r\n        }\r\n    \r\n        .logo-light {\r\n            display: $display-none;\r\n        }\r\n    }\r\n    \r\n    .topnav {\r\n        max-height: 360px;\r\n        overflow-y: auto;\r\n        padding: 0;\r\n        .navbar-nav {\r\n            .nav-link {\r\n                padding: 0.75rem 1.1rem;\r\n            }\r\n        }\r\n\r\n        .dropdown {\r\n            .dropdown-menu {\r\n                background-color: transparent;\r\n                border: none;\r\n                box-shadow: none;\r\n                padding-left: 15px;\r\n                &.dropdown-mega-menu-xl{\r\n                    width: auto;\r\n    \r\n                    .row{\r\n                        margin: 0px;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .dropdown-item {\r\n                position: relative;\r\n                background-color: transparent;\r\n\r\n                &.active,\r\n                &:active {\r\n                    color: $primary;\r\n                }\r\n            }\r\n        }\r\n\r\n        .arrow-down {\r\n            &::after {\r\n                right: 15px;\r\n                position: absolute;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-up(lg) {\r\n\r\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] {\r\n        .navbar-brand-box{\r\n            .logo-dark {\r\n                display: $display-block;\r\n            }\r\n        \r\n            .logo-light {\r\n                display: $display-none;\r\n            }\r\n        }\r\n\r\n        .topnav{\r\n            background-color: $primary;\r\n            .navbar-nav {\r\n        \r\n                .nav-link {\r\n                    color: rgba($white, 0.6);\r\n                    \r\n                    &:focus, &:hover{\r\n                        color: rgba($white, 0.9);\r\n                    }\r\n                }\r\n        \r\n                > .dropdown{\r\n                    &.active{\r\n                    >a {\r\n                            color: rgba($white, 0.9) !important;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Colored Topbar \r\n\r\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] {\r\n    #page-topbar{\r\n        background-color: $primary;\r\n        box-shadow: none;\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba($topbar-search-bg,0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n    .header-item {\r\n        color: $header-dark-item-color;\r\n    \r\n        &:hover {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .navbar-header {\r\n        .dropdown.show {\r\n            .header-item{\r\n                background-color: rgba($white,0.1);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .noti-icon {\r\n        i {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    @include media-breakpoint-up(lg) {\r\n        .topnav{\r\n            background-color: $primary;\r\n            .navbar-nav {\r\n        \r\n                .nav-link {\r\n                    color: rgba($white, 0.6);\r\n                    \r\n                    &:focus, &:hover{\r\n                        color: rgba($white, 0.9);\r\n                    }\r\n                }\r\n        \r\n                > .dropdown{\r\n                    &.active{\r\n                      >a {\r\n                            color: rgba($white, 0.9) !important;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n", "// \r\n// _layouts.scss\r\n// \r\n\r\nbody[data-layout-size=\"boxed\"] {\r\n    background-color: $boxed-body-bg;\r\n    #layout-wrapper {\r\n        background-color: $body-bg;\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n        box-shadow: $box-shadow;\r\n    }\r\n\r\n    #page-topbar {\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n    }\r\n\r\n    .footer {\r\n        margin: 0 auto;\r\n        max-width: calc(#{$boxed-layout-width} - #{$sidebar-width});\r\n    }\r\n\r\n    &.vertical-collpsed {\r\n        .footer {\r\n            max-width: calc(#{$boxed-layout-width} - #{$sidebar-collapsed-width});\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Horizontal Boxed Layout\r\n\r\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"]{\r\n    #page-topbar, #layout-wrapper, .footer {\r\n        max-width: 100%;\r\n    }\r\n    .container-fluid, .navbar-header {\r\n        max-width: $boxed-layout-width;\r\n    }\r\n}\r\n\r\n// Scrollable layout\r\n\r\nbody[data-layout-scrollable=\"true\"] {\r\n    @media (min-width: 992px) {\r\n        #page-topbar, .vertical-menu{\r\n            position: absolute;\r\n        }\r\n    }\r\n\r\n    &[data-layout=\"horizontal\"]{\r\n        @media (min-width: 992px) {\r\n            #page-topbar, .topnav{\r\n                position: absolute;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n", "\r\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n    position: relative;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -webkit-tap-highlight-color: transparent;\r\n  }\r\n  .waves-effect .waves-ripple {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    width: 100px;\r\n    height: 100px;\r\n    margin-top: -50px;\r\n    margin-left: -50px;\r\n    opacity: 0;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    -webkit-transition: all 0.5s ease-out;\r\n    -moz-transition: all 0.5s ease-out;\r\n    -o-transition: all 0.5s ease-out;\r\n    transition: all 0.5s ease-out;\r\n    -webkit-transition-property: -webkit-transform, opacity;\r\n    -moz-transition-property: -moz-transform, opacity;\r\n    -o-transition-property: -o-transform, opacity;\r\n    transition-property: transform, opacity;\r\n    -webkit-transform: scale(0) translate(0, 0);\r\n    -moz-transform: scale(0) translate(0, 0);\r\n    -ms-transform: scale(0) translate(0, 0);\r\n    -o-transform: scale(0) translate(0, 0);\r\n    transform: scale(0) translate(0, 0);\r\n    pointer-events: none;\r\n  }\r\n  .waves-effect.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n    background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  }\r\n  .waves-effect.waves-classic .waves-ripple {\r\n    background: rgba(0, 0, 0, 0.2);\r\n  }\r\n  .waves-effect.waves-classic.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n  }\r\n  .waves-notransition {\r\n    -webkit-transition: none !important;\r\n    -moz-transition: none !important;\r\n    -o-transition: none !important;\r\n    transition: none !important;\r\n  }\r\n  .waves-button,\r\n  .waves-circle {\r\n    -webkit-transform: translateZ(0);\r\n    -moz-transform: translateZ(0);\r\n    -ms-transform: translateZ(0);\r\n    -o-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n  }\r\n  .waves-button,\r\n  .waves-button:hover,\r\n  .waves-button:visited,\r\n  .waves-button-input {\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    cursor: pointer;\r\n    border: none;\r\n    outline: none;\r\n    color: inherit;\r\n    background-color: rgba(0, 0, 0, 0);\r\n    font-size: 1em;\r\n    line-height: 1em;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    z-index: 1;\r\n  }\r\n  .waves-button {\r\n    padding: 0.85em 1.1em;\r\n    border-radius: 0.2em;\r\n  }\r\n  .waves-button-input {\r\n    margin: 0;\r\n    padding: 0.85em 1.1em;\r\n  }\r\n  .waves-input-wrapper {\r\n    border-radius: 0.2em;\r\n    vertical-align: bottom;\r\n  }\r\n  .waves-input-wrapper.waves-button {\r\n    padding: 0;\r\n  }\r\n  .waves-input-wrapper .waves-button-input {\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n  .waves-circle {\r\n    text-align: center;\r\n    width: 2.5em;\r\n    height: 2.5em;\r\n    line-height: 2.5em;\r\n    border-radius: 50%;\r\n  }\r\n  .waves-float {\r\n    -webkit-mask-image: none;\r\n    -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    -webkit-transition: all 300ms;\r\n    -moz-transition: all 300ms;\r\n    -o-transition: all 300ms;\r\n    transition: all 300ms;\r\n  }\r\n  .waves-float:active {\r\n    -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n    box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  }\r\n  .waves-block {\r\n    display: block;\r\n  }\r\n\r\n.waves-effect.waves-light {\r\n    .waves-ripple {\r\n        background-color: rgba($white, 0.4);\r\n    }\r\n}\r\n\r\n.waves-effect.waves-primary {\r\n    .waves-ripple {\r\n        background-color: rgba($primary, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-success {\r\n    .waves-ripple {\r\n        background-color: rgba($success, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-info {\r\n    .waves-ripple {\r\n        background-color: rgba($info, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-warning {\r\n    .waves-ripple {\r\n        background-color: rgba($warning, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-danger {\r\n    .waves-ripple {\r\n        background-color: rgba($danger, 0.4);\r\n    }\r\n}", "//\n// avatar.scss\n//\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem;\n}\n\n.avatar-sm {\n  height: 3rem;\n  width: 3rem;\n}\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: $primary;\n  color: $white;\n  display: flex;\n  font-weight: $font-weight-medium;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}", "\r\n//\r\n// accordion.scss\r\n//\r\n\r\n.custom-accordion {\r\n    .accordion-list {\r\n        display: flex;\r\n        border-radius: 7px;\r\n        background-color: $gray-300;\r\n        padding: 12px 20px;\r\n        color: $body-color;\r\n        font-weight: $font-weight-semibold;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        &.collapsed {\r\n            i.accor-plus-icon {\r\n                &:before {\r\n                    content: \"\\F0415\";\r\n                }\r\n            }\r\n        }\r\n\r\n        .accor-plus-icon{\r\n            display: inline-block;\r\n            font-size: 16px;\r\n            height: 24px;\r\n            width: 24px;\r\n            line-height: 22px;\r\n            background-color: $card-bg;\r\n            text-align: center;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n\r\n    \r\n    a {\r\n\r\n        &.collapsed {\r\n            i.accor-down-icon {\r\n                &:before {\r\n                    content: \"\\F0140\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    .card-body {\r\n        color: $text-muted;\r\n    }\r\n}", "//\n// _helper.scss\n//\n\n.font-size-10 {\n  font-size: 10px !important;\n}\n\n.font-size-11 {\n  font-size: 11px !important;\n}\n\n.font-size-12 {\n  font-size: 12px !important;\n}\n\n.font-size-13 {\n  font-size: 13px !important;\n}\n\n.font-size-14 {\n  font-size: 14px !important;\n}\n\n.font-size-15 {\n  font-size: 15px !important;\n}\n\n.font-size-16 {\n  font-size: 16px !important;\n}\n\n.font-size-17 {\n  font-size: 17px !important;\n}\n\n.font-size-18 {\n  font-size: 18px !important;\n}\n\n.font-size-20 {\n  font-size: 20px !important;\n}\n\n.font-size-22 {\n  font-size: 22px !important;\n}\n\n.font-size-24 {\n  font-size: 24px !important;\n}\n\n// Font weight help class\n\n.font-weight-medium {\n  font-weight: $font-weight-medium;\n}\n\n.font-weight-semibold {\n  font-weight: $font-weight-semibold;\n}\n\n// Social\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 4px);\n  display: block;\n  border: 2px solid $gray-500;\n  border-radius: 50%;\n  color: $gray-500;\n  text-align: center;\n  transition: all 0.4s;\n  &:hover{\n    color: $gray-600;\n    background-color: $gray-200;\n  }\n}\n\n\n.w-xs {\n  min-width: 80px;\n}\n.w-sm {\n  min-width: 95px;\n}\n.w-md {\n  min-width: 110px;\n}\n.w-lg {\n  min-width: 140px;\n}\n\n.w-xl {\n  min-width: 160px;\n}", "// \r\n// preloader.scss\r\n//\r\n\r\n#preloader {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: $card-bg;\r\n    z-index: 9999;\r\n}\r\n\r\n#status {\r\n    width: 40px;\r\n    height: 40px;\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 50%;\r\n    margin: -20px 0 0 -20px;\r\n}\r\n\r\n.spinner-chase {\r\n    margin: 0 auto;\r\n    width: 40px;\r\n    height: 40px;\r\n    position: relative;\r\n    animation: spinner-chase 2.5s infinite linear both;\r\n}\r\n\r\n.chase-dot {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0; \r\n    animation: chase-dot 2.0s infinite ease-in-out both; \r\n    &:before {\r\n        content: '';\r\n        display: block;\r\n        width: 25%;\r\n        height: 25%;\r\n        background-color: $primary;\r\n        border-radius: 100%;\r\n        animation: chase-dot-before 2.0s infinite ease-in-out both; \r\n    }\r\n\r\n    &:nth-child(1) { \r\n        animation-delay: -1.1s; \r\n        &:before{\r\n            animation-delay: -1.1s;\r\n        }\r\n    }\r\n    &:nth-child(2) { \r\n        animation-delay: -1.0s;\r\n        &:before{\r\n            animation-delay: -1.0s;\r\n        }\r\n    }\r\n    &:nth-child(3) { \r\n        animation-delay: -0.9s;\r\n        &:before{\r\n            animation-delay: -0.9s;\r\n        } \r\n    }\r\n    &:nth-child(4) { \r\n        animation-delay: -0.8s; \r\n        &:before{\r\n            animation-delay: -0.8s;\r\n        } \r\n    }\r\n    &:nth-child(5) { \r\n        animation-delay: -0.7s; \r\n        &:before{\r\n            animation-delay: -0.7s;\r\n        } \r\n    }\r\n    &:nth-child(6) { \r\n        animation-delay: -0.6s; \r\n        &:before{\r\n            animation-delay: -0.6s;\r\n        }\r\n    }\r\n}\r\n\r\n@keyframes spinner-chase {\r\n    100% { \r\n        transform: rotate(360deg); \r\n    } \r\n}\r\n\r\n@keyframes chase-dot {\r\n    80%, 100% { \r\n        transform: rotate(360deg); \r\n    } \r\n}\r\n\r\n@keyframes chase-dot-before {\r\n    50% {\r\n        transform: scale(0.4); \r\n    } \r\n    100%, 0% {\r\n        transform: scale(1.0); \r\n    } \r\n}", "//\r\n// Forms.scss\r\n//\r\n\r\n\r\n// checkbox input right\r\n\r\n.form-check-right{\r\n    padding-left: 0;\r\n    display: inline-block;\r\n    padding-right: 1.25rem;\r\n    .form-check-input{\r\n      right: 0;\r\n      margin-left: 0;\r\n    }\r\n  \r\n    .form-check-label{\r\n      display: block;\r\n    }\r\n  }\r\n  \r\n  \r\n  .custom-control-right{\r\n    padding-left: 0;\r\n    padding-right: 1.5rem;  \r\n    display: inline-block;\r\n    .custom-control-label{\r\n      display: inline-block;\r\n      &:before, &:after{\r\n        left: auto;\r\n        right: -1.5rem;\r\n      }\r\n    }\r\n  \r\n    .custom-control-input{\r\n      left: auto;\r\n    }\r\n} \r\n\r\n// checkbox\r\n\r\n.custom-checkbox-outline{\r\n  \r\n  .custom-control-label::before{\r\n    border-width: 2px;\r\n  }\r\n  \r\n  .custom-control-input{\r\n    &:checked~.custom-control-label{\r\n      &:after{\r\n        background-image: none;\r\n        content: '\\F012C';\r\n        font-family: \"Material Design Icons\";\r\n        font-size: 20px;\r\n        top: -8px;\r\n        left: -22px;\r\n      }\r\n\r\n      &:before{\r\n        background-color: transparent !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// radio\r\n\r\n.custom-radio-outline{\r\n  \r\n  .custom-control-input{\r\n    &:checked~.custom-control-label{\r\n      &:after{\r\n        background-image: none;\r\n        content: '\\F0765';\r\n        font-family: \"Material Design Icons\";\r\n        font-size: 8px;\r\n        top: 4px;\r\n        left: -20px;\r\n      }\r\n\r\n      &:before{\r\n        background-color: transparent !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// checkbox color\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n  .custom-checkbox-#{$color}, .custom-radio-#{$color}{\r\n    .custom-control-input{\r\n      &:checked~.custom-control-label:before{\r\n        background-color: $value;\r\n        border-color: $value;\r\n      }\r\n    }\r\n  }\r\n\r\n  .custom-radio-outline{\r\n    &.custom-radio-#{$color}{\r\n      .custom-control-input{\r\n        &:checked~.custom-control-label:after{\r\n          color: $value;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n}\r\n\r\n.custom-checkbox-dark, .custom-radio-dark{\r\n  .custom-control-input{\r\n    &:checked~.custom-control-label:before{\r\n      color: $dark;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n.custom-control-label{\r\n  cursor: pointer;\r\n}\r\n\r\n// Switch sizes\r\n\r\n.custom-switch-md{\r\n  padding-left: 3rem;\r\n  .custom-control-label{\r\n    line-height: 20px;\r\n    &:before{\r\n      width: 40px;\r\n      height: 20px;\r\n      border-radius: 30px;\r\n      left: -3rem;\r\n    }\r\n    &:after{\r\n      width: calc(20px - 4px);\r\n      height: calc(20px - 4px);\r\n      left: calc(-3rem + 2px);\r\n    }\r\n  }\r\n  .custom-control-input:checked~.custom-control-label::after{\r\n    transform: translateX(1.25rem);\r\n  }\r\n}\r\n\r\n\r\n.custom-switch-lg{\r\n  padding-left: 3.75rem;\r\n  .custom-control-label{\r\n    line-height: 24px;\r\n    &:before{\r\n      width: 48px;\r\n      height: 24px;\r\n      border-radius: 30px;\r\n      left: -3.75rem;\r\n    }\r\n    &:after{\r\n      width: calc(24px - 4px);\r\n      height: calc(24px - 4px);\r\n      left: calc(-3.75rem + 2px);\r\n      border-radius: 50%;\r\n    }\r\n  }\r\n  .custom-control-input:checked~.custom-control-label::after{\r\n    transform: translateX(1.5rem);\r\n  }\r\n}\r\n\r\n\r\n.custom-control-label::before{\r\n  background-color: $input-bg;\r\n}\r\n", "// \r\n// Widgets.scss\r\n// \r\n\r\n.mini-stats-wid{\r\n    .mini-stat-icon{\r\n        overflow: hidden;\r\n        position: relative;\r\n        &:before, &:after{\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 8px;\r\n            height: 54px;\r\n            background-color: rgba($white,.1);\r\n            left: 16px;\r\n            transform: rotate(32deg);\r\n            top: -5px;\r\n            transition: all 0.4s;\r\n        }\r\n\r\n        &::after{\r\n            left: -12px;\r\n            width: 12px;\r\n            transition: all 0.2s;\r\n        }\r\n    }\r\n\r\n    &:hover{\r\n        .mini-stat-icon{\r\n            &::after{\r\n                left: 60px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n", "// \r\n// _demos.scss\r\n// \r\n\r\n// Demo Only\r\n.button-items {\r\n    margin-left: -8px;\r\n    margin-bottom: -12px;\r\n    \r\n    .btn {\r\n        margin-bottom: 12px;\r\n        margin-left: 8px;\r\n    }\r\n}\r\n\r\n// Lightbox \r\n\r\n.mfp-popup-form {\r\n    max-width: 1140px;\r\n}\r\n\r\n// Modals\r\n\r\n.bs-example-modal {\r\n    position: relative;\r\n    top: auto;\r\n    right: auto;\r\n    bottom: auto;\r\n    left: auto;\r\n    z-index: 1;\r\n    display: block;\r\n  }\r\n\r\n\r\n// Icon demo ( Demo only )\r\n.icon-demo-content {\r\n    text-align: center;\r\n    color: $gray-500;\r\n  \r\n    i{\r\n      display: block;\r\n      font-size: 24px;\r\n      margin-bottom: 16px;\r\n      color: $gray-600;\r\n      transition: all 0.4s;\r\n    }\r\n  \r\n    .col-lg-4 {\r\n      margin-top: 24px;\r\n  \r\n      &:hover {\r\n        i {\r\n          color: $primary;\r\n          transform: scale(1.5);\r\n        }\r\n      }\r\n    }\r\n}\r\n\r\n\r\n// Grid\r\n\r\n.grid-structure {\r\n    .grid-container {\r\n        background-color: $gray-100;\r\n        margin-top: 10px;\r\n        font-size: .8rem;\r\n        font-weight: $font-weight-medium;\r\n        padding: 10px 20px;\r\n    }\r\n}\r\n\r\n\r\n// card radio\r\n\r\n.card-radio{\r\n  background-color: $card-bg;\r\n  border: 2px solid $card-border-color;\r\n  border-radius: $border-radius;\r\n  padding: 1rem;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n\r\n  &:hover{\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.card-radio-label{\r\n  display: block;\r\n}\r\n\r\n\r\n.card-radio-input{\r\n  display: none;\r\n  &:checked + .card-radio {\r\n    border-color: $primary !important;\r\n  }\r\n}\r\n\r\n.navs-carousel{\r\n  .owl-nav{\r\n      margin-top: 16px;\r\n      button{\r\n          width: 30px;\r\n          height: 30px;\r\n          line-height: 28px !important;\r\n          font-size: 20px !important;\r\n          border-radius: 50% !important;\r\n          background-color: rgba($primary, 0.25) !important;\r\n          color: $primary !important;\r\n          margin: 4px 8px !important;\r\n      }\r\n  }\r\n}", "// \r\n// print.scss\r\n//\r\n\r\n// Used invoice page\r\n@media print {\r\n    .vertical-menu,\r\n    .right-bar,\r\n    .page-title-box,\r\n    .navbar-header,\r\n    .footer {\r\n        display: none !important;\r\n    }\r\n    .card-body,\r\n    .main-content,\r\n    .right-bar,\r\n    .page-content,\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    .card{\r\n        border: 0;\r\n    }\r\n}", "[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 4px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}", "\r\n\r\n/* ==============\r\n  Calendar\r\n===================*/\r\n\r\n.lnb-calendars-item{\r\n  display: inline-block;\r\n  margin-right: 7px;\r\n}\r\n\r\n.tui-full-calendar-layout, .tui-full-calendar-timegrid-timezone{\r\n  background-color: $card-bg !important;\r\n}\r\n\r\n.tui-full-calendar-dayname-container, \r\n.tui-full-calendar-left, \r\n.tui-full-calendar-splitter,\r\n.tui-full-calendar-time-date,\r\n.tui-full-calendar-weekday-grid-line,\r\n.tui-full-calendar-timegrid-timezone,\r\n.tui-full-calendar-timegrid-gridline{\r\n  border-color: $gray-300 !important;\r\n}\r\n\r\n.tui-full-calendar-weekday-exceed-in-week{\r\n  text-align: center;\r\n  width: 30px;\r\n  height: 30px;\r\n  line-height: 28px;\r\n  border-radius: 4px;\r\n  background-color: $card-bg;\r\n  color: $body-color;\r\n  border-color: $border-color;\r\n}\r\n\r\n.tui-full-calendar-timegrid-hour{\r\n  color: $body-color !important;\r\n}\r\n\r\n.tui-full-calendar-weekday-schedule-title, .tui-full-calendar-time-schedule{\r\n  font-weight: $font-weight-semibold;\r\n}\r\n", "\r\n\r\n/* ==============\r\n  Druafula\r\n===================*/\r\n\r\n\r\n.task-box{\r\n  border: 1px solid $gray-300;\r\n}\r\n\r\n.gu-transit {\r\n    border: 1px dashed $gray-600 !important;\r\n    background-color: $gray-200 !important;\r\n}", "//\r\n// session-timeout.scss\r\n//\r\n\r\n#session-timeout-dialog {\r\n    .close {\r\n        display: none;\r\n    }\r\n\r\n    .countdown-holder {\r\n        color: $danger;\r\n        font-weight: $font-weight-medium;\r\n    }\r\n\r\n    .btn-default {\r\n        background-color: $white;\r\n        color: $danger;\r\n        box-shadow: none;\r\n    }\r\n}", "\r\n//\r\n// Range slider\r\n//\r\n\r\n.irs--square{\r\n  .irs-bar, .irs-to, .irs-from, .irs-single {\r\n    background: $primary !important;\r\n    font-size: 11px;\r\n  }\r\n  .irs-to, .irs-from, .irs-single{\r\n    &:before{\r\n      border-top-color: $primary;\r\n    }\r\n  }\r\n\r\n  .irs-line{\r\n    background: $gray-300;\r\n    border-color: $gray-300;\r\n  }\r\n  .irs-grid-text{\r\n    font-size: 11px;\r\n    color: $gray-400;\r\n  }\r\n  .irs-min, .irs-max{\r\n    color: $gray-400;\r\n    background: $gray-300;\r\n    font-size: 11px;\r\n  }\r\n\r\n  .irs-handle{\r\n    border: 2px solid $primary;\r\n    width: 12px;\r\n    height: 12px;\r\n    top: 26px;\r\n    background-color: $card-bg !important;\r\n  }\r\n}", "\r\n//\r\n//  Sweetalert2\r\n//\r\n\r\n.swal2-container {\r\n  .swal2-title{\r\n    font-size: 20px;\r\n    font-weight: $font-weight-medium;\r\n  }  \r\n}\r\n\r\n.swal2-modal{\r\n  font-size: 14px;\r\n}\r\n\r\n.swal2-icon{\r\n  &.swal2-question{\r\n    border-color: $info;\r\n    color: $info;\r\n  }\r\n  &.swal2-success {\r\n    [class^=swal2-success-line]{\r\n      background-color: $success;\r\n    }\r\n\r\n    .swal2-success-ring{\r\n      border-color: rgba($success, 0.3);\r\n    }\r\n  }\r\n  &.swal2-warning{\r\n    border-color: $warning;\r\n    color: $warning;\r\n  }\r\n}\r\n\r\n.swal2-styled{\r\n  &:focus{\r\n    box-shadow: none;\r\n  }\r\n}\r\n\r\n.swal2-progress-steps {\r\n  .swal2-progress-step{\r\n    background: $primary;\r\n    &.swal2-active-progress-step{\r\n      background: $primary;\r\n      &~.swal2-progress-step, &~.swal2-progress-step-line{\r\n        background: rgba($primary, 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  .swal2-progress-step-line{\r\n    background: $primary;\r\n  }\r\n}\r\n\r\n.swal2-loader{\r\n  border-color: $primary transparent $primary transparent;\r\n}\r\n", "\r\n//\r\n// Rating\r\n//\r\n\r\n.symbol{\r\n  border-color: $card-bg;\r\n}\r\n\r\n.rating-symbol-background, .rating-symbol-foreground {\r\n  font-size: 24px;\r\n}\r\n\r\n.rating-symbol-foreground {\r\n  top: 0px;\r\n}", "\r\n//\r\n// toastr.scss\r\n//\r\n\r\n\r\n/* =============\r\n   Notification\r\n============= */\r\n#toast-container {\r\n    > div {\r\n        box-shadow: $box-shadow;\r\n        opacity: 1;\r\n        &:hover {\r\n            box-shadow: $box-shadow;\r\n            opacity: 0.9;\r\n        }\r\n    }\r\n\r\n    &.toast-top-full-width, &.toast-bottom-full-width{\r\n        > div{\r\n          min-width: 96%;\r\n          margin: 4px auto;\r\n        }\r\n      }\r\n\r\n}\r\n\r\n\r\n@each $color, $value in $theme-colors {\r\n    .toast-#{$color} {\r\n        border: 2px solid $value !important;\r\n        background-color: rgba(($value), 0.8) !important;\r\n    }\r\n}\r\n\r\n\r\n// for error\r\n\r\n.toast-error {\r\n    background-color: rgba($danger,0.8);\r\n    border: 2px solid $danger;\r\n}\r\n\r\n.toastr-options{\r\n    padding: 24px;\r\n    background-color: lighten($gray-200, 2%);\r\n    margin-bottom: 0;\r\n    border: 1px solid $border-color;\r\n}", "\r\n\r\n/* ==============\r\n  Cropperjs\r\n===================*/\r\n\r\n.image-crop-preview {\r\n  .img-preview {\r\n      float: left;\r\n      margin-bottom: .5rem;\r\n      margin-right: .5rem;\r\n      overflow: hidden;\r\n      background-color: $gray-300;\r\n      text-align: center;\r\n      width: 100%;\r\n  \r\n      >img {\r\n          max-width: 100%;\r\n      }\r\n  }\r\n\r\n  .preview-lg {\r\n      height: 9rem;\r\n      width: 16rem;\r\n  }\r\n  .preview-md {\r\n      height: 4.5rem;\r\n      width: 8rem;\r\n  }\r\n  .preview-sm {\r\n      height: 2.25rem;\r\n      width: 4rem;\r\n  }\r\n  .preview-xs {\r\n      height: 1.125rem;\r\n      margin-right: 0;\r\n      width: 2rem;\r\n  }\r\n}\r\n\r\n.img-crop-preview-btns, .img-crop-preview-toggles{\r\n  .btn{\r\n    .docs-tooltip{\r\n      display: block;\r\n      margin: -0.47rem -0.75rem;\r\n      padding: 0.47rem 0.75rem;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Parsley\r\n//\r\n\r\n.error {\r\n  color: $danger;\r\n}\r\n\r\n.parsley-error {\r\n  border-color: $danger;\r\n}\r\n\r\n.parsley-errors-list {\r\n  display: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  &.filled {\r\n    display: block;\r\n  }\r\n  > li {\r\n    font-size: 12px;\r\n    list-style: none;\r\n    color: $danger;\r\n    margin-top: 5px;\r\n  }\r\n}", "\r\n//\r\n// Select 2\r\n//\r\n\r\n.select2-container {\r\n\r\n  .select2-selection--single {\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color;\r\n    height: 38px;\r\n    &:focus{\r\n      outline: none;\r\n    }\r\n\r\n    .select2-selection__rendered {\r\n      line-height: 36px;\r\n      padding-left: 12px;\r\n      color: $input-color;\r\n    }\r\n\r\n    .select2-selection__arrow {\r\n      height: 34px;\r\n      width: 34px;\r\n      right: 3px;\r\n\r\n      b{\r\n        border-color: $gray-500 transparent transparent transparent;\r\n        border-width: 6px 6px 0 6px;\r\n      }\r\n    }\r\n\r\n    .select2-selection__placeholder{\r\n      color: $body-color;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--open {\r\n  .select2-selection--single {\r\n\r\n    .select2-selection__arrow {\r\n\r\n      b{\r\n        border-color: transparent transparent $gray-500 transparent !important;\r\n        border-width: 0 6px 6px 6px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default {\r\n  .select2-search--dropdown {\r\n      padding: 10px;\r\n      background-color: $dropdown-bg;\r\n      .select2-search__field {\r\n          border: 1px solid  $input-border-color;\r\n          background-color: $input-bg;\r\n          color: $gray-600;\r\n          outline: none;\r\n      }\r\n  }\r\n  .select2-results__option--highlighted[aria-selected] {\r\n      background-color: $primary;\r\n  }\r\n  .select2-results__option[aria-selected=true] {\r\n      background-color: $dropdown-link-active-bg;\r\n      color: $dropdown-link-active-color;\r\n      &:hover {\r\n          background-color: $primary;\r\n          color: $white;\r\n      }\r\n  }\r\n}\r\n\r\n.select2-results__option {\r\n  padding: 6px 12px;\r\n}\r\n\r\n.select2-dropdown {\r\n  border: 1px solid $dropdown-border-color;\r\n  background-color: $dropdown-bg;\r\n  box-shadow: $box-shadow;\r\n}\r\n\r\n.select2-search {\r\n  input{\r\n    border: 1px solid $gray-300;\r\n  }\r\n}\r\n\r\n.select2-container {\r\n  .select2-selection--multiple {\r\n    min-height: 38px;\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color !important;\r\n  \r\n    .select2-selection__rendered {\r\n      padding: 2px 10px;\r\n    }\r\n    .select2-search__field {\r\n      border: 0;\r\n      color: $input-color;\r\n      &::placeholder{\r\n          color: $input-color;\r\n      }\r\n  }\r\n    .select2-selection__choice {\r\n      background-color: $gray-200;\r\n      border: 1px solid $gray-300;\r\n      border-radius: 1px;\r\n      padding: 0 7px;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default{\r\n  &.select2-container--focus {\r\n    .select2-selection--multiple{\r\n      border-color: $gray-400;\r\n    }\r\n  }\r\n\r\n  .select2-results__group{\r\n    font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n// ajax select\r\n\r\n.select2-result-repository__avatar{\r\n    float: left;\r\n    width: 60px;\r\n    margin-right: 10px;\r\n  img{\r\n    width: 100%;\r\n    height: auto;\r\n    border-radius: 2px;\r\n  }\r\n}\r\n\r\n.select2-result-repository__statistics{\r\n  margin-top: 7px;\r\n}\r\n\r\n.select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  display: inline-block;\r\n  font-size: 11px;\r\n  margin-right: 1em;\r\n  color: $gray-500;\r\n\r\n  .fa{\r\n    margin-right: 4px;\r\n\r\n    &.fa-flash{\r\n      &::before{\r\n        content: \"\\f0e7\";\r\n        font-family: 'Font Awesome 5 Free';\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-results__option--highlighted{\r\n  .select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  color: rgba($white, 0.8);\r\n}\r\n}\r\n\r\n.select2-result-repository__meta{\r\n  overflow: hidden;\r\n}\r\n\r\n\r\n// templating-select\r\n\r\n.img-flag{\r\n  margin-right: 7px;\r\n  height: 15px;\r\n  width: 18px;\r\n}\r\n\r\n\r\n", "//\r\n//  Sweetalert2\r\n//\r\n\r\n/* CSS Switch */\r\ninput[switch] {\r\n  display: none;\r\n  + label {\r\n    font-size: 1em;\r\n    line-height: 1;\r\n    width: 56px;\r\n    height: 24px;\r\n    background-color: $gray-400;\r\n    background-image: none;\r\n    border-radius: 2rem;\r\n    padding: 0.16667rem;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    text-align: center;\r\n    position: relative;\r\n    font-weight: $font-weight-medium;\r\n    transition: all 0.1s ease-in-out;\r\n    &:before {\r\n      color: $dark;\r\n      content: attr(data-off-label);\r\n      display: block;\r\n      font-family: inherit;\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n      line-height: 21px;\r\n      position: absolute;\r\n      right: 1px;\r\n      margin: 3px;\r\n      top: -2px;\r\n      text-align: center;\r\n      min-width: 1.66667rem;\r\n      overflow: hidden;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n\r\n    &:after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 3px;\r\n      background-color: $gray-200;\r\n      box-shadow: none;\r\n      border-radius: 2rem;\r\n      height: 20px;\r\n      width: 20px;\r\n      top: 2px;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n  }\r\n\r\n  &:checked + label {\r\n    background-color: $primary;\r\n  }\r\n}\r\n\r\ninput[switch]:checked + label {\r\n  background-color: $primary;\r\n  &:before {\r\n    color: $white;\r\n    content: attr(data-on-label);\r\n    right: auto;\r\n    left: 3px;\r\n  }\r\n\r\n  &:after {\r\n    left: 33px;\r\n    background-color: $gray-200;\r\n  }\r\n}\r\n\r\ninput[switch=\"bool\"] + label {\r\n  background-color: $danger;\r\n}\r\ninput[switch=\"bool\"] + label:before,input[switch=\"bool\"]:checked + label:before,\r\ninput[switch=\"default\"]:checked + label:before{\r\n  color: $white;\r\n}\r\n\r\ninput[switch=\"bool\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"default\"]:checked + label {\r\n  background-color: #a2a2a2;\r\n}\r\n\r\ninput[switch=\"primary\"]:checked + label {\r\n  background-color: $primary;\r\n}\r\n\r\ninput[switch=\"success\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"info\"]:checked + label {\r\n  background-color: $info;\r\n}\r\n\r\ninput[switch=\"warning\"]:checked + label {\r\n  background-color: $warning;\r\n}\r\n\r\ninput[switch=\"danger\"]:checked + label {\r\n  background-color: $danger;\r\n}\r\n\r\ninput[switch=\"dark\"]:checked + label {\r\n  background-color: $dark;\r\n}\r\n\r\n.square-switch{\r\n  margin-right: 7px;\r\n  input[switch]+label, input[switch]+label:after{\r\n    border-radius: 4px;\r\n  }\r\n}\r\n", "\r\n\r\n/* Timepicker */\r\n.bootstrap-timepicker-widget {\r\n  table {\r\n    td {\r\n      a {\r\n        color: $input-color;\r\n        &:hover {\r\n          background-color: transparent;\r\n          border-color: transparent;\r\n          border-radius: 4px;\r\n          color: $primary;\r\n          text-decoration: none;\r\n        }\r\n      }\r\n      input {\r\n        width: 32px;\r\n        height: 32px;\r\n        border: 0;\r\n        color: $body-color;\r\n        border: 1px solid $border-color;;\r\n        background-color: $input-bg;\r\n\r\n      }\r\n    }\r\n  }\r\n\r\n  &.dropdown-menu:after{\r\n    border-bottom-color: $gray-200;\r\n  }\r\n  &.timepicker-orient-bottom:after{\r\n    border-top-color: $gray-200;\r\n  }\r\n}", "\r\n//\r\n// Datepicker\r\n//\r\n\r\n.datepicker {\r\n  border: 1px solid $border-color;\r\n  padding: 8px;\r\n  z-index: 999 !important;\r\n  table{\r\n    tr{\r\n      th{\r\n        font-weight: 500;\r\n      }\r\n      td{\r\n        &.active, &.active:hover, .active.disabled, &.active.disabled:hover,\r\n        &.today,  &.today:hover, &.today.disabled, &.today.disabled:hover, \r\n        &.selected, &.selected:hover, &.selected.disabled, &.selected.disabled:hover{\r\n          background-color: $primary !important;\r\n          background-image: none;\r\n          box-shadow: none;\r\n          color: $white !important;\r\n        }\r\n\r\n        &.day.focused,\r\n        &.day:hover,\r\n        span.focused,\r\n        span:hover {\r\n            background: $gray-200;\r\n        }\r\n\r\n        &.new,\r\n        &.old,\r\n        span.new,\r\n        span.old {\r\n            color: $gray-500;\r\n            opacity: 0.6;\r\n        }\r\n\r\n        &.range, &.range.disabled, &.range.disabled:hover, &.range:hover{\r\n            background-color: $gray-300;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-condensed{\r\n  >thead>tr>th, >tbody>tr>td {\r\n    padding: 7px;\r\n  }\r\n}\r\n\r\n.bootstrap-datepicker-inline{\r\n  .datepicker-inline{\r\n    width: auto !important;\r\n    display: inline-block;\r\n  }\r\n}\r\n\r\n\r\n\r\n// DATEPICKER\r\n\r\n.datepicker-container{\r\n  border: 1px solid $border-color;\r\n  box-shadow: none;\r\n  background-color: $dropdown-bg;\r\n\r\n  &.datepicker-inline{\r\n    width: 212px;\r\n  }\r\n}\r\n\r\n.datepicker-panel{\r\n  \r\n  >ul{\r\n    >li{\r\n      background-color: $dropdown-bg;\r\n      border-radius: 4px;\r\n\r\n      &.picked, &.picked:hover{\r\n        background-color: rgba($primary, 0.25);\r\n        color: $primary;\r\n      }\r\n\r\n      &.highlighted, &.highlighted:hover, &:hover{\r\n        background-color: $primary;\r\n        color: $white;\r\n      }\r\n\r\n      \r\n      &.muted, &.muted:hover{\r\n        color: $gray-500;\r\n        opacity: 0.6;\r\n      }\r\n    }\r\n\r\n    \r\n\r\n    &[data-view=week]{\r\n      >li{\r\n        font-weight: $font-weight-medium;\r\n      }\r\n\r\n      >li, >li:hover{\r\n        background-color: $dropdown-bg;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "//\r\n// Form editors.scss\r\n//\r\n\r\n// Tinymce \r\n\r\n.tox-tinymce {\r\n  border: 1px solid $input-border-color !important;\r\n}\r\n\r\n.tox {\r\n  .tox-statusbar {\r\n    border-top: 1px solid $light !important;\r\n  }\r\n    .tox-menubar, .tox-edit-area__iframe, .tox-statusbar{\r\n      background-color: $card-bg !important;\r\n      background: none !important;\r\n    }\r\n    .tox-mbtn{\r\n      color: $gray-700 !important;\r\n        &:hover:not(:disabled):not(.tox-mbtn--active){\r\n            background-color: $light !important;\r\n        }\r\n    }\r\n    .tox-tbtn{\r\n        &:hover{\r\n            background-color: $light !important;\r\n        }\r\n    }\r\n\r\n    .tox-toolbar, .tox-toolbar__overflow, .tox-toolbar__primary{\r\n      background: $light !important;\r\n    }\r\n\r\n    .tox-toolbar__primary{\r\n      border-top-color: $light !important;\r\n    }\r\n\r\n    .tox-tbtn{\r\n      color: $gray-700 !important;\r\n      svg{\r\n        fill: $gray-700 !important;\r\n      }\r\n    }\r\n\r\n    .tox-edit-area__iframe{\r\n      background-color: $card-bg !important;\r\n    }\r\n  \r\n    .tox-statusbar a, .tox-statusbar__path-item, .tox-statusbar__wordcount{\r\n      color: $gray-700 !important;\r\n    }\r\n\r\n    &:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\r\n      border-right: 1px solid darken($light, 5%) !important;\r\n    } \r\n}\r\n\r\n// Summernote\r\n\r\n.note-editor{\r\n    &.note-frame {\r\n      border: 1px solid $input-border-color;\r\n      box-shadow: none;\r\n      margin: 0;\r\n  \r\n      .note-statusbar {\r\n        background-color: $light;\r\n        border-top: 1px solid $light;\r\n      }\r\n\r\n      .note-editing-area{\r\n        .note-editable, .note-codable {\r\n          border: none;\r\n          color: $gray-500;\r\n          background-color: transparent;\r\n        }\r\n      }\r\n  \r\n    }\r\n  }\r\n\r\n  .note-btn-group{\r\n    .note-btn{\r\n      background-color: $gray-300 !important;\r\n      border-color: $gray-300 !important;\r\n\r\n      &.dropdown-toggle{\r\n        &:after{\r\n          display: inline-block;\r\n          margin-left: .255em;\r\n          vertical-align: .255em;\r\n          content: \"\";\r\n          border-top: .3em solid;\r\n          border-right: .3em solid transparent;\r\n          border-bottom: 0;\r\n          border-left: .3em solid transparent;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .note-btn-group{\r\n    .note-btn{\r\n      background-color: $light !important;\r\n      border-color: $light !important;\r\n    }\r\n  }\r\n  \r\n  .note-status-output {\r\n    display: none;\r\n  }\r\n  \r\n  .note-editable {\r\n  \r\n    p {\r\n      &:last-of-type {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .note-popover .popover-content .note-color .dropdown-menu,\r\n  .card-header.note-toolbar .note-color .dropdown-menu {\r\n      min-width: 344px;\r\n  }\r\n  \r\n  .note-popover{\r\n    border-color: $light;\r\n  }\r\n  \r\n  .note-popover .popover-content, \r\n  .card-header.note-toolbar{\r\n    background-color: $light;\r\n  }\r\n\r\n  .note-toolbar {\r\n    padding: 0 0 5px 5px !important;\r\n}\r\n", "\r\n//\r\n// Form-Upload\r\n//\r\n\r\n/* Dropzone */\r\n.dropzone {\r\n  min-height: 230px;\r\n  border: 2px dashed $gray-400;\r\n  background: $card-bg;\r\n  border-radius: 6px;\r\n\r\n  .dz-message {\r\n    font-size: 24px;\r\n    width: 100%;\r\n  }\r\n}", "//\r\n// Form Wizard\r\n//\r\n\r\n// twitter-bs-wizard\r\n\r\n.twitter-bs-wizard{\r\n\r\n  .twitter-bs-wizard-nav{\r\n    padding: 4px;\r\n    background-color: rgba($primary, 0.1);\r\n\r\n    .step-number{\r\n      display: inline-block;\r\n      width: 38px;\r\n      height: 38px;\r\n      line-height: 34px;\r\n      border: 2px solid $primary;\r\n      color: $primary;\r\n      text-align: center;\r\n      border-radius: 50%;\r\n\r\n      @media (max-width: 991.98px) {\r\n        display: block;\r\n        margin: 0 auto 8px !important;\r\n      }\r\n    }\r\n\r\n    .nav-link{\r\n      @media (min-width: 992px) {\r\n        text-align: left;\r\n      }\r\n      &.active{\r\n        background-color: transparent;\r\n        color: $gray-700;\r\n\r\n        .step-number{\r\n          background-color: $primary;\r\n          color: $white;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .twitter-bs-wizard-pager-link{\r\n    padding-top: 24px;\r\n      padding-left: 0;\r\n      list-style: none;\r\n      margin-bottom: 0;\r\n      li{\r\n        display: inline-block;\r\n        a{\r\n          display: inline-block;\r\n          padding: .47rem .75rem;\r\n          background-color: $primary;\r\n          color: $white;\r\n          border-radius: .25rem;\r\n        }\r\n        &.disabled{\r\n          a{\r\n            cursor: not-allowed;\r\n            background-color: lighten($primary, 8%);\r\n          }\r\n        }\r\n\r\n        &.next{\r\n          float: right;\r\n        }\r\n      }\r\n    }\r\n}\r\n\r\n.twitter-bs-wizard-tab-content{\r\n  padding-top: 24px;\r\n  min-height: 262px;\r\n}", "\r\n//\r\n// Responsive Table\r\n//\r\n\r\n.table-rep-plugin {\r\n  .btn-toolbar {\r\n    display: block;\r\n  }\r\n  .table-responsive {\r\n    border: none !important;\r\n  }\r\n  .btn-group{\r\n    .btn-default {\r\n      background-color: $secondary;\r\n      color: $light;\r\n      border: 1px solid $secondary;\r\n      &.btn-primary {\r\n          background-color: $primary;\r\n          border-color: $primary;\r\n          color: $white;\r\n          box-shadow: 0 0 0 2px rgba($primary, .5);\r\n      }\r\n  }\r\n    &.pull-right {\r\n      float: right;\r\n      .dropdown-menu {\r\n        right: 0;\r\n        transform: none !important;\r\n        top: 100% !important;\r\n      }\r\n    }\r\n  }\r\n  tbody {\r\n    th {\r\n      font-size: 14px;\r\n      font-weight: normal;\r\n    }\r\n  }\r\n\r\n  .checkbox-row {\r\n    padding-left: 40px;\r\n    color: $dropdown-color !important;\r\n\r\n    &:hover{\r\n      background-color: lighten($gray-200, 2%) !important;\r\n    }\r\n\r\n    label {\r\n      display: inline-block;\r\n      padding-left: 5px;\r\n      position: relative;\r\n      &::before {\r\n        -o-transition: 0.3s ease-in-out;\r\n        -webkit-transition: 0.3s ease-in-out;\r\n        background-color: $white;\r\n        border-radius: 3px;\r\n        border: 1px solid $gray-300;\r\n        content: \"\";\r\n        display: inline-block;\r\n        height: 17px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        position: absolute;\r\n        transition: 0.3s ease-in-out;\r\n        width: 17px;\r\n        outline: none !important;\r\n      }\r\n      &::after {\r\n        color: $gray-200;\r\n        display: inline-block;\r\n        font-size: 11px;\r\n        height: 16px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        padding-left: 3px;\r\n        padding-top: 1px;\r\n        position: absolute;\r\n        top: -1px;\r\n        width: 16px;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"] {\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      z-index: 1;\r\n      outline: none !important;\r\n\r\n      &:disabled + label {\r\n        opacity: 0.65;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:focus + label {\r\n      &::before {\r\n        outline-offset: -2px;\r\n        outline: none;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::after {\r\n        content: \"\\f00c\";\r\n        font-family: 'Font Awesome 5 Free';\r\n        font-weight: 900;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:disabled + label {\r\n      &::before {\r\n        background-color: $gray-100;\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::before {\r\n        background-color: $primary;\r\n        border-color: $primary;\r\n      }\r\n      &::after {\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  .fixed-solution {\r\n    .sticky-table-header{\r\n      top: $header-height !important;\r\n      background-color: $primary;\r\n      table{\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  table.focus-on tbody tr.focused th,\r\n  table.focus-on tbody tr.focused td,\r\n  .sticky-table-header {\r\n      background: $primary;\r\n      border-color: $primary;\r\n      color: $white;\r\n\r\n      table {\r\n          color: $white;\r\n      }\r\n  }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n  @media (min-width: 992px) {\r\n    .fixed-solution {\r\n      .sticky-table-header{\r\n        top: $header-height + 50px !important;;\r\n      }\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Table editable\r\n//\r\n\r\n\r\n.table-editable{\r\n  .editable-input{\r\n    .form-control{\r\n      height: 2rem;\r\n    }\r\n    \r\n  }\r\n  a.editable{\r\n    color: $table-color;\r\n  }\r\n  .editable-buttons{\r\n    .btn {\r\n      &.btn-sm{\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  }\r\n  tbody {\r\n    td{\r\n      &.focus{\r\n        box-shadow: inset 0 0 1px 1px $primary !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.glyphicon {\r\n  display: inline-block;\r\n  font-family: \"Material Design Icons\";\r\n  font-size: inherit;\r\n  font-weight: 600;\r\n  font-style: inherit;\r\n}\r\n\r\n.glyphicon-ok {\r\n  &:before {\r\n      content: \"\\F012C\";\r\n  }\r\n}\r\n\r\n.glyphicon-remove {\r\n  &:before {\r\n      content: \"\\F0156\";\r\n  }\r\n}\r\n\r\n.dt-autofill-list{\r\n  border: none !important;\r\n  background-color: $card-bg !important;\r\n  \r\n  .dt-autofill-question, .dt-autofill-button{\r\n    border-bottom-color: $gray-300 !important;\r\n  }\r\n\r\n  ul{\r\n    li{\r\n      &:hover{\r\n        background-color: $gray-300 !important;\r\n      }\r\n    }\r\n  }\r\n}", "\r\n//\r\n// apexcharts.scss\r\n//\r\n.apex-charts {\r\n    min-height: 10px !important;\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n    .apexcharts-canvas {\r\n        margin: 0 auto;\r\n    }\r\n}\r\n\r\n.apexcharts-tooltip-title,\r\n.apexcharts-tooltip-text {\r\n    font-family: $font-family-base !important;\r\n}\r\n\r\n.apexcharts-legend-series {\r\n    font-weight: $font-weight-medium;\r\n}\r\n\r\n.apexcharts-gridline {\r\n    pointer-events: none;\r\n    stroke: $apex-grid-color;\r\n}\r\n\r\n.apexcharts-legend-text {\r\n    color: $gray-600 !important;\r\n    font-family: $font-family-base !important;\r\n    font-size: 13px !important;\r\n}\r\n\r\n.apexcharts-pie-label {\r\n    fill: $white !important;\r\n}\r\n\r\n.apexcharts-yaxis,\r\n.apexcharts-xaxis {\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n}", "\r\n//\r\n// echarts.scss\r\n//\r\n\r\n.e-charts{\r\n    height: 350px;\r\n}", "\r\n\r\n/* Flot chart */\r\n.flot-charts-height {\r\n  height: 320px;\r\n}\r\n\r\n.flotTip {\r\n  padding: 8px 12px;\r\n  background-color: rgba($dark, 0.9);\r\n  z-index: 100;\r\n  color: $gray-100;\r\n  box-shadow: $box-shadow;\r\n  border-radius: 4px;\r\n}\r\n\r\n.legendLabel{\r\n  color: $gray-500;\r\n}", "//\r\n// sparkline.scss\r\n//\r\n\r\n.jqstooltip {\r\n  box-sizing: content-box;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  background-color: $gray-800 !important;\r\n  box-shadow: $box-shadow-lg;\r\n  padding: 5px 10px !important;\r\n  border-radius: 3px;\r\n  border-color: $gray-900 !important;\r\n}\r\n\r\n.jqsfield {\r\n  color: $gray-200 !important;\r\n  font-size: 12px !important;\r\n  line-height: 18px !important;\r\n  font-family: $font-family-base !important;\r\n  font-weight: $font-weight-medium !important;\r\n}\r\n", "\r\n//\r\n// Google map\r\n//\r\n\r\n.gmaps, .gmaps-panaroma {\r\n  height: 300px;\r\n  background: $gray-100;\r\n  border-radius: 3px;\r\n}\r\n\r\n.gmaps-overlay {\r\n  display: block;\r\n  text-align: center;\r\n  color: $white;\r\n  font-size: 16px;\r\n  line-height: 40px;\r\n  background: $primary;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.gmaps-overlay_arrow {\r\n  left: 50%;\r\n  margin-left: -16px;\r\n  width: 0;\r\n  height: 0;\r\n  position: absolute;\r\n  &.above {\r\n    bottom: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-top: 16px solid $primary;\r\n  }\r\n  &.below {\r\n    top: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-bottom: 16px solid $primary;\r\n  }\r\n  \r\n}", "//\r\n// vector-maps.scss\r\n//\r\n\r\n.jvectormap-label {\r\n    border: none;\r\n    background: $gray-800;\r\n    color: $gray-100;\r\n    font-family: $font-family-base;\r\n    font-size: $font-size-base;\r\n    padding: 5px 8px;\r\n}", "//\r\n// leaflet-maps.scss\r\n//\r\n\r\n.leaflet-map {\r\n    height: 300px;\r\n    &.leaflet-container{\r\n        z-index: 99;\r\n    }\r\n}", "// \r\n// authentication.scss\r\n//\r\n\r\n\r\n// authentication home icon\r\n.home-btn {\r\n    position: absolute;\r\n    top: 15px;\r\n    right: 25px;\r\n}\r\n\r\n// auth 2\r\n\r\n.auth-logo{\r\n    .auth-logo-dark{\r\n        display: $display-block;\r\n    }\r\n    .auth-logo-light{\r\n        display: $display-none;\r\n    }\r\n}\r\n\r\n.auth-body-bg{\r\n    background-color: $card-bg;\r\n}\r\n\r\n\r\n// authentication full page\r\n\r\n.auth-full-bg{\r\n    background-color: rgba($primary, 0.25);\r\n    display: flex;\r\n\r\n    @media (min-width: 1200px){\r\n        height: 100vh;\r\n    }\r\n    \r\n\r\n    &::before{\r\n        content: \"\";\r\n        position: absolute;\r\n        width: 300px;\r\n        height: 300px;\r\n        border-radius: 50%;\r\n    }\r\n\r\n    .bg-overlay{\r\n        background: url(\"../images/bg-auth-overlay.png\");\r\n        background-size: cover;\r\n        background-repeat: no-repeat;\r\n        background-position: center;\r\n    }\r\n}\r\n\r\n.auth-full-page-content{\r\n    display: flex;\r\n\r\n    @media (min-width: 1200px){\r\n        min-height: 100vh;\r\n    }\r\n}\r\n\r\n.auth-review-carousel{\r\n    &.owl-theme {\r\n        .owl-dots {\r\n            .owl-dot{\r\n                span{\r\n                    background-color: rgba($primary, 0.25);\r\n                }\r\n                &.active, &:hover{\r\n                    span{\r\n                        background-color: $primary;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// ecommerce.scss\r\n//\r\n\r\n// product\r\n\r\n.search-box{\r\n    .form-control{\r\n        border-radius: 30px;\r\n        padding-left: 40px;\r\n    }\r\n    .search-icon{\r\n        font-size: 16px;    \r\n        position: absolute;\r\n        left: 13px;\r\n        top: 0;\r\n        line-height: 38px;\r\n    }\r\n}\r\n\r\n.product-list{\r\n    li{\r\n        a{\r\n            display: block;\r\n            padding: 4px 0px;\r\n            color: $body-color;\r\n        }\r\n    }\r\n}\r\n\r\n.product-view-nav{\r\n    &.nav-pills {\r\n        .nav-item{\r\n            margin-left: 4px;\r\n        }\r\n        .nav-link{\r\n            width: 36px;\r\n            height: 36px;\r\n            font-size: 16px;\r\n            padding: 0;\r\n            line-height: 36px;\r\n            text-align: center;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n.product-ribbon{\r\n    position: absolute;\r\n    right: 0px;\r\n    top: 0px;\r\n}\r\n\r\n// Product Details\r\n\r\n.product-detai-imgs{\r\n    .nav{\r\n        .nav-link{\r\n            margin: 7px 0px;\r\n\r\n            &.active{\r\n                background-color: $gray-300;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-color{\r\n    a{\r\n        display: inline-block;\r\n        text-align: center;\r\n        color: $body-color;\r\n        .product-color-item{\r\n            margin: 7px;\r\n        }\r\n        &.active, &:hover{\r\n            color: $primary;\r\n            .product-color-item{\r\n                border-color: $primary !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// ecommerce cart\r\n\r\n.visa-card{\r\n    .visa-logo{\r\n        line-height: 0.5;\r\n    }\r\n\r\n    .visa-pattern{\r\n        position: absolute;\r\n        font-size: 385px;\r\n        color: rgba($white, 0.05);\r\n        line-height: 0.4;\r\n        right: 0px;\r\n        bottom: 0px;\r\n    }\r\n}\r\n\r\n\r\n// checkout\r\n\r\n.checkout-tabs{\r\n    .nav-pills{\r\n        .nav-link{\r\n            margin-bottom: 24px;\r\n            text-align: center;\r\n            background-color: $card-bg;\r\n            box-shadow: $box-shadow;\r\n\r\n            &.active{\r\n                background-color: $primary;\r\n            }\r\n            .check-nav-icon{\r\n                font-size: 36px;\r\n            }\r\n        }\r\n    }\r\n}", "/* ==============\r\n  Email\r\n===================*/\r\n.email-leftbar {\r\n  width: 236px;\r\n  float: left;\r\n  padding: 20px;\r\n  border-radius: 5px;\r\n}\r\n\r\n.email-rightbar {\r\n  margin-left: 260px;\r\n}\r\n\r\n.chat-user-box {\r\n  p.user-title {\r\n    color: $dark;\r\n    font-weight: 500;\r\n  }\r\n  p {\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n  .email-leftbar {\r\n    float: none;\r\n    width: 100%;\r\n  }\r\n  .email-rightbar {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n\r\n.mail-list {\r\n  a {\r\n    display: block;\r\n    color: $gray-600;\r\n    line-height: 24px;\r\n    padding: 8px 5px;\r\n    &.active {\r\n      color: $danger;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.message-list {\r\n  display: block;\r\n  padding-left: 0;\r\n\r\n  li {\r\n    position: relative;\r\n    display: block;\r\n    height: 50px;\r\n    line-height: 50px;\r\n    cursor: default;\r\n    transition-duration: .3s;\r\n\r\n    a{\r\n      color: $gray-600;\r\n    }\r\n\r\n    &:hover {\r\n      background: $gray-300;\r\n      transition-duration: .05s;\r\n    }\r\n\r\n    .col-mail {\r\n      float: left;\r\n      position: relative;\r\n    }\r\n\r\n    .col-mail-1 {\r\n      width: 320px;\r\n\r\n      .star-toggle,\r\n      .checkbox-wrapper-mail,\r\n      .dot {\r\n        display: block;\r\n        float: left;\r\n      }\r\n\r\n      .dot {\r\n        border: 4px solid transparent;\r\n        border-radius: 100px;\r\n        margin: 22px 26px 0;\r\n        height: 0;\r\n        width: 0;\r\n        line-height: 0;\r\n        font-size: 0;\r\n      }\r\n\r\n      .checkbox-wrapper-mail {\r\n        margin: 15px 10px 0 20px;\r\n      }\r\n\r\n      .star-toggle {\r\n        margin-top: 18px;\r\n        margin-left: 5px;\r\n      }\r\n\r\n      .title {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 110px;\r\n        right: 0;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .col-mail-2 {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 320px;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .subject,\r\n      .date {\r\n        position: absolute;\r\n        top: 0;\r\n      }\r\n\r\n      .subject {\r\n        left: 0;\r\n        right: 200px;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .date {\r\n        right: 0;\r\n        width: 170px;\r\n        padding-left: 80px;\r\n      }\r\n    }\r\n\r\n    &.active,\r\n    &.active:hover {\r\n      box-shadow: inset 3px 0 0 $primary;\r\n    }\r\n\r\n    \r\n  &.unread  {\r\n    background-color: $gray-300;\r\n    font-weight: 500;\r\n    color: darken($dark,5%);\r\n      a{\r\n        color: darken($dark,5%);\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .checkbox-wrapper-mail {\r\n    cursor: pointer;\r\n    height: 20px;\r\n    width: 20px;\r\n    position: relative;\r\n    display: inline-block;\r\n    box-shadow: inset 0 0 0 1px $gray-400;\r\n    border-radius: 1px;\r\n\r\n    input {\r\n      opacity: 0;\r\n      cursor: pointer;\r\n    }\r\n    input:checked ~ label {\r\n      opacity: 1;\r\n    }\r\n\r\n    label {\r\n      position: absolute;\r\n      height: 20px;\r\n      width: 20px;\r\n      left: 0;\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      margin-bottom: 0;\r\n      transition-duration: .05s;\r\n      top: 0;\r\n      &:before {\r\n        content: \"\\F012C\";\r\n        font-family: \"Material Design Icons\";\r\n        top: 0;\r\n        height: 20px;\r\n        color: darken($dark,5%);\r\n        width: 20px;\r\n        position: absolute;\r\n        margin-top: -16px;\r\n        left: 4px;\r\n        font-size: 13px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 575.98px) { \r\n  .message-list li .col-mail-1 {\r\n      width: 200px;\r\n  }\r\n}", "// \r\n// File manager.scss\r\n//\r\n\r\n// file manager\r\n\r\n.filemanager-sidebar{\r\n    \r\n    @media (min-width: 1200px){\r\n        min-width: 230px;\r\n        max-width: 230px;\r\n    }\r\n\r\n    @media (min-width: 1366px){\r\n        min-width: 280px;\r\n        max-width: 280px;\r\n    }\r\n\r\n}\r\n\r\n.categories-list{\r\n    padding: 4px 0;\r\n    li{\r\n        a{\r\n            display: block;\r\n            padding: 8px 12px;\r\n            color: $body-color;\r\n            font-weight: $font-weight-medium;\r\n        }\r\n\r\n        &.active{\r\n            a{\r\n                color: $primary;\r\n            }\r\n        }\r\n\r\n        ul{\r\n            padding-left: 16px;\r\n            li{\r\n                a{\r\n                    padding: 4px 12px;\r\n                    color: $text-muted;\r\n                    font-size: 13px;\r\n                    font-weight: $font-weight-normal;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n", "// \r\n// Chat.scss\r\n//\r\n\r\n.chat-leftsidebar{\r\n  @media (min-width: 992px) {\r\n    min-width: 380px;\r\n  }\r\n\r\n\r\n  .chat-leftsidebar-nav{\r\n    .nav{\r\n      background-color: $card-bg;\r\n    }\r\n\r\n    .tab-content{\r\n      min-height: 488px;\r\n    }\r\n  }\r\n}\r\n\r\n.chat-noti-dropdown{\r\n  &.active{\r\n    &:before{\r\n      content: \"\";\r\n      position: absolute;\r\n      width: 8px;\r\n      height: 8px;\r\n      background-color: $danger;\r\n      border-radius: 50%;\r\n      right: 0;\r\n    }\r\n  }\r\n\r\n  .btn{\r\n    padding: 6px;\r\n    box-shadow: none;\r\n    font-size: 20px;\r\n  }\r\n}\r\n\r\n.chat-search-box{\r\n  .form-control{\r\n    border: 0;\r\n  }\r\n}\r\n\r\n.chat-list{\r\n  margin: 0;\r\n  li{\r\n    &.active{\r\n      a{\r\n        background-color: $card-bg;\r\n        border-color: transparent;\r\n        box-shadow: $box-shadow;\r\n      }\r\n    }\r\n    a{\r\n      display: block;\r\n      padding: 14px 16px;\r\n      color: $gray-600;\r\n      transition: all 0.4s;\r\n      border-top: 1px solid $border-color;\r\n      border-radius: 4px;\r\n      &:hover{\r\n        background-color: $card-bg;\r\n        border-color: transparent;\r\n        box-shadow: $box-shadow;\r\n\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n.user-chat-nav{\r\n  .dropdown{\r\n    .nav-btn{\r\n      height: 40px;\r\n      width: 40px;\r\n      line-height: 40px;\r\n      box-shadow: none;\r\n      padding: 0;\r\n      font-size: 16px;\r\n      background-color: $light;\r\n      border-radius: 50%;\r\n    }\r\n\r\n    .dropdown-menu{\r\n      box-shadow: $box-shadow;\r\n      border: 1px solid $border-color\r\n    }\r\n  }\r\n}\r\n\r\n\r\n.chat-conversation{\r\n  li{\r\n    clear: both;\r\n  }\r\n\r\n  .chat-day-title{\r\n    position: relative;\r\n    text-align: center;\r\n    margin-bottom: 24px;\r\n\r\n    .title{\r\n      background-color: $card-bg;\r\n      position: relative;\r\n      z-index: 1;\r\n      padding: 6px 24px;\r\n    }\r\n\r\n    &:before{\r\n      content: \"\";\r\n      position: absolute;\r\n      width: 100%;\r\n      height: 1px;\r\n      left: 0;\r\n      right: 0;\r\n      background-color: $border-color;\r\n      top: 10px;\r\n    }\r\n    .badge{\r\n      font-size: 12px;\r\n    }\r\n  }\r\n  .conversation-list{\r\n    margin-bottom: 24px;\r\n    display: inline-block;\r\n    position: relative;\r\n\r\n    .ctext-wrap{\r\n      padding: 12px 24px;\r\n      background-color: rgba($primary,0.1);\r\n      border-radius: 8px 8px 8px 0px;\r\n      overflow: hidden;\r\n\r\n      .conversation-name{\r\n        font-weight: $font-weight-semibold;\r\n        color: $primary;\r\n        margin-bottom: 4px;\r\n      }\r\n    }\r\n\r\n    .dropdown{\r\n      float: right;\r\n      .dropdown-toggle{\r\n        font-size: 18px;\r\n        padding: 4px;\r\n        color: $gray-600;\r\n        @media (max-width: 575.98px) {\r\n          display: none;\r\n        }\r\n      }\r\n\r\n      .dropdown-menu{\r\n        box-shadow: $box-shadow;\r\n        border: 1px solid $border-color\r\n      }\r\n    }\r\n\r\n    .chat-time{\r\n      font-size: 12px;\r\n    }\r\n  }\r\n\r\n  .right{\r\n    .conversation-list{\r\n      float: right;\r\n      .ctext-wrap{\r\n        background-color: $light;\r\n        text-align: right;\r\n        border-radius: 8px 8px 0px 8px;\r\n      }\r\n      .dropdown{\r\n        float: left;\r\n      }\r\n\r\n      &.last-chat{\r\n        .conversation-list{\r\n          &:before{\r\n            right: 0;\r\n            left: auto;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n  }\r\n\r\n  .last-chat{\r\n    .conversation-list{\r\n      &:before{\r\n        content: \"\\F0009\";\r\n        font-family: \"Material Design Icons\";\r\n        position: absolute;\r\n        color: $primary;\r\n        right: 0;\r\n        bottom: 0;\r\n        font-size: 16px;\r\n        \r\n    @media (max-width: 575.98px) {\r\n      display: none;\r\n    }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-input-section{\r\n  border-top: 1px solid $border-color;\r\n}\r\n\r\n.chat-input{\r\n  border-radius: 30px;\r\n  background-color: $light !important;\r\n  border-color:  $light !important;\r\n  padding-right: 120px;\r\n}\r\n\r\n.chat-input-links{\r\n  position: absolute;\r\n  right: 16px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  li{\r\n    a{\r\n      font-size: 16px;\r\n      line-height: 36px;\r\n      padding: 0px 4px;\r\n      display: inline-block;\r\n    }\r\n  }\r\n}\r\n\r\n.chat-send{\r\n  @media (max-width: 575.98px) {\r\n    min-width: auto;\r\n  }\r\n}", "// \r\n// projects.scss\r\n//\r\n\r\n// project list\r\n\r\n.project-list-table{\r\n    border-collapse:separate; \r\n    border-spacing:0 12px; \r\n  tr{\r\n    background-color: $card-bg;\r\n  }\r\n  \r\n}", "// \r\n// Contacts.scss\r\n//\r\n\r\n.contact-links{\r\n  a{\r\n    color: $body-color;\r\n  }\r\n}\r\n\r\n// profile\r\n\r\n.profile-user-wid{\r\n  margin-top: -26px;\r\n}", "// \r\n// crypto.scss\r\n//\r\n\r\n@media (min-width: 576px){\r\n    .currency-value{\r\n        position: relative;\r\n        &:after{\r\n            content: \"\\F04E1\";\r\n            font-family: \"Material Design Icons\";\r\n            font-size: 24px;\r\n            position: absolute;\r\n            width: 45px;\r\n            height: 45px;\r\n            line-height: 45px;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            right: 0;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n            background-color: $primary;\r\n            color: $white;\r\n            z-index: 9;\r\n            right: -34px;\r\n        }\r\n    }\r\n\r\n}\r\n\r\n.crypto-buy-sell-nav-content{\r\n    border: 2px solid $gray-300;\r\n    border-top: 0;\r\n}\r\n\r\n\r\n// KYC Application\r\n\r\n.kyc-doc-verification{\r\n    .dropzone{\r\n        min-height: 180px;\r\n        .dz-message{\r\n            margin: 24px 0px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/******************\r\n    Ico Landing\r\n*******************/\r\n\r\n.section{\r\n    position: relative;\r\n    padding-top: 80px;\r\n    padding-bottom: 80px;\r\n\r\n    &.bg-white{\r\n        background-color: $card-bg !important;\r\n    }\r\n}\r\n\r\n.small-title{\r\n    color: $gray-600;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n\r\n// Navigation\r\n\r\n.navigation {\r\n    padding: 0 16px;\r\n    width: 100%;\r\n    z-index: 999;\r\n    margin-bottom: 0px;\r\n    transition: all 0.5s ease-in-out;\r\n    border-bottom: 1px solid rgba($white, 0.1);\r\n\r\n    @media (max-width: 991.98px) {\r\n        background-color: $topnav-bg;\r\n    }\r\n    \r\n    .navbar-logo{\r\n        line-height: 70px;\r\n        transition: all 0.4s;\r\n        .logo-dark{\r\n            display: none;\r\n            @media (max-width: 991.98px) {\r\n                display: block;\r\n            }\r\n        }\r\n        .logo-light{\r\n            display: block;\r\n            @media (max-width: 991.98px) {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n    \r\n\r\n    .navbar-nav{\r\n        .nav-item{\r\n            .nav-link{\r\n                color: rgba($white, 0.6);\r\n                line-height: 58px;\r\n                padding: 6px 16px;\r\n                font-weight: $font-weight-medium;\r\n                transition: all 0.4s;\r\n\r\n                @media (max-width: 991.98px) {\r\n                    color: $header-item-color;\r\n                }\r\n\r\n                &:hover, &.active{\r\n                    color:  rgba($white, 0.9);\r\n\r\n                    @media (max-width: 991.98px) {\r\n                        color: $primary;\r\n                    }\r\n                }\r\n\r\n                @media (max-width: 991.98px) {\r\n                    line-height: 28px !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &.nav-sticky{\r\n        background-color: $topnav-bg;\r\n        box-shadow: $box-shadow;\r\n        .navbar-logo{\r\n            line-height: 60px;\r\n\r\n            .logo-dark{\r\n                display: $display-block;\r\n            }\r\n            .logo-light{\r\n                display: $display-none;\r\n            }\r\n        }\r\n\r\n        .navbar-nav {\r\n            .nav-item {\r\n                .nav-link{\r\n                    line-height: 48px;\r\n                    color: $header-item-color;\r\n                    &:hover, &.active{\r\n                        color: $primary;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.bg-overlay{\r\n    position: absolute;\r\n    height: 100%;\r\n    width: 100%;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    top: 0;\r\n    opacity: 0.7;\r\n    background-color: $black;\r\n}\r\n\r\n.hero-section{\r\n    padding-top: 220px;\r\n    padding-bottom: 190px;\r\n\r\n    &.bg-ico-hero{\r\n        background-image: url(\"../images/crypto/bg-ico-hero.jpg\");\r\n        background-size: cover;\r\n        background-position: top;\r\n    }\r\n\r\n    @media (max-width: 575.98px) {\r\n        padding-top: 140px;\r\n        padding-bottom: 80px;\r\n    }\r\n\r\n    .hero-title{\r\n        font-size: 42px;\r\n        @media (max-width: 575.98px) {\r\n            font-size: 26px;\r\n        }\r\n    }\r\n\r\n    .ico-countdown{\r\n        font-size: 22px;\r\n        margin-right: -12px;\r\n        margin-left: -12px;\r\n        @media (max-width: 575.98px) {\r\n            display: block;\r\n        }\r\n        .coming-box{\r\n            margin-right: 12px;\r\n            margin-left: 12px;\r\n            border: 1px solid $border-color;\r\n            border-radius: 4px;\r\n            padding: 8px;\r\n            background-color: $card-bg;\r\n\r\n            @media (max-width: 575.98px) {\r\n                display: inline-block;\r\n                width: 40%;\r\n                margin-bottom: 24px;\r\n            }\r\n\r\n            span{\r\n                background-color: $light;\r\n                font-size: 12px;\r\n                padding: 4px;\r\n                margin-top: 8px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .softcap-progress{\r\n        overflow: visible;\r\n\r\n        .progress-bar{\r\n            overflow: visible;\r\n        }\r\n\r\n        .progress-label{\r\n            position: relative;\r\n            text-align: right;\r\n            color: $body-color;\r\n            bottom: 20px;\r\n            font-size: 12px;\r\n            font-weight: $font-weight-medium;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// currency price\r\n\r\n.currency-price{\r\n    position: relative;\r\n    bottom: 40px;\r\n}\r\n\r\n\r\n// Clients\r\n\r\n.client-images {\r\n    img {\r\n        max-height: 34px;\r\n        width: auto !important;\r\n        margin: 12px auto;\r\n        opacity: 0.7;\r\n        transition: all 0.4s;\r\n    }\r\n}\r\n\r\n\r\n// Features\r\n\r\n.features-number{\r\n    opacity: 0.1;\r\n}\r\n\r\n\r\n// Team\r\n\r\n.team-box{\r\n    .team-social-links{\r\n        a{\r\n            color: $body-color;\r\n            font-size: 14px;\r\n        }\r\n    }\r\n}\r\n\r\n// Blog\r\n\r\n.blog-box{\r\n    .blog-badge{\r\n        position: absolute;\r\n        top: 12px;\r\n        right: 12px;\r\n    }\r\n}\r\n\r\n// landing footer\r\n\r\n.landing-footer{\r\n    padding: 80px 0 40px;\r\n    background-color: $sidebar-dark-bg;\r\n    color: rgba($white, 0.5);\r\n\r\n    .footer-list-title{\r\n        color: rgba($white, 0.9);\r\n    }\r\n\r\n    .footer-list-menu {\r\n        li {\r\n            a {\r\n                display: block;\r\n                color: rgba($white, 0.5);\r\n                margin-bottom: 14px;\r\n                transition: all 0.4s;\r\n                &:hover{\r\n                    color: rgba($white, 0.8);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .blog-post{\r\n\r\n        .post{\r\n            display: block;\r\n            color: rgba($white, 0.5);\r\n            padding: 16px 0px;\r\n            border-bottom: 1px solid rgba($white, 0.1);\r\n            .post-title{\r\n                color: rgba($white, 0.8);\r\n                font-size: 14px;\r\n            }\r\n\r\n            &:first-of-type{\r\n                padding-top: 0;\r\n            }\r\n\r\n            &:last-of-type{\r\n                padding-bottom: 0;\r\n                border-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    .footer-border{\r\n        border-color: rgba($white, 0.1);\r\n    }\r\n}\r\n\r\n", "// \r\n// coming-soon.scss\r\n//\r\n\r\n.counter-number {\r\n    font-size: 32px;\r\n    font-weight: $font-weight-semibold;\r\n    text-align: center;\r\n    display: flex;\r\n    span {\r\n        font-size: 16px;\r\n        font-weight: $font-weight-normal;\r\n        display: block;\r\n        padding-top: 5px;\r\n    }\r\n}\r\n\r\n.coming-box {\r\n    width: 25%;\r\n}\r\n", "// \r\n// timeline.scss\r\n//\r\n\r\n/************** Horizontal timeline **************/ \r\n\r\n\r\n.hori-timeline{\r\n    .events{\r\n        .event-list{\r\n            text-align: center;\r\n            display: block;\r\n\r\n            .event-down-icon{\r\n                position: relative;\r\n                &::before{\r\n                    content: \"\";\r\n                    position: absolute;\r\n                    width: 100%;\r\n                    top: 16px;\r\n                    left: 0;\r\n                    right: 0;\r\n                    border-bottom: 3px dashed $gray-300;\r\n                }\r\n                .down-arrow-icon{\r\n                    position: relative;\r\n                    background-color: $card-bg;\r\n                    padding: 4px;\r\n                }\r\n            }\r\n\r\n            &:hover{\r\n                .down-arrow-icon{\r\n                    animation: fade-down 1.5s infinite linear;\r\n                }\r\n            }\r\n\r\n            &.active{\r\n                .down-arrow-icon{\r\n                    animation: fade-down 1.5s infinite linear;\r\n                    &:before {\r\n                        content: \"\\ec4c\";\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/************** vertical timeline **************/ \r\n\r\n.verti-timeline{\r\n    border-left: 3px dashed $gray-300;\r\n    margin: 0 10px;\r\n    .event-list{\r\n        position: relative;\r\n        padding: 0px 0px 40px 30px;\r\n\r\n        .event-timeline-dot{\r\n            position: absolute;\r\n            left: -9px;\r\n            top: 0px;\r\n            z-index: 9;\r\n            font-size: 16px;\r\n        }\r\n        .event-content{\r\n            position: relative;\r\n            border: 2px solid $border-color;\r\n            border-radius: 7px;\r\n        }\r\n\r\n        &.active{\r\n            .event-timeline-dot{\r\n                color: $primary;\r\n            }\r\n        }\r\n\r\n        &:last-child{\r\n            padding-bottom: 0px;\r\n        }\r\n    }\r\n}", "// \r\n// Extras pages.scss\r\n//\r\n\r\n\r\n// pricing\r\n\r\n\r\n.plan-box{\r\n    .plan-btn{\r\n        position: relative;\r\n\r\n        &::before{\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 2px;\r\n            background: $gray-300;\r\n            left: 0px;\r\n            right: 0px;\r\n            top: 12px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n// blog\r\n\r\n.blog-play-icon{\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 0;\r\n    right: 0;\r\n    transform: translateY(-50%);\r\n    margin: 0px auto;\r\n}", "// \r\n// general-rtl.scss\r\n//\r\n\r\nhtml {\r\n    direction: rtl;\r\n}\r\n\r\nbody {\r\n    text-align: right;\r\n}", "// \r\n// bootstrap-rtl.scss\r\n//\r\n\r\n\r\n// Dropdowns\r\n\r\n.dropdown-menu {\r\n    &.show {\r\n        text-align: right;\r\n        left: auto !important;\r\n        right: 0;\r\n        bottom: auto;\r\n    }\r\n}\r\n\r\n.dropdown-menu-right {\r\n    right: auto !important;\r\n    left: 0 !important;\r\n    &.show {\r\n        left: 0 !important;\r\n    }\r\n}\r\n\r\n\r\n\r\n// List\r\n\r\nul {\r\n    padding-right: 0;\r\n}\r\n\r\n.list-inline-item:not(:last-child) {\r\n    margin-left: 6px;\r\n    margin-right: 0px;\r\n}\r\n\r\n// border\r\n\r\n.border-right   { \r\n    border-left: $border-width solid $border-color !important; \r\n}\r\n\r\n.border-left    { \r\n    border-right: $border-width solid $border-color !important; \r\n}\r\n\r\n\r\n// Buttons\r\n\r\n.btn-label {\r\n        padding-right: 44px;\r\n        padding-left: 12px;\r\n    .label-icon{\r\n        left: auto;\r\n        right: 0;\r\n        border-right: 0;\r\n        border-left: 1px solid rgba($white, 0.4);\r\n    }\r\n\r\n    &.btn-light{\r\n        .label-icon{\r\n            border-right: 0;\r\n            border-left: 1px solid rgba($dark, 0.2);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n.btn-group,\r\n.btn-group-vertical {\r\n    direction: ltr;\r\n}\r\n\r\n// pagination\r\n\r\n.pagination{\r\n    .page-item {\r\n        &:first-child {\r\n            .page-link {\r\n                margin-right: 0;//rtl\r\n                border-top-left-radius: 0px;\r\n                border-bottom-left-radius: 0px;\r\n                @include border-right-radius($border-radius);//rtl\r\n            }\r\n        }\r\n        &:last-child {\r\n            .page-link {\r\n                border-top-right-radius: 0px;\r\n                border-bottom-right-radius: 0px;\r\n                @include border-left-radius($border-radius);//rtl\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// blockquote\r\n\r\n.blockquote-reverse{\r\n    text-align: left !important;\r\n}\r\n\r\n// dl\r\n\r\ndd {\r\n    margin-right: 0;\r\n}\r\n\r\n// Modal\r\n\r\n\r\n.custom-modal-title{\r\n    text-align: right;\r\n}\r\n\r\n.modal-header {\r\n    .close {\r\n        margin: (-$modal-header-padding-y) auto (-$modal-header-padding-x) (-$modal-header-padding-y);\r\n        left: 0px;\r\n    }\r\n}\r\n\r\n.modal-demo {\r\n    .close{\r\n        left: 25px;\r\n        right: auto;\r\n    }\r\n}\r\n\r\n\r\n.modal-footer {\r\n    > :not(:first-child) {\r\n        margin-right: .25rem;\r\n        margin-left: 0;\r\n    }\r\n\r\n    > :not(:last-child) {\r\n        margin-left: .25rem;\r\n        margin-right: 0;\r\n    }\r\n}\r\n\r\n\r\n// Alerts\r\n\r\n.alert-dismissible {\r\n    padding-left: $close-font-size + $alert-padding-x * 2;\r\n    padding-right: $alert-padding-x;\r\n\r\n    .close {\r\n        left: 0;\r\n        right: auto;\r\n    }\r\n}\r\n\r\n\r\n// Breadcrumb item arrow\r\n\r\n.breadcrumb-item {\r\n    +.breadcrumb-item {\r\n        padding-right: $breadcrumb-item-padding;\r\n        padding-left: 0px;\r\n        &::before {\r\n            padding-left: $breadcrumb-item-padding;\r\n            padding-right: 0px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Forms right check box\r\n\r\n.form-check-right {\r\n    padding-right: 0 !important;\r\n    padding-left: 1.25rem !important;\r\n  \r\n    .form-check-input{\r\n      right: auto;\r\n      left: 0;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n  \r\n  \r\n  .custom-control-right{\r\n    padding-left: 1.5rem !important;\r\n    padding-right: 0 !important;\r\n  \r\n    .custom-control-label{\r\n      display: block;\r\n      &:before, &:after{\r\n        right: auto;\r\n        left: -1.5rem;\r\n      }\r\n    }\r\n  \r\n    .custom-control-input{\r\n      right: auto;\r\n      left: 0;\r\n    }\r\n}\r\n\r\n\r\n// Custom Checkbox-Radio \r\n\r\n.form-check{\r\n    padding-left: 0;\r\n    padding-right: $form-check-input-gutter;\r\n}\r\n\r\n.form-check-input{\r\n    margin-left: 0;\r\n    margin-right: -$form-check-input-gutter;\r\n}\r\n\r\n.form-check-inline{\r\n    margin-left: .75rem;\r\n    margin-right: 0;\r\n}\r\n\r\n.custom-control {\r\n    padding-right: $custom-control-gutter + $custom-control-indicator-size;\r\n    padding-left: 0;\r\n}\r\n\r\n.custom-control-inline{\r\n    margin-right: 0;\r\n    margin-left: $custom-control-spacer-x;\r\n}\r\n\r\n.custom-control-label {\r\n    &::before {\r\n        left: auto;\r\n        right: -($custom-control-gutter + $custom-control-indicator-size);\r\n    }\r\n\r\n    // Foreground (icon)\r\n    &::after {\r\n        left: auto;\r\n        right: -($custom-control-gutter + $custom-control-indicator-size);\r\n    }\r\n}\r\n\r\n.custom-switch {\r\n    padding-right: $custom-switch-width + $custom-control-gutter;\r\n    padding-left: 0;\r\n\r\n    .custom-control-label {\r\n        &::before {\r\n            right: -($custom-switch-width + $custom-control-gutter);\r\n            left: auto;\r\n        }\r\n\r\n        &::after {\r\n            right: calc(#{-($custom-switch-width + $custom-control-gutter)} + #{$custom-control-indicator-border-width * 2});\r\n            left: auto;\r\n        }\r\n    }\r\n\r\n    .custom-control-input:checked~.custom-control-label {\r\n        &::after {\r\n            transform: translateX(#{-($custom-switch-width - $custom-control-indicator-size)});\r\n        }\r\n    }\r\n}\r\n\r\n.custom-file-label {\r\n    &::after {\r\n        right: auto;\r\n        left: 0;\r\n        border-right: inherit;\r\n    }\r\n}\r\n\r\n.custom-switch-md{\r\n    padding-right: 3rem;\r\n    padding-left: 0;\r\n    .custom-control-label{\r\n        &:before{\r\n            left: auto;\r\n            right: -3rem;\r\n        }\r\n\r\n        &:after{\r\n            right: calc(-3rem + 2px);\r\n            left: auto;\r\n        }\r\n\r\n    }\r\n\r\n    .custom-control-input:checked~.custom-control-label::after{\r\n        transform: translateX(-1.25rem);\r\n    }\r\n}\r\n\r\n\r\n.custom-switch-lg{\r\n    padding-right: 3.75rem;\r\n    padding-left: 0;\r\n    .custom-control-label{\r\n        &:before{\r\n            left: auto;\r\n            right: -3.75rem;\r\n        }\r\n\r\n        &:after{\r\n            right: calc(-3.75rem + 2px);\r\n            left: auto;\r\n        }\r\n\r\n    }\r\n\r\n    .custom-control-input:checked~.custom-control-label::after{\r\n        transform: translateX(-1.5rem);\r\n    }\r\n}\r\n\r\n\r\n// custom-checkbox-outline\r\n.custom-checkbox-outline {\r\n    .custom-control-input:checked~.custom-control-label:after{\r\n        left: auto;\r\n        right: -28px;\r\n    }\r\n}\r\n\r\n// custom-radio-outline\r\n.custom-radio-outline {\r\n    .custom-control-input:checked~.custom-control-label:after{\r\n        left: auto;\r\n        right: -20px;\r\n    }\r\n}\r\n\r\n// Input Group\r\n\r\n.input-group-prepend {\r\n    margin-left: -1px;\r\n    margin-right: 0;\r\n}\r\n\r\n.input-group-append {\r\n    margin-right: -1px;\r\n    margin-left: 0;\r\n}\r\n\r\n.input-group>.input-group-prepend>.btn,\r\n.input-group>.input-group-prepend>.input-group-text,\r\n.input-group>.input-group-append:not(:last-child)>.btn,\r\n.input-group>.input-group-append:not(:last-child)>.input-group-text,\r\n.input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle),\r\n.input-group>.input-group-append:last-child>.input-group-text:not(:last-child),\r\n.input-group>.custom-select:not(:last-child),\r\n.input-group>.form-control:not(:last-child) {\r\n    border-top-right-radius: $input-border-radius;\r\n    border-bottom-right-radius: $input-border-radius;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n}\r\n\r\n.input-group>.input-group-append>.btn,\r\n.input-group>.input-group-append>.input-group-text,\r\n.input-group>.input-group-prepend:not(:first-child)>.btn,\r\n.input-group>.input-group-prepend:not(:first-child)>.input-group-text,\r\n.input-group>.input-group-prepend:first-child>.btn:not(:first-child),\r\n.input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child),\r\n.input-group>.custom-select:not(:first-child),\r\n.input-group>.form-control:not(:first-child) {\r\n    border-top-left-radius: $input-border-radius;\r\n    border-bottom-left-radius: $input-border-radius;\r\n    border-top-right-radius: 0;\r\n    border-bottom-right-radius: 0;\r\n}", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// <PERSON><PERSON> and Padding\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $prop, $abbrev in (margin: m, padding: p) {\n      @each $size, $length in $spacers {\n        .#{$abbrev}#{$infix}-#{$size} { #{$prop}: $length !important; }\n        .#{$abbrev}t#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-top: $length !important;\n        }\n        .#{$abbrev}r#{$infix}-#{$size} {\n          #{$prop}-left: $length !important;\n          #{$prop}-right: 0 !important;\n        }\n        .#{$abbrev}b#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-bottom: $length !important;\n        }\n        .#{$abbrev}l#{$infix}-#{$size} {\n          #{$prop}-right: $length !important;\n          #{$prop}-left: 0 !important;\n        }\n      }\n    }\n\n    // Negative margins (e.g., where `.mb-n1` is negative version of `.mb-1`)\n    @each $size, $length in $spacers {\n      @if $size != 0 {\n        .m#{$infix}-n#{$size} { margin: -$length !important; }\n        .mt#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-top: -$length !important;\n        }\n        .mr#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-right: -$length !important;\n        }\n        .mb#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-bottom: -$length !important;\n        }\n        .ml#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-left: -$length !important;\n        }\n      }\n    }\n\n    // Some special margin utils\n    .m#{$infix}-auto { margin: auto !important; }\n    .mt#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-top: auto !important;\n    }\n    .mr#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-left: auto !important;\n      margin-right: inherit !important;\n    }\n    .mb#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-bottom: auto !important;\n    }\n    .mx#{$infix}-auto {\n      margin-right: auto !important;\n      margin-left: auto !important;\n    }\n    .ml#{$infix}-auto{\n      margin-right: auto !important;\n      margin-left: 0 !important;\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .float#{$infix}-left  { float: right !important; }\n    .float#{$infix}-right { float: left !important; }\n    .float#{$infix}-none  { float: none !important; }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Text\n//\n\n\n// Responsive alignment\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .text#{$infix}-left   { text-align: right !important; }\n    .text#{$infix}-right  { text-align: left !important; }\n    .text#{$infix}-center { text-align: center !important; }\n  }\n}\n", "// \r\n// structure-rtl.scss\r\n//\r\n\r\n// topbar.scss\r\n\r\n.navbar-header{\r\n    padding: 0 0 0 calc(#{$grid-gutter-width} / 2);\r\n}\r\n\r\n.noti-icon {\r\n    .badge{\r\n        right: auto;\r\n        left: 4px;\r\n    }\r\n}\r\n\r\n.main-content{\r\n    margin-left: 0px;\r\n    margin-right: $sidebar-width;\r\n}\r\n\r\n.footer{\r\n    left: 0px;\r\n    right: $sidebar-width;\r\n}\r\n\r\n// Sidebar\r\n\r\n#sidebar-menu {\r\n    .has-arrow{\r\n        &:after{\r\n            float: left;\r\n        }\r\n    }\r\n    ul {\r\n        li {\r\n            ul.sub-menu{\r\n                li {\r\n                    a{\r\n                        padding: .4rem 3.5rem .4rem 1.5rem;\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n\r\n                        li {\r\n                            a {\r\n                                padding: .4rem 4.5rem .4rem 1.5rem;\r\n                                font-size: 13.5px;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.vertical-collpsed {\r\n    .main-content{\r\n        margin-left: 0px;\r\n        margin-right: $sidebar-collapsed-width;\r\n    }\r\n\r\n    .footer{\r\n        left: 0px;\r\n        right: $sidebar-collapsed-width;\r\n    }\r\n    \r\n    .vertical-menu {\r\n        #sidebar-menu{\r\n            >ul{\r\n                >li{\r\n                    >a {\r\n                        i{\r\n                            margin-left: 0;\r\n                            margin-right: 4px;\r\n                        }\r\n\r\n                        span{\r\n                            padding-right: 25px;\r\n                            padding-left: 0;\r\n                        }\r\n                    }\r\n\r\n                    &:hover{\r\n                        >ul{\r\n                            left: 0;\r\n                            right: 70px;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul li:hover>ul{\r\n                    left: auto;\r\n                    right: 190px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n\r\n    .main-content {\r\n        margin-right: 0 !important;\r\n    }\r\n    .footer {\r\n        right: 0;\r\n    }\r\n}\r\n\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    float: left !important;\r\n    left: -($rightbar-width + 10px);\r\n    right: auto;\r\n\r\n    .user-box {\r\n        .user-img {\r\n            .user-edit {\r\n                right: 0;\r\n                left: -5px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        left: 0;\r\n        right: auto;\r\n    }\r\n}\r\n\r\n\r\n// Compact sidebar\r\n\r\nbody[data-sidebar-size=small] {\r\n    #sidebar-menu {\r\n        ul {\r\n            li {\r\n                ul.sub-menu {\r\n                    li {\r\n                        a{\r\n                            padding-right: 1.5rem;\r\n                        }\r\n\r\n                        ul.sub-menu {\r\n    \r\n                            li {\r\n                                a {\r\n                                    padding-right: 1.5rem;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &.vertical-collpsed {\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                text-align: right;\r\n            }\r\n        }\r\n\r\n        .main-content {\r\n            margin-left: 0;\r\n            margin-right: $sidebar-collapsed-width;\r\n        }\r\n\r\n        body[data-sidebar-size=small].vertical-collpsed .footer {\r\n            left: 70px;\r\n        }\r\n    }\r\n\r\n    .main-content {\r\n        margin-right: $sidebar-width-sm;\r\n        margin-left: 0;\r\n    }\r\n}\r\n\r\n// Horizontal layout\r\n\r\nbody[data-layout=horizontal] {\r\n    .main-content{\r\n        margin-right: 0!important;\r\n    }\r\n\r\n    .footer {\r\n        right: 0!important;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n    .topnav {\r\n        .navbar-nav {\r\n            .nav-item {\r\n                &:first-of-type {\r\n                    .nav-link {\r\n                        padding-left: 1.3rem;\r\n                        padding-right: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown {\r\n            .dropdown-menu{\r\n                right: 0px;\r\n                left: auto;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.arrow-down{\r\n    &:after{\r\n        margin-left: 0px;\r\n        margin-right: 10px;\r\n    }\r\n}\r\n\r\n.navbar-nav {\r\n    .dropdown-menu{\r\n        text-align: right;\r\n    }\r\n}\r\n\r\n@media (min-width: 992px){\r\n    .topnav {\r\n        .dropdown {\r\n            .dropdown-menu {\r\n                .arrow-down::after {\r\n                    right: auto;\r\n                    left: 15px;\r\n                    transform: rotate(45deg) translateY(-50%);\r\n                }\r\n\r\n                .dropdown {\r\n                    .dropdown-menu{\r\n                        right: 100%;\r\n                        left: auto;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991.98px){\r\n    .topnav {\r\n        .dropdown {\r\n            .dropdown-menu {\r\n                padding-left: 0px;\r\n                padding-right: 15px;\r\n            }\r\n        }\r\n    }\r\n}\r\n", "// \r\n// plugins-rtl.scss\r\n//\r\n\r\n// calendar\r\n\r\n.lnb-calendars-item{\r\n    margin-right: 0;\r\n    margin-left: 7px;\r\n}\r\n\r\ninput[type=checkbox].tui-full-calendar-checkbox-round+span{\r\n    margin-right: 0px;\r\n    margin-left: 8px;\r\n}\r\n\r\n.tui-full-calendar-time-schedule-content{\r\n    padding: 1px 3px 0 0;\r\n}\r\n\r\n// FLOT CHART\r\n\r\n.legendLabel {\r\n    padding-right: 5px!important;\r\n    padding-left: 20px;\r\n}\r\n\r\n// Select 2\r\n\r\n.select2-container {\r\n    .select2-selection--single {\r\n        .select2-selection__rendered {\r\n            padding-right: 12px;\r\n        }\r\n\r\n        .select2-selection__arrow {\r\n            left: 3px;\r\n            right: auto;\r\n        }\r\n    }\r\n\r\n    .select2-selection--multiple {\r\n        .select2-selection__choice {\r\n            float: right;\r\n            margin-left: 5px;\r\n            margin-right: 0;\r\n        }\r\n    }\r\n\r\n    .select2-search__field{\r\n        text-align: right;\r\n    }\r\n\r\n    .select2-search--inline {\r\n        float: right;\r\n    }\r\n}\r\n\r\n// Bootstrap select\r\n\r\n.bootstrap-select {\r\n    .dropdown-toggle {\r\n        &:before {\r\n            float: left;\r\n        }\r\n\r\n        .filter-option {\r\n            text-align: right;\r\n        }\r\n\r\n        .filter-option-inner {\r\n            padding-right: 0;\r\n            padding-left: inherit;\r\n        }\r\n    }\r\n}\r\n\r\n// datatable\r\n\r\n.dataTables_wrapper {\r\n    .dataTables_filter{\r\n        text-align: left !important;\r\n        input{\r\n            margin-left: 0px !important;\r\n            margin-right: 0.5em;\r\n        }\r\n    }\r\n}\r\n\r\n// Foo table\r\n\r\n.footable.breakpoint>tbody>tr>td>span.footable-toggle {\r\n    padding-left: 5px;\r\n    padding-right: 0;\r\n}\r\n\r\n// tablesaw\r\n.tablesaw-columntoggle-popup {\r\n    .tablesaw-btn-group {\r\n        > label {\r\n            input{\r\n                margin-right: 0;\r\n                margin-left: .8em;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.tablesaw-bar {\r\n    .tablesaw-bar-section {\r\n        .tablesaw-btn{\r\n            margin-left: 0;\r\n            margin-right: .4em;\r\n        }\r\n    }\r\n}\r\n// Responsive Table\r\n\r\n.table-rep-plugin {\r\n    .btn-group.pull-right {\r\n        float: left;\r\n    }\r\n    .checkbox-row {\r\n        label{\r\n            &:after{\r\n                margin-left: -22px;\r\n                top: -2px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Parsley\r\n.parsley-errors-list {\r\n    >li {\r\n        padding-left: 0;\r\n        padding-right: 20px;\r\n\r\n        &:before {\r\n            left: auto;\r\n            right: 2px;\r\n        }\r\n    }\r\n}\r\n\r\n/* =============\r\n   Form wizard\r\n============= */\r\n\r\n.twitter-bs-wizard {\r\n    .twitter-bs-wizard-nav {\r\n        @media (min-width: 992px){\r\n            .nav-link {\r\n                text-align: right;\r\n            }\r\n        }\r\n    }\r\n\r\n    .twitter-bs-wizard-pager-link {\r\n        li.next{\r\n            float: left;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// X-ediatable \r\n\r\n.editable-buttons {\r\n    margin-left: 0;\r\n    margin-right: 7px;\r\n\r\n    .editable-cancel {\r\n        margin-left: 0;\r\n        margin-right: 7px;\r\n    }\r\n}", "//\n// components-rtl.scss\n//\n\n// dropdown\n\n.dropdown-megamenu{\n  &.show{\n    left: 20px!important;\n  }\n}\n\n\n// icons\n\n.icon-list-demo{\n  i{\n    margin-left: 12px;\n    margin-right: 0;\n  }\n}\n\n\n\n\n// Invoice\n\n@media print {\n  .content-page,\n  .content,\n  body {\n      margin-right: 0;\n  }\n}\n\n// Demos button \n.demos-show-btn {\n  left: 0;\n  right: auto;\n  border-radius: 0 6px 6px 0;\n}", "// \r\n// pages-rtl.scss\r\n//\r\n\r\n\r\n// timeline\r\n\r\n.verti-timeline{\r\n    border-left: 0;\r\n    border-right: 3px dashed $gray-300;\r\n    .event-list{\r\n        padding: 0 30px 40px 0;\r\n        .event-timeline-dot{\r\n            left: auto;\r\n            right: -9px;\r\n        }\r\n    }\r\n}\r\n\r\n// email\r\n\r\n.email-leftbar{\r\n    float: right;\r\n}\r\n\r\n.email-rightbar {\r\n    margin-right: 260px;\r\n    margin-left: 0px;\r\n}\r\n\r\n.message-list {\r\n    li {\r\n        .col-mail{\r\n            float: right;\r\n        }\r\n        .col-mail-1{\r\n            .star-toggle, .checkbox-wrapper-mail, .dot{\r\n                float: right\r\n            }\r\n            .checkbox-wrapper-mail {\r\n                margin: 15px 20px 0 10px;\r\n            }\r\n            .star-toggle {\r\n                margin-right: 5px;\r\n            }\r\n            \r\n            .title {\r\n                right: 110px;\r\n                left: 0;\r\n            }\r\n        }\r\n        .col-mail-2{\r\n            right: 320px;\r\n            left: 0;\r\n\r\n            .subject {\r\n                right: 0;\r\n                left: 200px;\r\n            }\r\n\r\n            .date {\r\n                left: 0;\r\n                right: auto;\r\n                padding-right: 80px;\r\n                padding-left: 0px;\r\n            }\r\n        \r\n        }\r\n    }\r\n    .checkbox-wrapper-mail {\r\n        label{\r\n            &:before{\r\n                right: 4px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 767px){\r\n    .email-leftbar {\r\n        float: none !important;\r\n        width: 100%;\r\n    }\r\n    .email-rightbar {\r\n        margin-right: 0;\r\n    }\r\n}\r\n\r\n// authentication\r\n\r\n.home-btn {\r\n    position: absolute;\r\n    left: 25px;\r\n    right: auto;\r\n}\r\n\r\n\r\n// file manager\r\n\r\n.categories-list li ul{\r\n    padding-right: 16px;\r\n    padding-left: 0;\r\n}\r\n\r\n\r\n// Chat\r\n\r\n.chat-conversation {\r\n    .conversation-list {\r\n        .dropdown{\r\n            float: left;\r\n        }\r\n\r\n        .ctext-wrap{\r\n            border-radius: 8px 8px 0 8px;\r\n        }\r\n    }\r\n\r\n    .right {\r\n        float: left;\r\n        .conversation-list {\r\n            .ctext-wrap{\r\n                text-align: left;\r\n                border-radius: 8px 8px 8px 0;\r\n            }\r\n\r\n            .dropdown{\r\n                float: right;\r\n            }\r\n        }\r\n    }\r\n\r\n    .last-chat .conversation-list:before{\r\n        right: auto;\r\n        left: 0;\r\n    }\r\n}\r\n\r\n.chat-input{\r\n    padding-left: 120px;\r\n    padding-right: 12px;\r\n}\r\n\r\n.chat-input-links{\r\n    right: auto;\r\n    left: 16px;\r\n}\r\n\r\n@media (min-width: 576px){\r\n    .currency-value{\r\n        position: relative;\r\n        &:after{\r\n            right: auto;\r\n            left: -34px;\r\n        }\r\n    }\r\n}"]}