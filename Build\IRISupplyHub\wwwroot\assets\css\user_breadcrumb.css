﻿@import url('https://fonts.googleapis.com/css?family=Roboto:400,700,900&display=swap');

body {
    margin: 0;
    padding: 0;
    font-family: 'Roboto', sans-serif;
    width: 100%;
    height: 100vh;
}

.textPadding {
    padding-bottom: 2.5rem !important;
    width: 24.8%;
}

.crumbs {
    text-align: center;
}

    .crumbs ul {
        list-style: none;
        /*display: inline-block;*/
    }

        .crumbs ul li {
            display: inline;
        }

            .crumbs ul li a {
                display: block;
                float: left;
                height: 27px;
                /*background: #ecf0f1;*/
                background: #2a3042;
                text-align: center;
                position: relative;
                padding: 15px 20px 0 60px;
                /*      margin: 0 10px 0 0;*/
                font-size: 20px;
                text-decoration: none;
                color: #2c3e50;
                box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
                color: white !important;
            }

                .crumbs ul li a:last-child {
                    margin-right: 0px;
                }

                .crumbs ul li a::after {
                    content: '';
                    border-top: 27px solid transparent;
                    border-bottom: 30px solid transparent;
                    border-left: 40px solid #ecf0f1;
                    position: absolute;
                    right: -40px;
                    top: 0;
                    z-index: 1;
                    border-left-color: #2a3042;
                }

                .crumbs ul li a::before {
                    content: '';
                    border-top: 27px solid transparent;
                    border-bottom: 30px solid transparent;
                    border-left: 40px solid #fff;
                    position: absolute;
                    left: 0;
                    top: 0;
                }

            .crumbs ul li:first-child a {
                border-top-left-radius: 10px;
                border-bottom-left-radius: 10px;
            }

                .crumbs ul li:first-child a::before {
                    display: none;
                }

            .crumbs ul li:last-child a {
                padding-right: 40px;
                border-top-right-radius: 10px;
                border-bottom-right-radius: 10px;
                background: #2a3042;
                color: #fff;
            }

                .crumbs ul li:last-child a::after {
                    display: none;
                }



.active_Module {
    background: #0d34e2 !important;
}

    .active_Module:after {
        border-left-color: #0d34e2 !important;
    }

.breadcrumb_elements {
    margin: 0 1px 0 0 !important;
    color: white;
}

.returntext {
    padding-bottom: 2.5rem !important;
    width: 32% !important;
}
@media only screen and (min-width: 993px) and (max-width: 1090px) {
   
    .innerText {
        display: block;
    }
}
@media only screen and (max-width: 769px) {
    .textPadding {
        width: 24.8%;
    }

    .innerText {
        display: none;
    }
}

@media only screen and (max-width: 415px) {
    .innerText:after {
        display: none;
    }

    .textPadding {
        width: 23.8%;
    }

    .crumbs ul li a {
        display: block;
        float: left;
        height: 27px;
        /* background: #ecf0f1; */
        background: #2a3042;
        text-align: center;
        position: relative;
        padding: 15px 0px 0 48px;
        /* margin: 0 10px 0 0; */
        font-size: 20px;
        text-decoration: none;
        color: #2c3e50;
        box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
        color: white !important;
    }

    @media only screen and (max-width: 376px) {
        .textPadding {
            width: 22.8%;
        }

        .innerText {
            display: none;
        }
    }
}
