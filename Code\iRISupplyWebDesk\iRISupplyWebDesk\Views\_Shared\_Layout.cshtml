﻿@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
<!DOCTYPE html>
<html lang="en">

<head>
    
    @await Html.PartialAsync("~/Views/_Shared/_title_meta.cshtml")
    @RenderSection("styles", false)
    @await Html.PartialAsync("~/Views/_Shared/_head_css.cshtml")
    <link rel="stylesheet" href="~/iRISupplyWebDesk.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/assets/kendo/bootstrap-main.css" />
    @*<link rel="stylesheet" href="https://kendo.cdn.telerik.com/themes/6.2.0/bootstrap/bootstrap-main.css" />*@
    @* Kendo scripts are now loaded via webpack bundle - see layoutbundle.js below *@
    @*<script src="~/assets/kendo/jquery-main.js"></script>*@
    @*<script src="https://kendo.cdn.telerik.com/2023.1.314/js/jquery.min.js"></script>*@
    @*<script src="~/assets/kendo/jszip.js"></script>*@
    @*<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/2.4.0/jszip.js"></script>*@
    @*<script src="~/assets/kendo/kendo.all.min.js"></script>*@
    @*<script src="https://kendo.cdn.telerik.com/2023.1.314/js/kendo.all.min.js"></script>*@
    @*<script src="~/assets/kendo/kendo.aspnetmvc.min.js"></script>*@
    @*<script src="https://kendo.cdn.telerik.com/2023.1.314/js/kendo.aspnetmvc.min.js"></script>*@
    @*<link rel="stylesheet" href="~/assets/kendo/toastify.min.css" />*@
    @*<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">*@
    @*<script src="~/assets/kendo/toastify-js.js"></script>*@
    @*<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>*@
    <script src="~/assets/libs/dist/layoutbundle.js" type="text/javascript"></script>
<style>
        .colored-toast.swal2-icon-success {
            background-color: #a5dc86 !important;
        }

 

        .colored-toast.swal2-icon-error {
            background-color: #f27474 !important;
        }
         .vertical-collpsed span.fav_btn {
            margin-top: -29px !important;
        }
    <style>
        .colored-toast.swal2-icon-success {
            background-color: #a5dc86 !important;
        }

        .colored-toast.swal2-icon-error {
            background-color: #f27474 !important;
        }
         .vertical-collpsed span.fav_btn {
            margin-top: -29px !important;
        }
        .page-content{
        padding: calc(48px + 24px) calc(24px / 2) 60px calc(24px / 2) !important;
        }
    </style>
</head>

<body data-sidebar="dark">

    <!-- Begin page -->
    <div id="layout-wrapper">
        @*@await Html.PartialAsync("~/Views/_Shared/_topbar.cshtml")*@
        @await Component.InvokeAsync("Menu")
        @await Component.InvokeAsync("TopBar")

        @*@await Html.PartialAsync("~/Views/_Shared/_sidebar.cshtml")*@

        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">

                    @if (HttpContextAccessor.HttpContext.Session.GetString("CurrentUserModule") == "Use Items" ||
                           HttpContextAccessor.HttpContext.Session.GetString("CurrentUserModule") == "Return" ||
                                    HttpContextAccessor.HttpContext.Session.GetString("CurrentUserModule") == "Patient List")
                        {
                            @await Html.PartialAsync("~/Views/_Shared/_user_page_title.cshtml")
                        }
                        else
                        {
                            @await Html.PartialAsync("~/Views/_Shared/_page_title.cshtml")
                        }
                  
                    @RenderBody()
                </div> <!-- container-fluid -->
            </div>
            <!-- End Page-content -->
            @await Html.PartialAsync("~/Views/_Shared/_footer.cshtml")
        </div>
        <!-- end main content-->

    </div>

    @RenderSection("externalhtml", required: false)

    <!-- END layout-wrapper -->
    @await Html.PartialAsync("~/Views/_Shared/_right_sidebar.cshtml")

    @*@await Html.PartialAsync("~/Views/_Shared/_vendor_scripts.cshtml")*@

    @RenderSection("scripts", required: false)


</body>

</html>
<script>
    //Handle brower closing or tab closing
    //window.addEventListener('beforeunload', function (event) {
    //    alert(event)
    //    event.returnValue = 'Are you sure you want to leave?';
    //});
    //$(window).on('beforeunload', function (event) {
    //    alert("triggered");
    //    $.ajax({
    //        type:'get',
    //        url:'@Url.Action("LogOut","AuthLogin")'
    //    })
    //});




    //Session Timeout
    let isOpenCase = '@HttpContextAccessor.HttpContext.Session.GetString("isOpencase")';
    let needsSessionTimeOut = '@HttpContextAccessor.HttpContext.Session.GetString("needSessionTimeout")';
    //if(isOpenCase !="true" ||needsSessionTimeOut=="true" ){
    if(needsSessionTimeOut=="true" ){
    let time;
    let SessionTimeout = '@HttpContextAccessor.HttpContext.Session.GetInt32("SessionTimeout")';
    SessionTimeout=SessionTimeout * 1000;
  

    function resetTimer() {
        clearTimeout(time);
        time = setTimeout(logout, SessionTimeout)
    }
    function logout() {
        /*$(".modal").modal('hide');*/
            window.location.href = '@Url.Action("LogOut","AuthLogin",new{LogOutEvent="SessionTimedout"})'
    }

    //  window.onload =TimeoutFunc;
    document.onmousemove = resetTimer;
    document.onclick = resetTimer;
    document.onscroll = resetTimer;
    document.ondblclick = resetTimer;
    document.ontouchend = resetTimer;
    }
    else{
        //do nothing
    }


    const Toast = Swal.mixin({
      toast: true,
      position: 'top-right',
      iconColor: 'white',
      customClass: {
        popup: 'colored-toast'
      },
      showConfirmButton: false,
      timer: 3500,
      timerProgressBar: true
    })
</script>