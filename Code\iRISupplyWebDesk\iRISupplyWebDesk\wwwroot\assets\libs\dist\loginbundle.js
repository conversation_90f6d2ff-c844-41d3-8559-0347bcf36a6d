/*! For license information please see loginbundle.js.LICENSE.txt */
(()=>{var e={413:()=>{$(document).ready(function(){$("#LoginBarcodeID");var e=$("#username"),t=$("#userpassword"),n=($("#RemoveRFID"),"");e.on("input",function(t){e.focus()}),t.click(function(){t.focus()}),document.body.addEventListener("keypress",function(e){"Enter"===e.key?null!==n&&""!==n&&(window.location=`${appbaseurl}/AuthLogin/auth?r=${n}`):n+=e.key}),"Invalid User Badge!"===error&&Swal.fire({icon:"error",text:"Do you want to Self-Register your badge?",title:"Invalid Badge",showCancelButton:!0,confirmButtonText:"Yes",cancelButtonText:"No"}).then(e=>{e.isConfirmed&&($("#exampleModal").modal(focus),$("#userbadge").val(invalidBadge))})})},692:function(e,t){var n;!function(t,n){"use strict";"object"==typeof e.exports?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!=typeof window?window:this,function(i,r){"use strict";var o=[],a=Object.getPrototypeOf,s=o.slice,l=o.flat?function(e){return o.flat.call(e)}:function(e){return o.concat.apply([],e)},u=o.push,c=o.indexOf,f={},d=f.toString,h=f.hasOwnProperty,p=h.toString,g=p.call(Object),m={},v=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},y=function(e){return null!=e&&e===e.window},b=i.document,_={type:!0,src:!0,nonce:!0,noModule:!0};function w(e,t,n){var i,r,o=(n=n||b).createElement("script");if(o.text=e,t)for(i in _)(r=t[i]||t.getAttribute&&t.getAttribute(i))&&o.setAttribute(i,r);n.head.appendChild(o).parentNode.removeChild(o)}function x(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?f[d.call(e)]||"object":typeof e}var E="3.7.0",T=/HTML$/i,C=function(e,t){return new C.fn.init(e,t)};function S(e){var t=!!e&&"length"in e&&e.length,n=x(e);return!v(e)&&!y(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function k(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}C.fn=C.prototype={jquery:E,constructor:C,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=C.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return C.each(this,e)},map:function(e){return this.pushStack(C.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(C.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(C.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:u,sort:o.sort,splice:o.splice},C.extend=C.fn.extend=function(){var e,t,n,i,r,o,a=arguments[0]||{},s=1,l=arguments.length,u=!1;for("boolean"==typeof a&&(u=a,a=arguments[s]||{},s++),"object"==typeof a||v(a)||(a={}),s===l&&(a=this,s--);s<l;s++)if(null!=(e=arguments[s]))for(t in e)i=e[t],"__proto__"!==t&&a!==i&&(u&&i&&(C.isPlainObject(i)||(r=Array.isArray(i)))?(n=a[t],o=r&&!Array.isArray(n)?[]:r||C.isPlainObject(n)?n:{},r=!1,a[t]=C.extend(u,o,i)):void 0!==i&&(a[t]=i));return a},C.extend({expando:"jQuery"+(E+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==d.call(e))&&(!(t=a(e))||"function"==typeof(n=h.call(t,"constructor")&&t.constructor)&&p.call(n)===g)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){w(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(S(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},text:function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r)return e.textContent;if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=C.text(t);return n},makeArray:function(e,t){var n=t||[];return null!=e&&(S(Object(e))?C.merge(n,"string"==typeof e?[e]:e):u.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:c.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!T.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;i<n;i++)e[r++]=t[i];return e.length=r,e},grep:function(e,t,n){for(var i=[],r=0,o=e.length,a=!n;r<o;r++)!t(e[r],r)!==a&&i.push(e[r]);return i},map:function(e,t,n){var i,r,o=0,a=[];if(S(e))for(i=e.length;o<i;o++)null!=(r=t(e[o],o,n))&&a.push(r);else for(o in e)null!=(r=t(e[o],o,n))&&a.push(r);return l(a)},guid:1,support:m}),"function"==typeof Symbol&&(C.fn[Symbol.iterator]=o[Symbol.iterator]),C.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){f["[object "+t+"]"]=t.toLowerCase()});var D=o.pop,A=o.sort,N=o.splice,j="[\\x20\\t\\r\\n\\f]",O=new RegExp("^"+j+"+|((?:^|[^\\\\])(?:\\\\.)*)"+j+"+$","g");C.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var I=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function L(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}C.escapeSelector=function(e){return(e+"").replace(I,L)};var P=b,q=u;!function(){var e,t,n,r,a,l,u,f,d,p,g=q,v=C.expando,y=0,b=0,_=ee(),w=ee(),x=ee(),E=ee(),T=function(e,t){return e===t&&(a=!0),0},S="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",I="(?:\\\\[\\da-fA-F]{1,6}"+j+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",L="\\["+j+"*("+I+")(?:"+j+"*([*^$|!~]?=)"+j+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+I+"))|)"+j+"*\\]",H=":("+I+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+L+")*)|.*)\\)|)",R=new RegExp(j+"+","g"),F=new RegExp("^"+j+"*,"+j+"*"),M=new RegExp("^"+j+"*([>+~]|"+j+")"+j+"*"),B=new RegExp(j+"|>"),W=new RegExp(H),U=new RegExp("^"+I+"$"),$={ID:new RegExp("^#("+I+")"),CLASS:new RegExp("^\\.("+I+")"),TAG:new RegExp("^("+I+"|[*])"),ATTR:new RegExp("^"+L),PSEUDO:new RegExp("^"+H),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+j+"*(even|odd|(([+-]|)(\\d*)n|)"+j+"*(?:([+-]|)"+j+"*(\\d+)|))"+j+"*\\)|)","i"),bool:new RegExp("^(?:"+S+")$","i"),needsContext:new RegExp("^"+j+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+j+"*((?:-\\d)?\\d*)"+j+"*\\)|)(?=[^-]|$)","i")},Q=/^(?:input|select|textarea|button)$/i,V=/^h\d$/i,z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,X=/[+~]/,Y=new RegExp("\\\\[\\da-fA-F]{1,6}"+j+"?|\\\\([^\\r\\n\\f])","g"),K=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},G=function(){le()},J=de(function(e){return!0===e.disabled&&k(e,"fieldset")},{dir:"parentNode",next:"legend"});try{g.apply(o=s.call(P.childNodes),P.childNodes),o[P.childNodes.length].nodeType}catch(e){g={apply:function(e,t){q.apply(e,s.call(t))},call:function(e){q.apply(e,s.call(arguments,1))}}}function Z(e,t,n,i){var r,o,a,s,u,c,h,p=t&&t.ownerDocument,y=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==y&&9!==y&&11!==y)return n;if(!i&&(le(t),t=t||l,f)){if(11!==y&&(u=z.exec(e)))if(r=u[1]){if(9===y){if(!(a=t.getElementById(r)))return n;if(a.id===r)return g.call(n,a),n}else if(p&&(a=p.getElementById(r))&&Z.contains(t,a)&&a.id===r)return g.call(n,a),n}else{if(u[2])return g.apply(n,t.getElementsByTagName(e)),n;if((r=u[3])&&t.getElementsByClassName)return g.apply(n,t.getElementsByClassName(r)),n}if(!(E[e+" "]||d&&d.test(e))){if(h=e,p=t,1===y&&(B.test(e)||M.test(e))){for((p=X.test(e)&&se(t.parentNode)||t)==t&&m.scope||((s=t.getAttribute("id"))?s=C.escapeSelector(s):t.setAttribute("id",s=v)),o=(c=ce(e)).length;o--;)c[o]=(s?"#"+s:":scope")+" "+fe(c[o]);h=c.join(",")}try{return g.apply(n,p.querySelectorAll(h)),n}catch(t){E(e,!0)}finally{s===v&&t.removeAttribute("id")}}}return ye(e.replace(O,"$1"),t,n,i)}function ee(){var e=[];return function n(i,r){return e.push(i+" ")>t.cacheLength&&delete n[e.shift()],n[i+" "]=r}}function te(e){return e[v]=!0,e}function ne(e){var t=l.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function ie(e){return function(t){return k(t,"input")&&t.type===e}}function re(e){return function(t){return(k(t,"input")||k(t,"button"))&&t.type===e}}function oe(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&J(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function ae(e){return te(function(t){return t=+t,te(function(n,i){for(var r,o=e([],n.length,t),a=o.length;a--;)n[r=o[a]]&&(n[r]=!(i[r]=n[r]))})})}function se(e){return e&&void 0!==e.getElementsByTagName&&e}function le(e){var n,i=e?e.ownerDocument||e:P;return i!=l&&9===i.nodeType&&i.documentElement?(u=(l=i).documentElement,f=!C.isXMLDoc(l),p=u.matches||u.webkitMatchesSelector||u.msMatchesSelector,P!=l&&(n=l.defaultView)&&n.top!==n&&n.addEventListener("unload",G),m.getById=ne(function(e){return u.appendChild(e).id=C.expando,!l.getElementsByName||!l.getElementsByName(C.expando).length}),m.disconnectedMatch=ne(function(e){return p.call(e,"*")}),m.scope=ne(function(){return l.querySelectorAll(":scope")}),m.cssHas=ne(function(){try{return l.querySelector(":has(*,:jqfake)"),!1}catch(e){return!0}}),m.getById?(t.filter.ID=function(e){var t=e.replace(Y,K);return function(e){return e.getAttribute("id")===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&f){var n=t.getElementById(e);return n?[n]:[]}}):(t.filter.ID=function(e){var t=e.replace(Y,K);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&f){var n,i,r,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(r=t.getElementsByName(e),i=0;o=r[i++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),t.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},t.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&f)return t.getElementsByClassName(e)},d=[],ne(function(e){var t;u.appendChild(e).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||d.push("\\["+j+"*(?:value|"+S+")"),e.querySelectorAll("[id~="+v+"-]").length||d.push("~="),e.querySelectorAll("a#"+v+"+*").length||d.push(".#.+[+~]"),e.querySelectorAll(":checked").length||d.push(":checked"),(t=l.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),u.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&d.push(":enabled",":disabled"),(t=l.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||d.push("\\["+j+"*name"+j+"*="+j+"*(?:''|\"\")")}),m.cssHas||d.push(":has"),d=d.length&&new RegExp(d.join("|")),T=function(e,t){if(e===t)return a=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!m.sortDetached&&t.compareDocumentPosition(e)===n?e===l||e.ownerDocument==P&&Z.contains(P,e)?-1:t===l||t.ownerDocument==P&&Z.contains(P,t)?1:r?c.call(r,e)-c.call(r,t):0:4&n?-1:1)},l):l}for(e in Z.matches=function(e,t){return Z(e,null,null,t)},Z.matchesSelector=function(e,t){if(le(e),f&&!E[t+" "]&&(!d||!d.test(t)))try{var n=p.call(e,t);if(n||m.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){E(t,!0)}return Z(t,l,null,[e]).length>0},Z.contains=function(e,t){return(e.ownerDocument||e)!=l&&le(e),C.contains(e,t)},Z.attr=function(e,n){(e.ownerDocument||e)!=l&&le(e);var i=t.attrHandle[n.toLowerCase()],r=i&&h.call(t.attrHandle,n.toLowerCase())?i(e,n,!f):void 0;return void 0!==r?r:e.getAttribute(n)},Z.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},C.uniqueSort=function(e){var t,n=[],i=0,o=0;if(a=!m.sortStable,r=!m.sortStable&&s.call(e,0),A.call(e,T),a){for(;t=e[o++];)t===e[o]&&(i=n.push(o));for(;i--;)N.call(e,n[i],1)}return r=null,e},C.fn.uniqueSort=function(){return this.pushStack(C.uniqueSort(s.apply(this)))},t=C.expr={cacheLength:50,createPseudo:te,match:$,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Y,K),e[3]=(e[3]||e[4]||e[5]||"").replace(Y,K),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||Z.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&Z.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return $.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&W.test(n)&&(t=ce(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Y,K).toLowerCase();return"*"===e?function(){return!0}:function(e){return k(e,t)}},CLASS:function(e){var t=_[e+" "];return t||(t=new RegExp("(^|"+j+")"+e+"("+j+"|$)"))&&_(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(i){var r=Z.attr(i,e);return null==r?"!="===t:!t||(r+="","="===t?r===n:"!="===t?r!==n:"^="===t?n&&0===r.indexOf(n):"*="===t?n&&r.indexOf(n)>-1:"$="===t?n&&r.slice(-n.length)===n:"~="===t?(" "+r.replace(R," ")+" ").indexOf(n)>-1:"|="===t&&(r===n||r.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,l){var u,c,f,d,h,p=o!==a?"nextSibling":"previousSibling",g=t.parentNode,m=s&&t.nodeName.toLowerCase(),b=!l&&!s,_=!1;if(g){if(o){for(;p;){for(f=t;f=f[p];)if(s?k(f,m):1===f.nodeType)return!1;h=p="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?g.firstChild:g.lastChild],a&&b){for(_=(d=(u=(c=g[v]||(g[v]={}))[e]||[])[0]===y&&u[1])&&u[2],f=d&&g.childNodes[d];f=++d&&f&&f[p]||(_=d=0)||h.pop();)if(1===f.nodeType&&++_&&f===t){c[e]=[y,d,_];break}}else if(b&&(_=d=(u=(c=t[v]||(t[v]={}))[e]||[])[0]===y&&u[1]),!1===_)for(;(f=++d&&f&&f[p]||(_=d=0)||h.pop())&&(!(s?k(f,m):1===f.nodeType)||!++_||(b&&((c=f[v]||(f[v]={}))[e]=[y,_]),f!==t)););return(_-=r)===i||_%i===0&&_/i>=0}}},PSEUDO:function(e,n){var i,r=t.pseudos[e]||t.setFilters[e.toLowerCase()]||Z.error("unsupported pseudo: "+e);return r[v]?r(n):r.length>1?(i=[e,e,"",n],t.setFilters.hasOwnProperty(e.toLowerCase())?te(function(e,t){for(var i,o=r(e,n),a=o.length;a--;)e[i=c.call(e,o[a])]=!(t[i]=o[a])}):function(e){return r(e,0,i)}):r}},pseudos:{not:te(function(e){var t=[],n=[],i=ve(e.replace(O,"$1"));return i[v]?te(function(e,t,n,r){for(var o,a=i(e,null,r,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,r,o){return t[0]=e,i(t,null,o,n),t[0]=null,!n.pop()}}),has:te(function(e){return function(t){return Z(e,t).length>0}}),contains:te(function(e){return e=e.replace(Y,K),function(t){return(t.textContent||C.text(t)).indexOf(e)>-1}}),lang:te(function(e){return U.test(e||"")||Z.error("unsupported lang: "+e),e=e.replace(Y,K).toLowerCase(),function(t){var n;do{if(n=f?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(e){var t=i.location&&i.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===u},focus:function(e){return e===function(){try{return l.activeElement}catch(e){}}()&&l.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:oe(!1),disabled:oe(!0),checked:function(e){return k(e,"input")&&!!e.checked||k(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!t.pseudos.empty(e)},header:function(e){return V.test(e.nodeName)},input:function(e){return Q.test(e.nodeName)},button:function(e){return k(e,"input")&&"button"===e.type||k(e,"button")},text:function(e){var t;return k(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:ae(function(){return[0]}),last:ae(function(e,t){return[t-1]}),eq:ae(function(e,t,n){return[n<0?n+t:n]}),even:ae(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:ae(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:ae(function(e,t,n){var i;for(i=n<0?n+t:n>t?t:n;--i>=0;)e.push(i);return e}),gt:ae(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}},t.pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=ie(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=re(e);function ue(){}function ce(e,n){var i,r,o,a,s,l,u,c=w[e+" "];if(c)return n?0:c.slice(0);for(s=e,l=[],u=t.preFilter;s;){for(a in i&&!(r=F.exec(s))||(r&&(s=s.slice(r[0].length)||s),l.push(o=[])),i=!1,(r=M.exec(s))&&(i=r.shift(),o.push({value:i,type:r[0].replace(O," ")}),s=s.slice(i.length)),t.filter)!(r=$[a].exec(s))||u[a]&&!(r=u[a](r))||(i=r.shift(),o.push({value:i,type:a,matches:r}),s=s.slice(i.length));if(!i)break}return n?s.length:s?Z.error(e):w(e,l).slice(0)}function fe(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function de(e,t,n){var i=t.dir,r=t.next,o=r||i,a=n&&"parentNode"===o,s=b++;return t.first?function(t,n,r){for(;t=t[i];)if(1===t.nodeType||a)return e(t,n,r);return!1}:function(t,n,l){var u,c,f=[y,s];if(l){for(;t=t[i];)if((1===t.nodeType||a)&&e(t,n,l))return!0}else for(;t=t[i];)if(1===t.nodeType||a)if(c=t[v]||(t[v]={}),r&&k(t,r))t=t[i]||t;else{if((u=c[o])&&u[0]===y&&u[1]===s)return f[2]=u[2];if(c[o]=f,f[2]=e(t,n,l))return!0}return!1}}function he(e){return e.length>1?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function pe(e,t,n,i,r){for(var o,a=[],s=0,l=e.length,u=null!=t;s<l;s++)(o=e[s])&&(n&&!n(o,i,r)||(a.push(o),u&&t.push(s)));return a}function ge(e,t,n,i,r,o){return i&&!i[v]&&(i=ge(i)),r&&!r[v]&&(r=ge(r,o)),te(function(o,a,s,l){var u,f,d,h,p=[],m=[],v=a.length,y=o||function(e,t,n){for(var i=0,r=t.length;i<r;i++)Z(e,t[i],n);return n}(t||"*",s.nodeType?[s]:s,[]),b=!e||!o&&t?y:pe(y,p,e,s,l);if(n?n(b,h=r||(o?e:v||i)?[]:a,s,l):h=b,i)for(u=pe(h,m),i(u,[],s,l),f=u.length;f--;)(d=u[f])&&(h[m[f]]=!(b[m[f]]=d));if(o){if(r||e){if(r){for(u=[],f=h.length;f--;)(d=h[f])&&u.push(b[f]=d);r(null,h=[],u,l)}for(f=h.length;f--;)(d=h[f])&&(u=r?c.call(o,d):p[f])>-1&&(o[u]=!(a[u]=d))}}else h=pe(h===a?h.splice(v,h.length):h),r?r(null,a,h,l):g.apply(a,h)})}function me(e){for(var i,r,o,a=e.length,s=t.relative[e[0].type],l=s||t.relative[" "],u=s?1:0,f=de(function(e){return e===i},l,!0),d=de(function(e){return c.call(i,e)>-1},l,!0),h=[function(e,t,r){var o=!s&&(r||t!=n)||((i=t).nodeType?f(e,t,r):d(e,t,r));return i=null,o}];u<a;u++)if(r=t.relative[e[u].type])h=[de(he(h),r)];else{if((r=t.filter[e[u].type].apply(null,e[u].matches))[v]){for(o=++u;o<a&&!t.relative[e[o].type];o++);return ge(u>1&&he(h),u>1&&fe(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(O,"$1"),r,u<o&&me(e.slice(u,o)),o<a&&me(e=e.slice(o)),o<a&&fe(e))}h.push(r)}return he(h)}function ve(e,i){var r,o=[],a=[],s=x[e+" "];if(!s){for(i||(i=ce(e)),r=i.length;r--;)(s=me(i[r]))[v]?o.push(s):a.push(s);s=x(e,function(e,i){var r=i.length>0,o=e.length>0,a=function(a,s,u,c,d){var h,p,m,v=0,b="0",_=a&&[],w=[],x=n,E=a||o&&t.find.TAG("*",d),T=y+=null==x?1:Math.random()||.1,S=E.length;for(d&&(n=s==l||s||d);b!==S&&null!=(h=E[b]);b++){if(o&&h){for(p=0,s||h.ownerDocument==l||(le(h),u=!f);m=e[p++];)if(m(h,s||l,u)){g.call(c,h);break}d&&(y=T)}r&&((h=!m&&h)&&v--,a&&_.push(h))}if(v+=b,r&&b!==v){for(p=0;m=i[p++];)m(_,w,s,u);if(a){if(v>0)for(;b--;)_[b]||w[b]||(w[b]=D.call(c));w=pe(w)}g.apply(c,w),d&&!a&&w.length>0&&v+i.length>1&&C.uniqueSort(c)}return d&&(y=T,n=x),_};return r?te(a):a}(a,o)),s.selector=e}return s}function ye(e,n,i,r){var o,a,s,l,u,c="function"==typeof e&&e,d=!r&&ce(e=c.selector||e);if(i=i||[],1===d.length){if((a=d[0]=d[0].slice(0)).length>2&&"ID"===(s=a[0]).type&&9===n.nodeType&&f&&t.relative[a[1].type]){if(!(n=(t.find.ID(s.matches[0].replace(Y,K),n)||[])[0]))return i;c&&(n=n.parentNode),e=e.slice(a.shift().value.length)}for(o=$.needsContext.test(e)?0:a.length;o--&&(s=a[o],!t.relative[l=s.type]);)if((u=t.find[l])&&(r=u(s.matches[0].replace(Y,K),X.test(a[0].type)&&se(n.parentNode)||n))){if(a.splice(o,1),!(e=r.length&&fe(a)))return g.apply(i,r),i;break}}return(c||ve(e,d))(r,n,!f,i,!n||X.test(e)&&se(n.parentNode)||n),i}ue.prototype=t.filters=t.pseudos,t.setFilters=new ue,m.sortStable=v.split("").sort(T).join("")===v,le(),m.sortDetached=ne(function(e){return 1&e.compareDocumentPosition(l.createElement("fieldset"))}),C.find=Z,C.expr[":"]=C.expr.pseudos,C.unique=C.uniqueSort,Z.compile=ve,Z.select=ye,Z.setDocument=le,Z.escape=C.escapeSelector,Z.getText=C.text,Z.isXML=C.isXMLDoc,Z.selectors=C.expr,Z.support=C.support,Z.uniqueSort=C.uniqueSort}();var H=function(e,t,n){for(var i=[],r=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(r&&C(e).is(n))break;i.push(e)}return i},R=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},F=C.expr.match.needsContext,M=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function B(e,t,n){return v(t)?C.grep(e,function(e,i){return!!t.call(e,i,e)!==n}):t.nodeType?C.grep(e,function(e){return e===t!==n}):"string"!=typeof t?C.grep(e,function(e){return c.call(t,e)>-1!==n}):C.filter(t,e,n)}C.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?C.find.matchesSelector(i,e)?[i]:[]:C.find.matches(e,C.grep(t,function(e){return 1===e.nodeType}))},C.fn.extend({find:function(e){var t,n,i=this.length,r=this;if("string"!=typeof e)return this.pushStack(C(e).filter(function(){for(t=0;t<i;t++)if(C.contains(r[t],this))return!0}));for(n=this.pushStack([]),t=0;t<i;t++)C.find(e,r[t],n);return i>1?C.uniqueSort(n):n},filter:function(e){return this.pushStack(B(this,e||[],!1))},not:function(e){return this.pushStack(B(this,e||[],!0))},is:function(e){return!!B(this,"string"==typeof e&&F.test(e)?C(e):e||[],!1).length}});var W,U=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(C.fn.init=function(e,t,n){var i,r;if(!e)return this;if(n=n||W,"string"==typeof e){if(!(i="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:U.exec(e))||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(t=t instanceof C?t[0]:t,C.merge(this,C.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:b,!0)),M.test(i[1])&&C.isPlainObject(t))for(i in t)v(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(r=b.getElementById(i[2]))&&(this[0]=r,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):v(e)?void 0!==n.ready?n.ready(e):e(C):C.makeArray(e,this)}).prototype=C.fn,W=C(b);var $=/^(?:parents|prev(?:Until|All))/,Q={children:!0,contents:!0,next:!0,prev:!0};function V(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}C.fn.extend({has:function(e){var t=C(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(C.contains(this,t[e]))return!0})},closest:function(e,t){var n,i=0,r=this.length,o=[],a="string"!=typeof e&&C(e);if(!F.test(e))for(;i<r;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&C.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?C.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?c.call(C(e),this[0]):c.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(C.uniqueSort(C.merge(this.get(),C(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),C.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return H(e,"parentNode")},parentsUntil:function(e,t,n){return H(e,"parentNode",n)},next:function(e){return V(e,"nextSibling")},prev:function(e){return V(e,"previousSibling")},nextAll:function(e){return H(e,"nextSibling")},prevAll:function(e){return H(e,"previousSibling")},nextUntil:function(e,t,n){return H(e,"nextSibling",n)},prevUntil:function(e,t,n){return H(e,"previousSibling",n)},siblings:function(e){return R((e.parentNode||{}).firstChild,e)},children:function(e){return R(e.firstChild)},contents:function(e){return null!=e.contentDocument&&a(e.contentDocument)?e.contentDocument:(k(e,"template")&&(e=e.content||e),C.merge([],e.childNodes))}},function(e,t){C.fn[e]=function(n,i){var r=C.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=C.filter(i,r)),this.length>1&&(Q[e]||C.uniqueSort(r),$.test(e)&&r.reverse()),this.pushStack(r)}});var z=/[^\x20\t\r\n\f]+/g;function X(e){return e}function Y(e){throw e}function K(e,t,n,i){var r;try{e&&v(r=e.promise)?r.call(e).done(t).fail(n):e&&v(r=e.then)?r.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}C.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return C.each(e.match(z)||[],function(e,n){t[n]=!0}),t}(e):C.extend({},e);var t,n,i,r,o=[],a=[],s=-1,l=function(){for(r=r||e.once,i=t=!0;a.length;s=-1)for(n=a.shift();++s<o.length;)!1===o[s].apply(n[0],n[1])&&e.stopOnFalse&&(s=o.length,n=!1);e.memory||(n=!1),t=!1,r&&(o=n?[]:"")},u={add:function(){return o&&(n&&!t&&(s=o.length-1,a.push(n)),function t(n){C.each(n,function(n,i){v(i)?e.unique&&u.has(i)||o.push(i):i&&i.length&&"string"!==x(i)&&t(i)})}(arguments),n&&!t&&l()),this},remove:function(){return C.each(arguments,function(e,t){for(var n;(n=C.inArray(t,o,n))>-1;)o.splice(n,1),n<=s&&s--}),this},has:function(e){return e?C.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return r=a=[],o=n="",this},disabled:function(){return!o},lock:function(){return r=a=[],n||t||(o=n=""),this},locked:function(){return!!r},fireWith:function(e,n){return r||(n=[e,(n=n||[]).slice?n.slice():n],a.push(n),t||l()),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!i}};return u},C.extend({Deferred:function(e){var t=[["notify","progress",C.Callbacks("memory"),C.Callbacks("memory"),2],["resolve","done",C.Callbacks("once memory"),C.Callbacks("once memory"),0,"resolved"],["reject","fail",C.Callbacks("once memory"),C.Callbacks("once memory"),1,"rejected"]],n="pending",r={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return r.then(null,e)},pipe:function(){var e=arguments;return C.Deferred(function(n){C.each(t,function(t,i){var r=v(e[i[4]])&&e[i[4]];o[i[1]](function(){var e=r&&r.apply(this,arguments);e&&v(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[i[0]+"With"](this,r?[e]:arguments)})}),e=null}).promise()},then:function(e,n,r){var o=0;function a(e,t,n,r){return function(){var s=this,l=arguments,u=function(){var i,u;if(!(e<o)){if((i=n.apply(s,l))===t.promise())throw new TypeError("Thenable self-resolution");u=i&&("object"==typeof i||"function"==typeof i)&&i.then,v(u)?r?u.call(i,a(o,t,X,r),a(o,t,Y,r)):(o++,u.call(i,a(o,t,X,r),a(o,t,Y,r),a(o,t,X,t.notifyWith))):(n!==X&&(s=void 0,l=[i]),(r||t.resolveWith)(s,l))}},c=r?u:function(){try{u()}catch(i){C.Deferred.exceptionHook&&C.Deferred.exceptionHook(i,c.error),e+1>=o&&(n!==Y&&(s=void 0,l=[i]),t.rejectWith(s,l))}};e?c():(C.Deferred.getErrorHook?c.error=C.Deferred.getErrorHook():C.Deferred.getStackHook&&(c.error=C.Deferred.getStackHook()),i.setTimeout(c))}}return C.Deferred(function(i){t[0][3].add(a(0,i,v(r)?r:X,i.notifyWith)),t[1][3].add(a(0,i,v(e)?e:X)),t[2][3].add(a(0,i,v(n)?n:Y))}).promise()},promise:function(e){return null!=e?C.extend(e,r):r}},o={};return C.each(t,function(e,i){var a=i[2],s=i[5];r[i[1]]=a.add,s&&a.add(function(){n=s},t[3-e][2].disable,t[3-e][3].disable,t[0][2].lock,t[0][3].lock),a.add(i[3].fire),o[i[0]]=function(){return o[i[0]+"With"](this===o?void 0:this,arguments),this},o[i[0]+"With"]=a.fireWith}),r.promise(o),e&&e.call(o,o),o},when:function(e){var t=arguments.length,n=t,i=Array(n),r=s.call(arguments),o=C.Deferred(),a=function(e){return function(n){i[e]=this,r[e]=arguments.length>1?s.call(arguments):n,--t||o.resolveWith(i,r)}};if(t<=1&&(K(e,o.done(a(n)).resolve,o.reject,!t),"pending"===o.state()||v(r[n]&&r[n].then)))return o.then();for(;n--;)K(r[n],a(n),o.reject);return o.promise()}});var G=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;C.Deferred.exceptionHook=function(e,t){i.console&&i.console.warn&&e&&G.test(e.name)&&i.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},C.readyException=function(e){i.setTimeout(function(){throw e})};var J=C.Deferred();function Z(){b.removeEventListener("DOMContentLoaded",Z),i.removeEventListener("load",Z),C.ready()}C.fn.ready=function(e){return J.then(e).catch(function(e){C.readyException(e)}),this},C.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--C.readyWait:C.isReady)||(C.isReady=!0,!0!==e&&--C.readyWait>0||J.resolveWith(b,[C]))}}),C.ready.then=J.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?i.setTimeout(C.ready):(b.addEventListener("DOMContentLoaded",Z),i.addEventListener("load",Z));var ee=function(e,t,n,i,r,o,a){var s=0,l=e.length,u=null==n;if("object"===x(n))for(s in r=!0,n)ee(e,t,s,n[s],!0,o,a);else if(void 0!==i&&(r=!0,v(i)||(a=!0),u&&(a?(t.call(e,i),t=null):(u=t,t=function(e,t,n){return u.call(C(e),n)})),t))for(;s<l;s++)t(e[s],n,a?i:i.call(e[s],s,t(e[s],n)));return r?e:u?t.call(e):l?t(e[0],n):o},te=/^-ms-/,ne=/-([a-z])/g;function ie(e,t){return t.toUpperCase()}function re(e){return e.replace(te,"ms-").replace(ne,ie)}var oe=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function ae(){this.expando=C.expando+ae.uid++}ae.uid=1,ae.prototype={cache:function(e){var t=e[this.expando];return t||(t={},oe(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,r=this.cache(e);if("string"==typeof t)r[re(t)]=n;else for(i in t)r[re(i)]=t[i];return r},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][re(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(re):(t=re(t))in i?[t]:t.match(z)||[]).length;for(;n--;)delete i[t[n]]}(void 0===t||C.isEmptyObject(i))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!C.isEmptyObject(t)}};var se=new ae,le=new ae,ue=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ce=/[A-Z]/g;function fe(e,t,n){var i;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(ce,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n=function(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:ue.test(e)?JSON.parse(e):e)}(n)}catch(e){}le.set(e,t,n)}else n=void 0;return n}C.extend({hasData:function(e){return le.hasData(e)||se.hasData(e)},data:function(e,t,n){return le.access(e,t,n)},removeData:function(e,t){le.remove(e,t)},_data:function(e,t,n){return se.access(e,t,n)},_removeData:function(e,t){se.remove(e,t)}}),C.fn.extend({data:function(e,t){var n,i,r,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(r=le.get(o),1===o.nodeType&&!se.get(o,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&0===(i=a[n].name).indexOf("data-")&&(i=re(i.slice(5)),fe(o,i,r[i]));se.set(o,"hasDataAttrs",!0)}return r}return"object"==typeof e?this.each(function(){le.set(this,e)}):ee(this,function(t){var n;if(o&&void 0===t)return void 0!==(n=le.get(o,e))||void 0!==(n=fe(o,e))?n:void 0;this.each(function(){le.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){le.remove(this,e)})}}),C.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=se.get(e,t),n&&(!i||Array.isArray(n)?i=se.access(e,t,C.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=C.queue(e,t),i=n.length,r=n.shift(),o=C._queueHooks(e,t);"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,function(){C.dequeue(e,t)},o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return se.get(e,n)||se.access(e,n,{empty:C.Callbacks("once memory").add(function(){se.remove(e,[t+"queue",n])})})}}),C.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?C.queue(this[0],e):void 0===t?this:this.each(function(){var n=C.queue(this,e,t);C._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&C.dequeue(this,e)})},dequeue:function(e){return this.each(function(){C.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=C.Deferred(),o=this,a=this.length,s=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=se.get(o[a],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(s));return s(),r.promise(t)}});var de=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,he=new RegExp("^(?:([+-])=|)("+de+")([a-z%]*)$","i"),pe=["Top","Right","Bottom","Left"],ge=b.documentElement,me=function(e){return C.contains(e.ownerDocument,e)},ve={composed:!0};ge.getRootNode&&(me=function(e){return C.contains(e.ownerDocument,e)||e.getRootNode(ve)===e.ownerDocument});var ye=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&me(e)&&"none"===C.css(e,"display")};function be(e,t,n,i){var r,o,a=20,s=i?function(){return i.cur()}:function(){return C.css(e,t,"")},l=s(),u=n&&n[3]||(C.cssNumber[t]?"":"px"),c=e.nodeType&&(C.cssNumber[t]||"px"!==u&&+l)&&he.exec(C.css(e,t));if(c&&c[3]!==u){for(l/=2,u=u||c[3],c=+l||1;a--;)C.style(e,t,c+u),(1-o)*(1-(o=s()/l||.5))<=0&&(a=0),c/=o;c*=2,C.style(e,t,c+u),n=n||[]}return n&&(c=+c||+l||0,r=n[1]?c+(n[1]+1)*n[2]:+n[2],i&&(i.unit=u,i.start=c,i.end=r)),r}var _e={};function we(e){var t,n=e.ownerDocument,i=e.nodeName,r=_e[i];return r||(t=n.body.appendChild(n.createElement(i)),r=C.css(t,"display"),t.parentNode.removeChild(t),"none"===r&&(r="block"),_e[i]=r,r)}function xe(e,t){for(var n,i,r=[],o=0,a=e.length;o<a;o++)(i=e[o]).style&&(n=i.style.display,t?("none"===n&&(r[o]=se.get(i,"display")||null,r[o]||(i.style.display="")),""===i.style.display&&ye(i)&&(r[o]=we(i))):"none"!==n&&(r[o]="none",se.set(i,"display",n)));for(o=0;o<a;o++)null!=r[o]&&(e[o].style.display=r[o]);return e}C.fn.extend({show:function(){return xe(this,!0)},hide:function(){return xe(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ye(this)?C(this).show():C(this).hide()})}});var Ee,Te,Ce=/^(?:checkbox|radio)$/i,Se=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ke=/^$|^module$|\/(?:java|ecma)script/i;Ee=b.createDocumentFragment().appendChild(b.createElement("div")),(Te=b.createElement("input")).setAttribute("type","radio"),Te.setAttribute("checked","checked"),Te.setAttribute("name","t"),Ee.appendChild(Te),m.checkClone=Ee.cloneNode(!0).cloneNode(!0).lastChild.checked,Ee.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!Ee.cloneNode(!0).lastChild.defaultValue,Ee.innerHTML="<option></option>",m.option=!!Ee.lastChild;var De={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Ae(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&k(e,t)?C.merge([e],n):n}function Ne(e,t){for(var n=0,i=e.length;n<i;n++)se.set(e[n],"globalEval",!t||se.get(t[n],"globalEval"))}De.tbody=De.tfoot=De.colgroup=De.caption=De.thead,De.th=De.td,m.option||(De.optgroup=De.option=[1,"<select multiple='multiple'>","</select>"]);var je=/<|&#?\w+;/;function Oe(e,t,n,i,r){for(var o,a,s,l,u,c,f=t.createDocumentFragment(),d=[],h=0,p=e.length;h<p;h++)if((o=e[h])||0===o)if("object"===x(o))C.merge(d,o.nodeType?[o]:o);else if(je.test(o)){for(a=a||f.appendChild(t.createElement("div")),s=(Se.exec(o)||["",""])[1].toLowerCase(),l=De[s]||De._default,a.innerHTML=l[1]+C.htmlPrefilter(o)+l[2],c=l[0];c--;)a=a.lastChild;C.merge(d,a.childNodes),(a=f.firstChild).textContent=""}else d.push(t.createTextNode(o));for(f.textContent="",h=0;o=d[h++];)if(i&&C.inArray(o,i)>-1)r&&r.push(o);else if(u=me(o),a=Ae(f.appendChild(o),"script"),u&&Ne(a),n)for(c=0;o=a[c++];)ke.test(o.type||"")&&n.push(o);return f}var Ie=/^([^.]*)(?:\.(.+)|)/;function Le(){return!0}function Pe(){return!1}function qe(e,t,n,i,r,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(i=i||n,n=void 0),t)qe(e,s,n,i,t[s],o);return e}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),!1===r)r=Pe;else if(!r)return e;return 1===o&&(a=r,r=function(e){return C().off(e),a.apply(this,arguments)},r.guid=a.guid||(a.guid=C.guid++)),e.each(function(){C.event.add(this,t,r,i,n)})}function He(e,t,n){n?(se.set(e,t,!1),C.event.add(e,t,{namespace:!1,handler:function(e){var n,i=se.get(this,t);if(1&e.isTrigger&&this[t]){if(i)(C.event.special[t]||{}).delegateType&&e.stopPropagation();else if(i=s.call(arguments),se.set(this,t,i),this[t](),n=se.get(this,t),se.set(this,t,!1),i!==n)return e.stopImmediatePropagation(),e.preventDefault(),n}else i&&(se.set(this,t,C.event.trigger(i[0],i.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=Le)}})):void 0===se.get(e,t)&&C.event.add(e,t,Le)}C.event={global:{},add:function(e,t,n,i,r){var o,a,s,l,u,c,f,d,h,p,g,m=se.get(e);if(oe(e))for(n.handler&&(n=(o=n).handler,r=o.selector),r&&C.find.matchesSelector(ge,r),n.guid||(n.guid=C.guid++),(l=m.events)||(l=m.events=Object.create(null)),(a=m.handle)||(a=m.handle=function(t){return void 0!==C&&C.event.triggered!==t.type?C.event.dispatch.apply(e,arguments):void 0}),u=(t=(t||"").match(z)||[""]).length;u--;)h=g=(s=Ie.exec(t[u])||[])[1],p=(s[2]||"").split(".").sort(),h&&(f=C.event.special[h]||{},h=(r?f.delegateType:f.bindType)||h,f=C.event.special[h]||{},c=C.extend({type:h,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&C.expr.match.needsContext.test(r),namespace:p.join(".")},o),(d=l[h])||((d=l[h]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(e,i,p,a)||e.addEventListener&&e.addEventListener(h,a)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),r?d.splice(d.delegateCount++,0,c):d.push(c),C.event.global[h]=!0)},remove:function(e,t,n,i,r){var o,a,s,l,u,c,f,d,h,p,g,m=se.hasData(e)&&se.get(e);if(m&&(l=m.events)){for(u=(t=(t||"").match(z)||[""]).length;u--;)if(h=g=(s=Ie.exec(t[u])||[])[1],p=(s[2]||"").split(".").sort(),h){for(f=C.event.special[h]||{},d=l[h=(i?f.delegateType:f.bindType)||h]||[],s=s[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=d.length;o--;)c=d[o],!r&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||i&&i!==c.selector&&("**"!==i||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,f.remove&&f.remove.call(e,c));a&&!d.length&&(f.teardown&&!1!==f.teardown.call(e,p,m.handle)||C.removeEvent(e,h,m.handle),delete l[h])}else for(h in l)C.event.remove(e,h+t[u],n,i,!0);C.isEmptyObject(l)&&se.remove(e,"handle events")}},dispatch:function(e){var t,n,i,r,o,a,s=new Array(arguments.length),l=C.event.fix(e),u=(se.get(this,"events")||Object.create(null))[l.type]||[],c=C.event.special[l.type]||{};for(s[0]=l,t=1;t<arguments.length;t++)s[t]=arguments[t];if(l.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,l)){for(a=C.event.handlers.call(this,l,u),t=0;(r=a[t++])&&!l.isPropagationStopped();)for(l.currentTarget=r.elem,n=0;(o=r.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(i=((C.event.special[o.origType]||{}).handle||o.handler).apply(r.elem,s))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,l),l.result}},handlers:function(e,t){var n,i,r,o,a,s=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!("click"===e.type&&e.button>=1))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==e.type||!0!==u.disabled)){for(o=[],a={},n=0;n<l;n++)void 0===a[r=(i=t[n]).selector+" "]&&(a[r]=i.needsContext?C(r,this).index(u)>-1:C.find(r,this,null,[u]).length),a[r]&&o.push(i);o.length&&s.push({elem:u,handlers:o})}return u=this,l<t.length&&s.push({elem:u,handlers:t.slice(l)}),s},addProp:function(e,t){Object.defineProperty(C.Event.prototype,e,{enumerable:!0,configurable:!0,get:v(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[C.expando]?e:new C.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return Ce.test(t.type)&&t.click&&k(t,"input")&&He(t,"click",!0),!1},trigger:function(e){var t=this||e;return Ce.test(t.type)&&t.click&&k(t,"input")&&He(t,"click"),!0},_default:function(e){var t=e.target;return Ce.test(t.type)&&t.click&&k(t,"input")&&se.get(t,"click")||k(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},C.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},C.Event=function(e,t){if(!(this instanceof C.Event))return new C.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Le:Pe,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&C.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[C.expando]=!0},C.Event.prototype={constructor:C.Event,isDefaultPrevented:Pe,isPropagationStopped:Pe,isImmediatePropagationStopped:Pe,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Le,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Le,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Le,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},C.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},C.event.addProp),C.each({focus:"focusin",blur:"focusout"},function(e,t){function n(e){if(b.documentMode){var n=se.get(this,"handle"),i=C.event.fix(e);i.type="focusin"===e.type?"focus":"blur",i.isSimulated=!0,n(e),i.target===i.currentTarget&&n(i)}else C.event.simulate(t,e.target,C.event.fix(e))}C.event.special[e]={setup:function(){var i;if(He(this,e,!0),!b.documentMode)return!1;(i=se.get(this,t))||this.addEventListener(t,n),se.set(this,t,(i||0)+1)},trigger:function(){return He(this,e),!0},teardown:function(){var e;if(!b.documentMode)return!1;(e=se.get(this,t)-1)?se.set(this,t,e):(this.removeEventListener(t,n),se.remove(this,t))},_default:function(t){return se.get(t.target,e)},delegateType:t},C.event.special[t]={setup:function(){var i=this.ownerDocument||this.document||this,r=b.documentMode?this:i,o=se.get(r,t);o||(b.documentMode?this.addEventListener(t,n):i.addEventListener(e,n,!0)),se.set(r,t,(o||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,r=b.documentMode?this:i,o=se.get(r,t)-1;o?se.set(r,t,o):(b.documentMode?this.removeEventListener(t,n):i.removeEventListener(e,n,!0),se.remove(r,t))}}}),C.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){C.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=e.relatedTarget,r=e.handleObj;return i&&(i===this||C.contains(this,i))||(e.type=r.origType,n=r.handler.apply(this,arguments),e.type=t),n}}}),C.fn.extend({on:function(e,t,n,i){return qe(this,e,t,n,i)},one:function(e,t,n,i){return qe(this,e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,C(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(r in e)this.off(r,t,e[r]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Pe),this.each(function(){C.event.remove(this,e,n,t)})}});var Re=/<script|<style|<link/i,Fe=/checked\s*(?:[^=]|=\s*.checked.)/i,Me=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Be(e,t){return k(e,"table")&&k(11!==t.nodeType?t:t.firstChild,"tr")&&C(e).children("tbody")[0]||e}function We(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Ue(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function $e(e,t){var n,i,r,o,a,s;if(1===t.nodeType){if(se.hasData(e)&&(s=se.get(e).events))for(r in se.remove(t,"handle events"),s)for(n=0,i=s[r].length;n<i;n++)C.event.add(t,r,s[r][n]);le.hasData(e)&&(o=le.access(e),a=C.extend({},o),le.set(t,a))}}function Qe(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Ce.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function Ve(e,t,n,i){t=l(t);var r,o,a,s,u,c,f=0,d=e.length,h=d-1,p=t[0],g=v(p);if(g||d>1&&"string"==typeof p&&!m.checkClone&&Fe.test(p))return e.each(function(r){var o=e.eq(r);g&&(t[0]=p.call(this,r,o.html())),Ve(o,t,n,i)});if(d&&(o=(r=Oe(t,e[0].ownerDocument,!1,e,i)).firstChild,1===r.childNodes.length&&(r=o),o||i)){for(s=(a=C.map(Ae(r,"script"),We)).length;f<d;f++)u=r,f!==h&&(u=C.clone(u,!0,!0),s&&C.merge(a,Ae(u,"script"))),n.call(e[f],u,f);if(s)for(c=a[a.length-1].ownerDocument,C.map(a,Ue),f=0;f<s;f++)u=a[f],ke.test(u.type||"")&&!se.access(u,"globalEval")&&C.contains(c,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?C._evalUrl&&!u.noModule&&C._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},c):w(u.textContent.replace(Me,""),u,c))}return e}function ze(e,t,n){for(var i,r=t?C.filter(t,e):e,o=0;null!=(i=r[o]);o++)n||1!==i.nodeType||C.cleanData(Ae(i)),i.parentNode&&(n&&me(i)&&Ne(Ae(i,"script")),i.parentNode.removeChild(i));return e}C.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,r,o,a,s=e.cloneNode(!0),l=me(e);if(!(m.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||C.isXMLDoc(e)))for(a=Ae(s),i=0,r=(o=Ae(e)).length;i<r;i++)Qe(o[i],a[i]);if(t)if(n)for(o=o||Ae(e),a=a||Ae(s),i=0,r=o.length;i<r;i++)$e(o[i],a[i]);else $e(e,s);return(a=Ae(s,"script")).length>0&&Ne(a,!l&&Ae(e,"script")),s},cleanData:function(e){for(var t,n,i,r=C.event.special,o=0;void 0!==(n=e[o]);o++)if(oe(n)){if(t=n[se.expando]){if(t.events)for(i in t.events)r[i]?C.event.remove(n,i):C.removeEvent(n,i,t.handle);n[se.expando]=void 0}n[le.expando]&&(n[le.expando]=void 0)}}}),C.fn.extend({detach:function(e){return ze(this,e,!0)},remove:function(e){return ze(this,e)},text:function(e){return ee(this,function(e){return void 0===e?C.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Ve(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Be(this,e).appendChild(e)})},prepend:function(){return Ve(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Be(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return Ve(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Ve(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(C.cleanData(Ae(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return C.clone(this,e,t)})},html:function(e){return ee(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Re.test(e)&&!De[(Se.exec(e)||["",""])[1].toLowerCase()]){e=C.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(C.cleanData(Ae(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return Ve(this,arguments,function(t){var n=this.parentNode;C.inArray(this,e)<0&&(C.cleanData(Ae(this)),n&&n.replaceChild(t,this))},e)}}),C.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){C.fn[e]=function(e){for(var n,i=[],r=C(e),o=r.length-1,a=0;a<=o;a++)n=a===o?this:this.clone(!0),C(r[a])[t](n),u.apply(i,n.get());return this.pushStack(i)}});var Xe=new RegExp("^("+de+")(?!px)[a-z%]+$","i"),Ye=/^--/,Ke=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=i),t.getComputedStyle(e)},Ge=function(e,t,n){var i,r,o={};for(r in t)o[r]=e.style[r],e.style[r]=t[r];for(r in i=n.call(e),t)e.style[r]=o[r];return i},Je=new RegExp(pe.join("|"),"i");function Ze(e,t,n){var i,r,o,a,s=Ye.test(t),l=e.style;return(n=n||Ke(e))&&(a=n.getPropertyValue(t)||n[t],s&&a&&(a=a.replace(O,"$1")||void 0),""!==a||me(e)||(a=C.style(e,t)),!m.pixelBoxStyles()&&Xe.test(a)&&Je.test(t)&&(i=l.width,r=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=a,a=n.width,l.width=i,l.minWidth=r,l.maxWidth=o)),void 0!==a?a+"":a}function et(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(c){u.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ge.appendChild(u).appendChild(c);var e=i.getComputedStyle(c);n="1%"!==e.top,l=12===t(e.marginLeft),c.style.right="60%",a=36===t(e.right),r=36===t(e.width),c.style.position="absolute",o=12===t(c.offsetWidth/3),ge.removeChild(u),c=null}}function t(e){return Math.round(parseFloat(e))}var n,r,o,a,s,l,u=b.createElement("div"),c=b.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===c.style.backgroundClip,C.extend(m,{boxSizingReliable:function(){return e(),r},pixelBoxStyles:function(){return e(),a},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,t,n,r;return null==s&&(e=b.createElement("table"),t=b.createElement("tr"),n=b.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",ge.appendChild(e).appendChild(t).appendChild(n),r=i.getComputedStyle(t),s=parseInt(r.height,10)+parseInt(r.borderTopWidth,10)+parseInt(r.borderBottomWidth,10)===t.offsetHeight,ge.removeChild(e)),s}}))}();var tt=["Webkit","Moz","ms"],nt=b.createElement("div").style,it={};function rt(e){var t=C.cssProps[e]||it[e];return t||(e in nt?e:it[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=tt.length;n--;)if((e=tt[n]+t)in nt)return e}(e)||e)}var ot=/^(none|table(?!-c[ea]).+)/,at={position:"absolute",visibility:"hidden",display:"block"},st={letterSpacing:"0",fontWeight:"400"};function lt(e,t,n){var i=he.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function ut(e,t,n,i,r,o){var a="width"===t?1:0,s=0,l=0,u=0;if(n===(i?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(u+=C.css(e,n+pe[a],!0,r)),i?("content"===n&&(l-=C.css(e,"padding"+pe[a],!0,r)),"margin"!==n&&(l-=C.css(e,"border"+pe[a]+"Width",!0,r))):(l+=C.css(e,"padding"+pe[a],!0,r),"padding"!==n?l+=C.css(e,"border"+pe[a]+"Width",!0,r):s+=C.css(e,"border"+pe[a]+"Width",!0,r));return!i&&o>=0&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-s-.5))||0),l+u}function ct(e,t,n){var i=Ke(e),r=(!m.boxSizingReliable()||n)&&"border-box"===C.css(e,"boxSizing",!1,i),o=r,a=Ze(e,t,i),s="offset"+t[0].toUpperCase()+t.slice(1);if(Xe.test(a)){if(!n)return a;a="auto"}return(!m.boxSizingReliable()&&r||!m.reliableTrDimensions()&&k(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===C.css(e,"display",!1,i))&&e.getClientRects().length&&(r="border-box"===C.css(e,"boxSizing",!1,i),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+ut(e,t,n||(r?"border":"content"),o,i,a)+"px"}function ft(e,t,n,i,r){return new ft.prototype.init(e,t,n,i,r)}C.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ze(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,a,s=re(t),l=Ye.test(t),u=e.style;if(l||(t=rt(s)),a=C.cssHooks[t]||C.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(r=a.get(e,!1,i))?r:u[t];"string"===(o=typeof n)&&(r=he.exec(n))&&r[1]&&(n=be(e,t,r),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=r&&r[3]||(C.cssNumber[s]?"":"px")),m.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,i))||(l?u.setProperty(t,n):u[t]=n))}},css:function(e,t,n,i){var r,o,a,s=re(t);return Ye.test(t)||(t=rt(s)),(a=C.cssHooks[t]||C.cssHooks[s])&&"get"in a&&(r=a.get(e,!0,n)),void 0===r&&(r=Ze(e,t,i)),"normal"===r&&t in st&&(r=st[t]),""===n||n?(o=parseFloat(r),!0===n||isFinite(o)?o||0:r):r}}),C.each(["height","width"],function(e,t){C.cssHooks[t]={get:function(e,n,i){if(n)return!ot.test(C.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?ct(e,t,i):Ge(e,at,function(){return ct(e,t,i)})},set:function(e,n,i){var r,o=Ke(e),a=!m.scrollboxSize()&&"absolute"===o.position,s=(a||i)&&"border-box"===C.css(e,"boxSizing",!1,o),l=i?ut(e,t,i,s,o):0;return s&&a&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-ut(e,t,"border",!1,o)-.5)),l&&(r=he.exec(n))&&"px"!==(r[3]||"px")&&(e.style[t]=n,n=C.css(e,t)),lt(0,n,l)}}}),C.cssHooks.marginLeft=et(m.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Ze(e,"marginLeft"))||e.getBoundingClientRect().left-Ge(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),C.each({margin:"",padding:"",border:"Width"},function(e,t){C.cssHooks[e+t]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];i<4;i++)r[e+pe[i]+t]=o[i]||o[i-2]||o[0];return r}},"margin"!==e&&(C.cssHooks[e+t].set=lt)}),C.fn.extend({css:function(e,t){return ee(this,function(e,t,n){var i,r,o={},a=0;if(Array.isArray(t)){for(i=Ke(e),r=t.length;a<r;a++)o[t[a]]=C.css(e,t[a],!1,i);return o}return void 0!==n?C.style(e,t,n):C.css(e,t)},e,t,arguments.length>1)}}),C.Tween=ft,ft.prototype={constructor:ft,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||C.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(C.cssNumber[n]?"":"px")},cur:function(){var e=ft.propHooks[this.prop];return e&&e.get?e.get(this):ft.propHooks._default.get(this)},run:function(e){var t,n=ft.propHooks[this.prop];return this.options.duration?this.pos=t=C.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):ft.propHooks._default.set(this),this}},ft.prototype.init.prototype=ft.prototype,ft.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=C.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){C.fx.step[e.prop]?C.fx.step[e.prop](e):1!==e.elem.nodeType||!C.cssHooks[e.prop]&&null==e.elem.style[rt(e.prop)]?e.elem[e.prop]=e.now:C.style(e.elem,e.prop,e.now+e.unit)}}},ft.propHooks.scrollTop=ft.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},C.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},C.fx=ft.prototype.init,C.fx.step={};var dt,ht,pt=/^(?:toggle|show|hide)$/,gt=/queueHooks$/;function mt(){ht&&(!1===b.hidden&&i.requestAnimationFrame?i.requestAnimationFrame(mt):i.setTimeout(mt,C.fx.interval),C.fx.tick())}function vt(){return i.setTimeout(function(){dt=void 0}),dt=Date.now()}function yt(e,t){var n,i=0,r={height:e};for(t=t?1:0;i<4;i+=2-t)r["margin"+(n=pe[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function bt(e,t,n){for(var i,r=(_t.tweeners[t]||[]).concat(_t.tweeners["*"]),o=0,a=r.length;o<a;o++)if(i=r[o].call(n,t,e))return i}function _t(e,t,n){var i,r,o=0,a=_t.prefilters.length,s=C.Deferred().always(function(){delete l.elem}),l=function(){if(r)return!1;for(var t=dt||vt(),n=Math.max(0,u.startTime+u.duration-t),i=1-(n/u.duration||0),o=0,a=u.tweens.length;o<a;o++)u.tweens[o].run(i);return s.notifyWith(e,[u,i,n]),i<1&&a?n:(a||s.notifyWith(e,[u,1,0]),s.resolveWith(e,[u]),!1)},u=s.promise({elem:e,props:C.extend({},t),opts:C.extend(!0,{specialEasing:{},easing:C.easing._default},n),originalProperties:t,originalOptions:n,startTime:dt||vt(),duration:n.duration,tweens:[],createTween:function(t,n){var i=C.Tween(e,u.opts,t,n,u.opts.specialEasing[t]||u.opts.easing);return u.tweens.push(i),i},stop:function(t){var n=0,i=t?u.tweens.length:0;if(r)return this;for(r=!0;n<i;n++)u.tweens[n].run(1);return t?(s.notifyWith(e,[u,1,0]),s.resolveWith(e,[u,t])):s.rejectWith(e,[u,t]),this}}),c=u.props;for(!function(e,t){var n,i,r,o,a;for(n in e)if(r=t[i=re(n)],o=e[n],Array.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),(a=C.cssHooks[i])&&"expand"in a)for(n in o=a.expand(o),delete e[i],o)n in e||(e[n]=o[n],t[n]=r);else t[i]=r}(c,u.opts.specialEasing);o<a;o++)if(i=_t.prefilters[o].call(u,e,c,u.opts))return v(i.stop)&&(C._queueHooks(u.elem,u.opts.queue).stop=i.stop.bind(i)),i;return C.map(c,bt,u),v(u.opts.start)&&u.opts.start.call(e,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),C.fx.timer(C.extend(l,{elem:e,anim:u,queue:u.opts.queue})),u}C.Animation=C.extend(_t,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return be(n.elem,e,he.exec(t),n),n}]},tweener:function(e,t){v(e)?(t=e,e=["*"]):e=e.match(z);for(var n,i=0,r=e.length;i<r;i++)n=e[i],_t.tweeners[n]=_t.tweeners[n]||[],_t.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,r,o,a,s,l,u,c,f="width"in t||"height"in t,d=this,h={},p=e.style,g=e.nodeType&&ye(e),m=se.get(e,"fxshow");for(i in n.queue||(null==(a=C._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,d.always(function(){d.always(function(){a.unqueued--,C.queue(e,"fx").length||a.empty.fire()})})),t)if(r=t[i],pt.test(r)){if(delete t[i],o=o||"toggle"===r,r===(g?"hide":"show")){if("show"!==r||!m||void 0===m[i])continue;g=!0}h[i]=m&&m[i]||C.style(e,i)}if((l=!C.isEmptyObject(t))||!C.isEmptyObject(h))for(i in f&&1===e.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(u=m&&m.display)&&(u=se.get(e,"display")),"none"===(c=C.css(e,"display"))&&(u?c=u:(xe([e],!0),u=e.style.display||u,c=C.css(e,"display"),xe([e]))),("inline"===c||"inline-block"===c&&null!=u)&&"none"===C.css(e,"float")&&(l||(d.done(function(){p.display=u}),null==u&&(c=p.display,u="none"===c?"":c)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",d.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),l=!1,h)l||(m?"hidden"in m&&(g=m.hidden):m=se.access(e,"fxshow",{display:u}),o&&(m.hidden=!g),g&&xe([e],!0),d.done(function(){for(i in g||xe([e]),se.remove(e,"fxshow"),h)C.style(e,i,h[i])})),l=bt(g?m[i]:0,i,d),i in m||(m[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?_t.prefilters.unshift(e):_t.prefilters.push(e)}}),C.speed=function(e,t,n){var i=e&&"object"==typeof e?C.extend({},e):{complete:n||!n&&t||v(e)&&e,duration:e,easing:n&&t||t&&!v(t)&&t};return C.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in C.fx.speeds?i.duration=C.fx.speeds[i.duration]:i.duration=C.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){v(i.old)&&i.old.call(this),i.queue&&C.dequeue(this,i.queue)},i},C.fn.extend({fadeTo:function(e,t,n,i){return this.filter(ye).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var r=C.isEmptyObject(e),o=C.speed(t,n,i),a=function(){var t=_t(this,C.extend({},e),o);(r||se.get(this,"finish"))&&t.stop(!0)};return a.finish=a,r||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each(function(){var t=!0,r=null!=e&&e+"queueHooks",o=C.timers,a=se.get(this);if(r)a[r]&&a[r].stop&&i(a[r]);else for(r in a)a[r]&&a[r].stop&&gt.test(r)&&i(a[r]);for(r=o.length;r--;)o[r].elem!==this||null!=e&&o[r].queue!==e||(o[r].anim.stop(n),t=!1,o.splice(r,1));!t&&n||C.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=se.get(this),i=n[e+"queue"],r=n[e+"queueHooks"],o=C.timers,a=i?i.length:0;for(n.finish=!0,C.queue(this,e,[]),r&&r.stop&&r.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<a;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),C.each(["toggle","show","hide"],function(e,t){var n=C.fn[t];C.fn[t]=function(e,i,r){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(yt(t,!0),e,i,r)}}),C.each({slideDown:yt("show"),slideUp:yt("hide"),slideToggle:yt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){C.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}}),C.timers=[],C.fx.tick=function(){var e,t=0,n=C.timers;for(dt=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||C.fx.stop(),dt=void 0},C.fx.timer=function(e){C.timers.push(e),C.fx.start()},C.fx.interval=13,C.fx.start=function(){ht||(ht=!0,mt())},C.fx.stop=function(){ht=null},C.fx.speeds={slow:600,fast:200,_default:400},C.fn.delay=function(e,t){return e=C.fx&&C.fx.speeds[e]||e,t=t||"fx",this.queue(t,function(t,n){var r=i.setTimeout(t,e);n.stop=function(){i.clearTimeout(r)}})},function(){var e=b.createElement("input"),t=b.createElement("select").appendChild(b.createElement("option"));e.type="checkbox",m.checkOn=""!==e.value,m.optSelected=t.selected,(e=b.createElement("input")).value="t",e.type="radio",m.radioValue="t"===e.value}();var wt,xt=C.expr.attrHandle;C.fn.extend({attr:function(e,t){return ee(this,C.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){C.removeAttr(this,e)})}}),C.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?C.prop(e,t,n):(1===o&&C.isXMLDoc(e)||(r=C.attrHooks[t.toLowerCase()]||(C.expr.match.bool.test(t)?wt:void 0)),void 0!==n?null===n?void C.removeAttr(e,t):r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):r&&"get"in r&&null!==(i=r.get(e,t))?i:null==(i=C.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!m.radioValue&&"radio"===t&&k(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,r=t&&t.match(z);if(r&&1===e.nodeType)for(;n=r[i++];)e.removeAttribute(n)}}),wt={set:function(e,t,n){return!1===t?C.removeAttr(e,n):e.setAttribute(n,n),n}},C.each(C.expr.match.bool.source.match(/\w+/g),function(e,t){var n=xt[t]||C.find.attr;xt[t]=function(e,t,i){var r,o,a=t.toLowerCase();return i||(o=xt[a],xt[a]=r,r=null!=n(e,t,i)?a:null,xt[a]=o),r}});var Et=/^(?:input|select|textarea|button)$/i,Tt=/^(?:a|area)$/i;function Ct(e){return(e.match(z)||[]).join(" ")}function St(e){return e.getAttribute&&e.getAttribute("class")||""}function kt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(z)||[]}C.fn.extend({prop:function(e,t){return ee(this,C.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[C.propFix[e]||e]})}}),C.extend({prop:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&C.isXMLDoc(e)||(t=C.propFix[t]||t,r=C.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=C.find.attr(e,"tabindex");return t?parseInt(t,10):Et.test(e.nodeName)||Tt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(C.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),C.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){C.propFix[this.toLowerCase()]=this}),C.fn.extend({addClass:function(e){var t,n,i,r,o,a;return v(e)?this.each(function(t){C(this).addClass(e.call(this,t,St(this)))}):(t=kt(e)).length?this.each(function(){if(i=St(this),n=1===this.nodeType&&" "+Ct(i)+" "){for(o=0;o<t.length;o++)r=t[o],n.indexOf(" "+r+" ")<0&&(n+=r+" ");a=Ct(n),i!==a&&this.setAttribute("class",a)}}):this},removeClass:function(e){var t,n,i,r,o,a;return v(e)?this.each(function(t){C(this).removeClass(e.call(this,t,St(this)))}):arguments.length?(t=kt(e)).length?this.each(function(){if(i=St(this),n=1===this.nodeType&&" "+Ct(i)+" "){for(o=0;o<t.length;o++)for(r=t[o];n.indexOf(" "+r+" ")>-1;)n=n.replace(" "+r+" "," ");a=Ct(n),i!==a&&this.setAttribute("class",a)}}):this:this.attr("class","")},toggleClass:function(e,t){var n,i,r,o,a=typeof e,s="string"===a||Array.isArray(e);return v(e)?this.each(function(n){C(this).toggleClass(e.call(this,n,St(this),t),t)}):"boolean"==typeof t&&s?t?this.addClass(e):this.removeClass(e):(n=kt(e),this.each(function(){if(s)for(o=C(this),r=0;r<n.length;r++)i=n[r],o.hasClass(i)?o.removeClass(i):o.addClass(i);else void 0!==e&&"boolean"!==a||((i=St(this))&&se.set(this,"__className__",i),this.setAttribute&&this.setAttribute("class",i||!1===e?"":se.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&(" "+Ct(St(n))+" ").indexOf(t)>-1)return!0;return!1}});var Dt=/\r/g;C.fn.extend({val:function(e){var t,n,i,r=this[0];return arguments.length?(i=v(e),this.each(function(n){var r;1===this.nodeType&&(null==(r=i?e.call(this,n,C(this).val()):e)?r="":"number"==typeof r?r+="":Array.isArray(r)&&(r=C.map(r,function(e){return null==e?"":e+""})),(t=C.valHooks[this.type]||C.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,r,"value")||(this.value=r))})):r?(t=C.valHooks[r.type]||C.valHooks[r.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(r,"value"))?n:"string"==typeof(n=r.value)?n.replace(Dt,""):null==n?"":n:void 0}}),C.extend({valHooks:{option:{get:function(e){var t=C.find.attr(e,"value");return null!=t?t:Ct(C.text(e))}},select:{get:function(e){var t,n,i,r=e.options,o=e.selectedIndex,a="select-one"===e.type,s=a?null:[],l=a?o+1:r.length;for(i=o<0?l:a?o:0;i<l;i++)if(((n=r[i]).selected||i===o)&&!n.disabled&&(!n.parentNode.disabled||!k(n.parentNode,"optgroup"))){if(t=C(n).val(),a)return t;s.push(t)}return s},set:function(e,t){for(var n,i,r=e.options,o=C.makeArray(t),a=r.length;a--;)((i=r[a]).selected=C.inArray(C.valHooks.option.get(i),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),C.each(["radio","checkbox"],function(){C.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=C.inArray(C(e).val(),t)>-1}},m.checkOn||(C.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var At=i.location,Nt={guid:Date.now()},jt=/\?/;C.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new i.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||C.error("Invalid XML: "+(n?C.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t};var Ot=/^(?:focusinfocus|focusoutblur)$/,It=function(e){e.stopPropagation()};C.extend(C.event,{trigger:function(e,t,n,r){var o,a,s,l,u,c,f,d,p=[n||b],g=h.call(e,"type")?e.type:e,m=h.call(e,"namespace")?e.namespace.split("."):[];if(a=d=s=n=n||b,3!==n.nodeType&&8!==n.nodeType&&!Ot.test(g+C.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),u=g.indexOf(":")<0&&"on"+g,(e=e[C.expando]?e:new C.Event(g,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=m.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:C.makeArray(t,[e]),f=C.event.special[g]||{},r||!f.trigger||!1!==f.trigger.apply(n,t))){if(!r&&!f.noBubble&&!y(n)){for(l=f.delegateType||g,Ot.test(l+g)||(a=a.parentNode);a;a=a.parentNode)p.push(a),s=a;s===(n.ownerDocument||b)&&p.push(s.defaultView||s.parentWindow||i)}for(o=0;(a=p[o++])&&!e.isPropagationStopped();)d=a,e.type=o>1?l:f.bindType||g,(c=(se.get(a,"events")||Object.create(null))[e.type]&&se.get(a,"handle"))&&c.apply(a,t),(c=u&&a[u])&&c.apply&&oe(a)&&(e.result=c.apply(a,t),!1===e.result&&e.preventDefault());return e.type=g,r||e.isDefaultPrevented()||f._default&&!1!==f._default.apply(p.pop(),t)||!oe(n)||u&&v(n[g])&&!y(n)&&((s=n[u])&&(n[u]=null),C.event.triggered=g,e.isPropagationStopped()&&d.addEventListener(g,It),n[g](),e.isPropagationStopped()&&d.removeEventListener(g,It),C.event.triggered=void 0,s&&(n[u]=s)),e.result}},simulate:function(e,t,n){var i=C.extend(new C.Event,n,{type:e,isSimulated:!0});C.event.trigger(i,null,t)}}),C.fn.extend({trigger:function(e,t){return this.each(function(){C.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return C.event.trigger(e,t,n,!0)}});var Lt=/\[\]$/,Pt=/\r?\n/g,qt=/^(?:submit|button|image|reset|file)$/i,Ht=/^(?:input|select|textarea|keygen)/i;function Rt(e,t,n,i){var r;if(Array.isArray(t))C.each(t,function(t,r){n||Lt.test(e)?i(e,r):Rt(e+"["+("object"==typeof r&&null!=r?t:"")+"]",r,n,i)});else if(n||"object"!==x(t))i(e,t);else for(r in t)Rt(e+"["+r+"]",t[r],n,i)}C.param=function(e,t){var n,i=[],r=function(e,t){var n=v(t)?t():t;i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!C.isPlainObject(e))C.each(e,function(){r(this.name,this.value)});else for(n in e)Rt(n,e[n],t,r);return i.join("&")},C.fn.extend({serialize:function(){return C.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=C.prop(this,"elements");return e?C.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!C(this).is(":disabled")&&Ht.test(this.nodeName)&&!qt.test(e)&&(this.checked||!Ce.test(e))}).map(function(e,t){var n=C(this).val();return null==n?null:Array.isArray(n)?C.map(n,function(e){return{name:t.name,value:e.replace(Pt,"\r\n")}}):{name:t.name,value:n.replace(Pt,"\r\n")}}).get()}});var Ft=/%20/g,Mt=/#.*$/,Bt=/([?&])_=[^&]*/,Wt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ut=/^(?:GET|HEAD)$/,$t=/^\/\//,Qt={},Vt={},zt="*/".concat("*"),Xt=b.createElement("a");function Yt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,r=0,o=t.toLowerCase().match(z)||[];if(v(n))for(;i=o[r++];)"+"===i[0]?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function Kt(e,t,n,i){var r={},o=e===Vt;function a(s){var l;return r[s]=!0,C.each(e[s]||[],function(e,s){var u=s(t,n,i);return"string"!=typeof u||o||r[u]?o?!(l=u):void 0:(t.dataTypes.unshift(u),a(u),!1)}),l}return a(t.dataTypes[0])||!r["*"]&&a("*")}function Gt(e,t){var n,i,r=C.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((r[n]?e:i||(i={}))[n]=t[n]);return i&&C.extend(!0,e,i),e}Xt.href=At.href,C.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:At.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(At.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":zt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":C.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Gt(Gt(e,C.ajaxSettings),t):Gt(C.ajaxSettings,e)},ajaxPrefilter:Yt(Qt),ajaxTransport:Yt(Vt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n,r,o,a,s,l,u,c,f,d,h=C.ajaxSetup({},t),p=h.context||h,g=h.context&&(p.nodeType||p.jquery)?C(p):C.event,m=C.Deferred(),v=C.Callbacks("once memory"),y=h.statusCode||{},_={},w={},x="canceled",E={readyState:0,getResponseHeader:function(e){var t;if(u){if(!a)for(a={};t=Wt.exec(o);)a[t[1].toLowerCase()+" "]=(a[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=a[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return u?o:null},setRequestHeader:function(e,t){return null==u&&(e=w[e.toLowerCase()]=w[e.toLowerCase()]||e,_[e]=t),this},overrideMimeType:function(e){return null==u&&(h.mimeType=e),this},statusCode:function(e){var t;if(e)if(u)E.always(e[E.status]);else for(t in e)y[t]=[y[t],e[t]];return this},abort:function(e){var t=e||x;return n&&n.abort(t),T(0,t),this}};if(m.promise(E),h.url=((e||h.url||At.href)+"").replace($t,At.protocol+"//"),h.type=t.method||t.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(z)||[""],null==h.crossDomain){l=b.createElement("a");try{l.href=h.url,l.href=l.href,h.crossDomain=Xt.protocol+"//"+Xt.host!=l.protocol+"//"+l.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=C.param(h.data,h.traditional)),Kt(Qt,h,t,E),u)return E;for(f in(c=C.event&&h.global)&&0===C.active++&&C.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!Ut.test(h.type),r=h.url.replace(Mt,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(Ft,"+")):(d=h.url.slice(r.length),h.data&&(h.processData||"string"==typeof h.data)&&(r+=(jt.test(r)?"&":"?")+h.data,delete h.data),!1===h.cache&&(r=r.replace(Bt,"$1"),d=(jt.test(r)?"&":"?")+"_="+Nt.guid+++d),h.url=r+d),h.ifModified&&(C.lastModified[r]&&E.setRequestHeader("If-Modified-Since",C.lastModified[r]),C.etag[r]&&E.setRequestHeader("If-None-Match",C.etag[r])),(h.data&&h.hasContent&&!1!==h.contentType||t.contentType)&&E.setRequestHeader("Content-Type",h.contentType),E.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+zt+"; q=0.01":""):h.accepts["*"]),h.headers)E.setRequestHeader(f,h.headers[f]);if(h.beforeSend&&(!1===h.beforeSend.call(p,E,h)||u))return E.abort();if(x="abort",v.add(h.complete),E.done(h.success),E.fail(h.error),n=Kt(Vt,h,t,E)){if(E.readyState=1,c&&g.trigger("ajaxSend",[E,h]),u)return E;h.async&&h.timeout>0&&(s=i.setTimeout(function(){E.abort("timeout")},h.timeout));try{u=!1,n.send(_,T)}catch(e){if(u)throw e;T(-1,e)}}else T(-1,"No Transport");function T(e,t,a,l){var f,d,b,_,w,x=t;u||(u=!0,s&&i.clearTimeout(s),n=void 0,o=l||"",E.readyState=e>0?4:0,f=e>=200&&e<300||304===e,a&&(_=function(e,t,n){for(var i,r,o,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(r in s)if(s[r]&&s[r].test(i)){l.unshift(r);break}if(l[0]in n)o=l[0];else{for(r in n){if(!l[0]||e.converters[r+" "+l[0]]){o=r;break}a||(a=r)}o=o||a}if(o)return o!==l[0]&&l.unshift(o),n[o]}(h,E,a)),!f&&C.inArray("script",h.dataTypes)>-1&&C.inArray("json",h.dataTypes)<0&&(h.converters["text script"]=function(){}),_=function(e,t,n,i){var r,o,a,s,l,u={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)u[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(a=u[l+" "+o]||u["* "+o]))for(r in u)if((s=r.split(" "))[1]===o&&(a=u[l+" "+s[0]]||u["* "+s[0]])){!0===a?a=u[r]:!0!==u[r]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(h,_,E,f),f?(h.ifModified&&((w=E.getResponseHeader("Last-Modified"))&&(C.lastModified[r]=w),(w=E.getResponseHeader("etag"))&&(C.etag[r]=w)),204===e||"HEAD"===h.type?x="nocontent":304===e?x="notmodified":(x=_.state,d=_.data,f=!(b=_.error))):(b=x,!e&&x||(x="error",e<0&&(e=0))),E.status=e,E.statusText=(t||x)+"",f?m.resolveWith(p,[d,x,E]):m.rejectWith(p,[E,x,b]),E.statusCode(y),y=void 0,c&&g.trigger(f?"ajaxSuccess":"ajaxError",[E,h,f?d:b]),v.fireWith(p,[E,x]),c&&(g.trigger("ajaxComplete",[E,h]),--C.active||C.event.trigger("ajaxStop")))}return E},getJSON:function(e,t,n){return C.get(e,t,n,"json")},getScript:function(e,t){return C.get(e,void 0,t,"script")}}),C.each(["get","post"],function(e,t){C[t]=function(e,n,i,r){return v(n)&&(r=r||i,i=n,n=void 0),C.ajax(C.extend({url:e,type:t,dataType:r,data:n,success:i},C.isPlainObject(e)&&e))}}),C.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),C._evalUrl=function(e,t,n){return C.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){C.globalEval(e,t,n)}})},C.fn.extend({wrapAll:function(e){var t;return this[0]&&(v(e)&&(e=e.call(this[0])),t=C(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return v(e)?this.each(function(t){C(this).wrapInner(e.call(this,t))}):this.each(function(){var t=C(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=v(e);return this.each(function(n){C(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){C(this).replaceWith(this.childNodes)}),this}}),C.expr.pseudos.hidden=function(e){return!C.expr.pseudos.visible(e)},C.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},C.ajaxSettings.xhr=function(){try{return new i.XMLHttpRequest}catch(e){}};var Jt={0:200,1223:204},Zt=C.ajaxSettings.xhr();m.cors=!!Zt&&"withCredentials"in Zt,m.ajax=Zt=!!Zt,C.ajaxTransport(function(e){var t,n;if(m.cors||Zt&&!e.crossDomain)return{send:function(r,o){var a,s=e.xhr();if(s.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(a in e.xhrFields)s[a]=e.xhrFields[a];for(a in e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||r["X-Requested-With"]||(r["X-Requested-With"]="XMLHttpRequest"),r)s.setRequestHeader(a,r[a]);t=function(e){return function(){t&&(t=n=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?o(0,"error"):o(s.status,s.statusText):o(Jt[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=t(),n=s.onerror=s.ontimeout=t("error"),void 0!==s.onabort?s.onabort=n:s.onreadystatechange=function(){4===s.readyState&&i.setTimeout(function(){t&&n()})},t=t("abort");try{s.send(e.hasContent&&e.data||null)}catch(e){if(t)throw e}},abort:function(){t&&t()}}}),C.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),C.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return C.globalEval(e),e}}}),C.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),C.ajaxTransport("script",function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(i,r){t=C("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&r("error"===e.type?404:200,e.type)}),b.head.appendChild(t[0])},abort:function(){n&&n()}}});var en,tn=[],nn=/(=)\?(?=&|$)|\?\?/;C.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=tn.pop()||C.expando+"_"+Nt.guid++;return this[e]=!0,e}}),C.ajaxPrefilter("json jsonp",function(e,t,n){var r,o,a,s=!1!==e.jsonp&&(nn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=v(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(nn,"$1"+r):!1!==e.jsonp&&(e.url+=(jt.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return a||C.error(r+" was not called"),a[0]},e.dataTypes[0]="json",o=i[r],i[r]=function(){a=arguments},n.always(function(){void 0===o?C(i).removeProp(r):i[r]=o,e[r]&&(e.jsonpCallback=t.jsonpCallback,tn.push(r)),a&&v(o)&&o(a[0]),a=o=void 0}),"script"}),m.createHTMLDocument=((en=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===en.childNodes.length),C.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(m.createHTMLDocument?((i=(t=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,t.head.appendChild(i)):t=b),o=!n&&[],(r=M.exec(e))?[t.createElement(r[1])]:(r=Oe([e],t,o),o&&o.length&&C(o).remove(),C.merge([],r.childNodes)));var i,r,o},C.fn.load=function(e,t,n){var i,r,o,a=this,s=e.indexOf(" ");return s>-1&&(i=Ct(e.slice(s)),e=e.slice(0,s)),v(t)?(n=t,t=void 0):t&&"object"==typeof t&&(r="POST"),a.length>0&&C.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(i?C("<div>").append(C.parseHTML(e)).find(i):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},C.expr.pseudos.animated=function(e){return C.grep(C.timers,function(t){return e===t.elem}).length},C.offset={setOffset:function(e,t,n){var i,r,o,a,s,l,u=C.css(e,"position"),c=C(e),f={};"static"===u&&(e.style.position="relative"),s=c.offset(),o=C.css(e,"top"),l=C.css(e,"left"),("absolute"===u||"fixed"===u)&&(o+l).indexOf("auto")>-1?(a=(i=c.position()).top,r=i.left):(a=parseFloat(o)||0,r=parseFloat(l)||0),v(t)&&(t=t.call(e,n,C.extend({},s))),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+r),"using"in t?t.using.call(e,f):c.css(f)}},C.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){C.offset.setOffset(this,e,t)});var t,n,i=this[0];return i?i.getClientRects().length?(t=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],r={top:0,left:0};if("fixed"===C.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===C.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((r=C(e).offset()).top+=C.css(e,"borderTopWidth",!0),r.left+=C.css(e,"borderLeftWidth",!0))}return{top:t.top-r.top-C.css(i,"marginTop",!0),left:t.left-r.left-C.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===C.css(e,"position");)e=e.offsetParent;return e||ge})}}),C.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;C.fn[e]=function(i){return ee(this,function(e,i,r){var o;if(y(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===r)return o?o[t]:e[i];o?o.scrollTo(n?o.pageXOffset:r,n?r:o.pageYOffset):e[i]=r},e,i,arguments.length)}}),C.each(["top","left"],function(e,t){C.cssHooks[t]=et(m.pixelPosition,function(e,n){if(n)return n=Ze(e,t),Xe.test(n)?C(e).position()[t]+"px":n})}),C.each({Height:"height",Width:"width"},function(e,t){C.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){C.fn[i]=function(r,o){var a=arguments.length&&(n||"boolean"!=typeof r),s=n||(!0===r||!0===o?"margin":"border");return ee(this,function(t,n,r){var o;return y(t)?0===i.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===r?C.css(t,n,s):C.style(t,n,r,s)},t,a?r:void 0,a)}})}),C.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){C.fn[t]=function(e){return this.on(t,e)}}),C.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),C.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){C.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}});var rn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;C.proxy=function(e,t){var n,i,r;if("string"==typeof t&&(n=e[t],t=e,e=n),v(e))return i=s.call(arguments,2),r=function(){return e.apply(t||this,i.concat(s.call(arguments)))},r.guid=e.guid=e.guid||C.guid++,r},C.holdReady=function(e){e?C.readyWait++:C.ready(!0)},C.isArray=Array.isArray,C.parseJSON=JSON.parse,C.nodeName=k,C.isFunction=v,C.isWindow=y,C.camelCase=re,C.type=x,C.now=Date.now,C.isNumeric=function(e){var t=C.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},C.trim=function(e){return null==e?"":(e+"").replace(rn,"$1")},void 0===(n=function(){return C}.apply(t,[]))||(e.exports=n);var on=i.jQuery,an=i.$;return C.noConflict=function(e){return i.$===C&&(i.$=an),e&&i.jQuery===C&&(i.jQuery=on),C},void 0===r&&(i.jQuery=i.$=C),C})},699:function(e,t,n){!function(e,t){"use strict";function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=i(t);function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}function l(e){var t=this,n=!1;return r.default(this).one(u.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||u.triggerTransitionEnd(t)},e),this}var u={TRANSITION_END:"bsTransitionEnd",getUID:function(e){do{e+=~~(1e6*Math.random())}while(document.getElementById(e));return e},getSelectorFromElement:function(e){var t=e.getAttribute("data-target");if(!t||"#"===t){var n=e.getAttribute("href");t=n&&"#"!==n?n.trim():""}try{return document.querySelector(t)?t:null}catch(e){return null}},getTransitionDurationFromElement:function(e){if(!e)return 0;var t=r.default(e).css("transition-duration"),n=r.default(e).css("transition-delay"),i=parseFloat(t),o=parseFloat(n);return i||o?(t=t.split(",")[0],n=n.split(",")[0],1e3*(parseFloat(t)+parseFloat(n))):0},reflow:function(e){return e.offsetHeight},triggerTransitionEnd:function(e){r.default(e).trigger("transitionend")},supportsTransitionEnd:function(){return Boolean("transitionend")},isElement:function(e){return(e[0]||e).nodeType},typeCheckConfig:function(e,t,n){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var r=n[i],o=t[i],a=o&&u.isElement(o)?"element":null===(s=o)||void 0===s?""+s:{}.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(r).test(a))throw new Error(e.toUpperCase()+': Option "'+i+'" provided type "'+a+'" but expected type "'+r+'".')}var s},findShadowRoot:function(e){if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){var t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?u.findShadowRoot(e.parentNode):null},jQueryDetection:function(){if(void 0===r.default)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=r.default.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||e[0]>=4)throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};u.jQueryDetection(),r.default.fn.emulateTransitionEnd=l,r.default.event.special[u.TRANSITION_END]={bindType:"transitionend",delegateType:"transitionend",handle:function(e){if(r.default(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}};var c="alert",f=r.default.fn[c],d=function(){function e(e){this._element=e}var t=e.prototype;return t.close=function(e){var t=this._element;e&&(t=this._getRootElement(e)),this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},t.dispose=function(){r.default.removeData(this._element,"bs.alert"),this._element=null},t._getRootElement=function(e){var t=u.getSelectorFromElement(e),n=!1;return t&&(n=document.querySelector(t)),n||(n=r.default(e).closest(".alert")[0]),n},t._triggerCloseEvent=function(e){var t=r.default.Event("close.bs.alert");return r.default(e).trigger(t),t},t._removeElement=function(e){var t=this;if(r.default(e).removeClass("show"),r.default(e).hasClass("fade")){var n=u.getTransitionDurationFromElement(e);r.default(e).one(u.TRANSITION_END,function(n){return t._destroyElement(e,n)}).emulateTransitionEnd(n)}else this._destroyElement(e)},t._destroyElement=function(e){r.default(e).detach().trigger("closed.bs.alert").remove()},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this),i=n.data("bs.alert");i||(i=new e(this),n.data("bs.alert",i)),"close"===t&&i[t](this)})},e._handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},a(e,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),e}();r.default(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',d._handleDismiss(new d)),r.default.fn[c]=d._jQueryInterface,r.default.fn[c].Constructor=d,r.default.fn[c].noConflict=function(){return r.default.fn[c]=f,d._jQueryInterface};var h=r.default.fn.button,p=function(){function e(e){this._element=e,this.shouldAvoidTriggerChange=!1}var t=e.prototype;return t.toggle=function(){var e=!0,t=!0,n=r.default(this._element).closest('[data-toggle="buttons"]')[0];if(n){var i=this._element.querySelector('input:not([type="hidden"])');if(i){if("radio"===i.type)if(i.checked&&this._element.classList.contains("active"))e=!1;else{var o=n.querySelector(".active");o&&r.default(o).removeClass("active")}e&&("checkbox"!==i.type&&"radio"!==i.type||(i.checked=!this._element.classList.contains("active")),this.shouldAvoidTriggerChange||r.default(i).trigger("change")),i.focus(),t=!1}}this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(t&&this._element.setAttribute("aria-pressed",!this._element.classList.contains("active")),e&&r.default(this._element).toggleClass("active"))},t.dispose=function(){r.default.removeData(this._element,"bs.button"),this._element=null},e._jQueryInterface=function(t,n){return this.each(function(){var i=r.default(this),o=i.data("bs.button");o||(o=new e(this),i.data("bs.button",o)),o.shouldAvoidTriggerChange=n,"toggle"===t&&o[t]()})},a(e,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),e}();r.default(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(e){var t=e.target,n=t;if(r.default(t).hasClass("btn")||(t=r.default(t).closest(".btn")[0]),!t||t.hasAttribute("disabled")||t.classList.contains("disabled"))e.preventDefault();else{var i=t.querySelector('input:not([type="hidden"])');if(i&&(i.hasAttribute("disabled")||i.classList.contains("disabled")))return void e.preventDefault();"INPUT"!==n.tagName&&"LABEL"===t.tagName||p._jQueryInterface.call(r.default(t),"toggle","INPUT"===n.tagName)}}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(e){var t=r.default(e.target).closest(".btn")[0];r.default(t).toggleClass("focus",/^focus(in)?$/.test(e.type))}),r.default(window).on("load.bs.button.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),t=0,n=e.length;t<n;t++){var i=e[t],r=i.querySelector('input:not([type="hidden"])');r.checked||r.hasAttribute("checked")?i.classList.add("active"):i.classList.remove("active")}for(var o=0,a=(e=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<a;o++){var s=e[o];"true"===s.getAttribute("aria-pressed")?s.classList.add("active"):s.classList.remove("active")}}),r.default.fn.button=p._jQueryInterface,r.default.fn.button.Constructor=p,r.default.fn.button.noConflict=function(){return r.default.fn.button=h,p._jQueryInterface};var g="carousel",m=".bs.carousel",v=r.default.fn[g],y={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},b={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},_={TOUCH:"touch",PEN:"pen"},w=function(){function e(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||navigator.maxTouchPoints>0,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var t=e.prototype;return t.next=function(){this._isSliding||this._slide("next")},t.nextWhenVisible=function(){var e=r.default(this._element);!document.hidden&&e.is(":visible")&&"hidden"!==e.css("visibility")&&this.next()},t.prev=function(){this._isSliding||this._slide("prev")},t.pause=function(e){e||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(u.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},t.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},t.to=function(e){var t=this;this._activeElement=this._element.querySelector(".active.carousel-item");var n=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)r.default(this._element).one("slid.bs.carousel",function(){return t.to(e)});else{if(n===e)return this.pause(),void this.cycle();var i=e>n?"next":"prev";this._slide(i,this._items[e])}},t.dispose=function(){r.default(this._element).off(m),r.default.removeData(this._element,"bs.carousel"),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},t._getConfig=function(e){return e=s({},y,e),u.typeCheckConfig(g,e,b),e},t._handleSwipe=function(){var e=Math.abs(this.touchDeltaX);if(!(e<=40)){var t=e/this.touchDeltaX;this.touchDeltaX=0,t>0&&this.prev(),t<0&&this.next()}},t._addEventListeners=function(){var e=this;this._config.keyboard&&r.default(this._element).on("keydown.bs.carousel",function(t){return e._keydown(t)}),"hover"===this._config.pause&&r.default(this._element).on("mouseenter.bs.carousel",function(t){return e.pause(t)}).on("mouseleave.bs.carousel",function(t){return e.cycle(t)}),this._config.touch&&this._addTouchEventListeners()},t._addTouchEventListeners=function(){var e=this;if(this._touchSupported){var t=function(t){e._pointerEvent&&_[t.originalEvent.pointerType.toUpperCase()]?e.touchStartX=t.originalEvent.clientX:e._pointerEvent||(e.touchStartX=t.originalEvent.touches[0].clientX)},n=function(t){e._pointerEvent&&_[t.originalEvent.pointerType.toUpperCase()]&&(e.touchDeltaX=t.originalEvent.clientX-e.touchStartX),e._handleSwipe(),"hover"===e._config.pause&&(e.pause(),e.touchTimeout&&clearTimeout(e.touchTimeout),e.touchTimeout=setTimeout(function(t){return e.cycle(t)},500+e._config.interval))};r.default(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(e){return e.preventDefault()}),this._pointerEvent?(r.default(this._element).on("pointerdown.bs.carousel",function(e){return t(e)}),r.default(this._element).on("pointerup.bs.carousel",function(e){return n(e)}),this._element.classList.add("pointer-event")):(r.default(this._element).on("touchstart.bs.carousel",function(e){return t(e)}),r.default(this._element).on("touchmove.bs.carousel",function(t){return function(t){t.originalEvent.touches&&t.originalEvent.touches.length>1?e.touchDeltaX=0:e.touchDeltaX=t.originalEvent.touches[0].clientX-e.touchStartX}(t)}),r.default(this._element).on("touchend.bs.carousel",function(e){return n(e)}))}},t._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next()}},t._getItemIndex=function(e){return this._items=e&&e.parentNode?[].slice.call(e.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(e)},t._getItemByDirection=function(e,t){var n="next"===e,i="prev"===e,r=this._getItemIndex(t),o=this._items.length-1;if((i&&0===r||n&&r===o)&&!this._config.wrap)return t;var a=(r+("prev"===e?-1:1))%this._items.length;return-1===a?this._items[this._items.length-1]:this._items[a]},t._triggerSlideEvent=function(e,t){var n=this._getItemIndex(e),i=this._getItemIndex(this._element.querySelector(".active.carousel-item")),o=r.default.Event("slide.bs.carousel",{relatedTarget:e,direction:t,from:i,to:n});return r.default(this._element).trigger(o),o},t._setActiveIndicatorElement=function(e){if(this._indicatorsElement){var t=[].slice.call(this._indicatorsElement.querySelectorAll(".active"));r.default(t).removeClass("active");var n=this._indicatorsElement.children[this._getItemIndex(e)];n&&r.default(n).addClass("active")}},t._slide=function(e,t){var n,i,o,a=this,s=this._element.querySelector(".active.carousel-item"),l=this._getItemIndex(s),c=t||s&&this._getItemByDirection(e,s),f=this._getItemIndex(c),d=Boolean(this._interval);if("next"===e?(n="carousel-item-left",i="carousel-item-next",o="left"):(n="carousel-item-right",i="carousel-item-prev",o="right"),c&&r.default(c).hasClass("active"))this._isSliding=!1;else if(!this._triggerSlideEvent(c,o).isDefaultPrevented()&&s&&c){this._isSliding=!0,d&&this.pause(),this._setActiveIndicatorElement(c);var h=r.default.Event("slid.bs.carousel",{relatedTarget:c,direction:o,from:l,to:f});if(r.default(this._element).hasClass("slide")){r.default(c).addClass(i),u.reflow(c),r.default(s).addClass(n),r.default(c).addClass(n);var p=parseInt(c.getAttribute("data-interval"),10);p?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=p):this._config.interval=this._config.defaultInterval||this._config.interval;var g=u.getTransitionDurationFromElement(s);r.default(s).one(u.TRANSITION_END,function(){r.default(c).removeClass(n+" "+i).addClass("active"),r.default(s).removeClass("active "+i+" "+n),a._isSliding=!1,setTimeout(function(){return r.default(a._element).trigger(h)},0)}).emulateTransitionEnd(g)}else r.default(s).removeClass("active"),r.default(c).addClass("active"),this._isSliding=!1,r.default(this._element).trigger(h);d&&this.cycle()}},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this).data("bs.carousel"),i=s({},y,r.default(this).data());"object"==typeof t&&(i=s({},i,t));var o="string"==typeof t?t:i.slide;if(n||(n=new e(this,i),r.default(this).data("bs.carousel",n)),"number"==typeof t)n.to(t);else if("string"==typeof o){if(void 0===n[o])throw new TypeError('No method named "'+o+'"');n[o]()}else i.interval&&i.ride&&(n.pause(),n.cycle())})},e._dataApiClickHandler=function(t){var n=u.getSelectorFromElement(this);if(n){var i=r.default(n)[0];if(i&&r.default(i).hasClass("carousel")){var o=s({},r.default(i).data(),r.default(this).data()),a=this.getAttribute("data-slide-to");a&&(o.interval=!1),e._jQueryInterface.call(r.default(i),o),a&&r.default(i).data("bs.carousel").to(a),t.preventDefault()}}},a(e,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return y}}]),e}();r.default(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",w._dataApiClickHandler),r.default(window).on("load.bs.carousel.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),t=0,n=e.length;t<n;t++){var i=r.default(e[t]);w._jQueryInterface.call(i,i.data())}}),r.default.fn[g]=w._jQueryInterface,r.default.fn[g].Constructor=w,r.default.fn[g].noConflict=function(){return r.default.fn[g]=v,w._jQueryInterface};var x="collapse",E=r.default.fn[x],T={toggle:!0,parent:""},C={toggle:"boolean",parent:"(string|element)"},S=function(){function e(e,t){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(t),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'));for(var n=[].slice.call(document.querySelectorAll('[data-toggle="collapse"]')),i=0,r=n.length;i<r;i++){var o=n[i],a=u.getSelectorFromElement(o),s=[].slice.call(document.querySelectorAll(a)).filter(function(t){return t===e});null!==a&&s.length>0&&(this._selector=a,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var t=e.prototype;return t.toggle=function(){r.default(this._element).hasClass("show")?this.hide():this.show()},t.show=function(){var t,n,i=this;if(!(this._isTransitioning||r.default(this._element).hasClass("show")||(this._parent&&0===(t=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(e){return"string"==typeof i._config.parent?e.getAttribute("data-parent")===i._config.parent:e.classList.contains("collapse")})).length&&(t=null),t&&(n=r.default(t).not(this._selector).data("bs.collapse"))&&n._isTransitioning))){var o=r.default.Event("show.bs.collapse");if(r.default(this._element).trigger(o),!o.isDefaultPrevented()){t&&(e._jQueryInterface.call(r.default(t).not(this._selector),"hide"),n||r.default(t).data("bs.collapse",null));var a=this._getDimension();r.default(this._element).removeClass("collapse").addClass("collapsing"),this._element.style[a]=0,this._triggerArray.length&&r.default(this._triggerArray).removeClass("collapsed").attr("aria-expanded",!0),this.setTransitioning(!0);var s="scroll"+(a[0].toUpperCase()+a.slice(1)),l=u.getTransitionDurationFromElement(this._element);r.default(this._element).one(u.TRANSITION_END,function(){r.default(i._element).removeClass("collapsing").addClass("collapse show"),i._element.style[a]="",i.setTransitioning(!1),r.default(i._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(l),this._element.style[a]=this._element[s]+"px"}}},t.hide=function(){var e=this;if(!this._isTransitioning&&r.default(this._element).hasClass("show")){var t=r.default.Event("hide.bs.collapse");if(r.default(this._element).trigger(t),!t.isDefaultPrevented()){var n=this._getDimension();this._element.style[n]=this._element.getBoundingClientRect()[n]+"px",u.reflow(this._element),r.default(this._element).addClass("collapsing").removeClass("collapse show");var i=this._triggerArray.length;if(i>0)for(var o=0;o<i;o++){var a=this._triggerArray[o],s=u.getSelectorFromElement(a);null!==s&&(r.default([].slice.call(document.querySelectorAll(s))).hasClass("show")||r.default(a).addClass("collapsed").attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[n]="";var l=u.getTransitionDurationFromElement(this._element);r.default(this._element).one(u.TRANSITION_END,function(){e.setTransitioning(!1),r.default(e._element).removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")}).emulateTransitionEnd(l)}}},t.setTransitioning=function(e){this._isTransitioning=e},t.dispose=function(){r.default.removeData(this._element,"bs.collapse"),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},t._getConfig=function(e){return(e=s({},T,e)).toggle=Boolean(e.toggle),u.typeCheckConfig(x,e,C),e},t._getDimension=function(){return r.default(this._element).hasClass("width")?"width":"height"},t._getParent=function(){var t,n=this;u.isElement(this._config.parent)?(t=this._config.parent,void 0!==this._config.parent.jquery&&(t=this._config.parent[0])):t=document.querySelector(this._config.parent);var i='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',o=[].slice.call(t.querySelectorAll(i));return r.default(o).each(function(t,i){n._addAriaAndCollapsedClass(e._getTargetFromElement(i),[i])}),t},t._addAriaAndCollapsedClass=function(e,t){var n=r.default(e).hasClass("show");t.length&&r.default(t).toggleClass("collapsed",!n).attr("aria-expanded",n)},e._getTargetFromElement=function(e){var t=u.getSelectorFromElement(e);return t?document.querySelector(t):null},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this),i=n.data("bs.collapse"),o=s({},T,n.data(),"object"==typeof t&&t?t:{});if(!i&&o.toggle&&"string"==typeof t&&/show|hide/.test(t)&&(o.toggle=!1),i||(i=new e(this,o),n.data("bs.collapse",i)),"string"==typeof t){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t]()}})},a(e,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return T}}]),e}();r.default(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var t=r.default(this),n=u.getSelectorFromElement(this),i=[].slice.call(document.querySelectorAll(n));r.default(i).each(function(){var e=r.default(this),n=e.data("bs.collapse")?"toggle":t.data();S._jQueryInterface.call(e,n)})}),r.default.fn[x]=S._jQueryInterface,r.default.fn[x].Constructor=S,r.default.fn[x].noConflict=function(){return r.default.fn[x]=E,S._jQueryInterface};var k="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,D=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(k&&navigator.userAgent.indexOf(e[t])>=0)return 1;return 0}(),A=k&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},D))}};function N(e){return e&&"[object Function]"==={}.toString.call(e)}function j(e,t){if(1!==e.nodeType)return[];var n=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?n[t]:n}function O(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function I(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=j(e),n=t.overflow,i=t.overflowX,r=t.overflowY;return/(auto|scroll|overlay)/.test(n+r+i)?e:I(O(e))}function L(e){return e&&e.referenceNode?e.referenceNode:e}var P=k&&!(!window.MSInputMethodContext||!document.documentMode),q=k&&/MSIE 10/.test(navigator.userAgent);function H(e){return 11===e?P:10===e?q:P||q}function R(e){if(!e)return document.documentElement;for(var t=H(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var i=n&&n.nodeName;return i&&"BODY"!==i&&"HTML"!==i?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===j(n,"position")?R(n):n:e?e.ownerDocument.documentElement:document.documentElement}function F(e){return null!==e.parentNode?F(e.parentNode):e}function M(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,i=n?e:t,r=n?t:e,o=document.createRange();o.setStart(i,0),o.setEnd(r,0);var a,s,l=o.commonAncestorContainer;if(e!==l&&t!==l||i.contains(r))return"BODY"===(s=(a=l).nodeName)||"HTML"!==s&&R(a.firstElementChild)!==a?R(l):l;var u=F(e);return u.host?M(u.host,t):M(e,F(t).host)}function B(e){var t="top"===(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top")?"scrollTop":"scrollLeft",n=e.nodeName;if("BODY"===n||"HTML"===n){var i=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||i)[t]}return e[t]}function W(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=B(t,"top"),r=B(t,"left"),o=n?-1:1;return e.top+=i*o,e.bottom+=i*o,e.left+=r*o,e.right+=r*o,e}function U(e,t){var n="x"===t?"Left":"Top",i="Left"===n?"Right":"Bottom";return parseFloat(e["border"+n+"Width"])+parseFloat(e["border"+i+"Width"])}function $(e,t,n,i){return Math.max(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],H(10)?parseInt(n["offset"+e])+parseInt(i["margin"+("Height"===e?"Top":"Left")])+parseInt(i["margin"+("Height"===e?"Bottom":"Right")]):0)}function Q(e){var t=e.body,n=e.documentElement,i=H(10)&&getComputedStyle(n);return{height:$("Height",t,n,i),width:$("Width",t,n,i)}}var V=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},z=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),X=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},Y=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};function K(e){return Y({},e,{right:e.left+e.width,bottom:e.top+e.height})}function G(e){var t={};try{if(H(10)){t=e.getBoundingClientRect();var n=B(e,"top"),i=B(e,"left");t.top+=n,t.left+=i,t.bottom+=n,t.right+=i}else t=e.getBoundingClientRect()}catch(e){}var r={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},o="HTML"===e.nodeName?Q(e.ownerDocument):{},a=o.width||e.clientWidth||r.width,s=o.height||e.clientHeight||r.height,l=e.offsetWidth-a,u=e.offsetHeight-s;if(l||u){var c=j(e);l-=U(c,"x"),u-=U(c,"y"),r.width-=l,r.height-=u}return K(r)}function J(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=H(10),r="HTML"===t.nodeName,o=G(e),a=G(t),s=I(e),l=j(t),u=parseFloat(l.borderTopWidth),c=parseFloat(l.borderLeftWidth);n&&r&&(a.top=Math.max(a.top,0),a.left=Math.max(a.left,0));var f=K({top:o.top-a.top-u,left:o.left-a.left-c,width:o.width,height:o.height});if(f.marginTop=0,f.marginLeft=0,!i&&r){var d=parseFloat(l.marginTop),h=parseFloat(l.marginLeft);f.top-=u-d,f.bottom-=u-d,f.left-=c-h,f.right-=c-h,f.marginTop=d,f.marginLeft=h}return(i&&!n?t.contains(s):t===s&&"BODY"!==s.nodeName)&&(f=W(f,t)),f}function Z(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.ownerDocument.documentElement,i=J(e,n),r=Math.max(n.clientWidth,window.innerWidth||0),o=Math.max(n.clientHeight,window.innerHeight||0),a=t?0:B(n),s=t?0:B(n,"left");return K({top:a-i.top+i.marginTop,left:s-i.left+i.marginLeft,width:r,height:o})}function ee(e){var t=e.nodeName;if("BODY"===t||"HTML"===t)return!1;if("fixed"===j(e,"position"))return!0;var n=O(e);return!!n&&ee(n)}function te(e){if(!e||!e.parentElement||H())return document.documentElement;for(var t=e.parentElement;t&&"none"===j(t,"transform");)t=t.parentElement;return t||document.documentElement}function ne(e,t,n,i){var r=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o={top:0,left:0},a=r?te(e):M(e,L(t));if("viewport"===i)o=Z(a,r);else{var s=void 0;"scrollParent"===i?"BODY"===(s=I(O(t))).nodeName&&(s=e.ownerDocument.documentElement):s="window"===i?e.ownerDocument.documentElement:i;var l=J(s,a,r);if("HTML"!==s.nodeName||ee(a))o=l;else{var u=Q(e.ownerDocument),c=u.height,f=u.width;o.top+=l.top-l.marginTop,o.bottom=c+l.top,o.left+=l.left-l.marginLeft,o.right=f+l.left}}var d="number"==typeof(n=n||0);return o.left+=d?n:n.left||0,o.top+=d?n:n.top||0,o.right-=d?n:n.right||0,o.bottom-=d?n:n.bottom||0,o}function ie(e){return e.width*e.height}function re(e,t,n,i,r){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;if(-1===e.indexOf("auto"))return e;var a=ne(n,i,o,r),s={top:{width:a.width,height:t.top-a.top},right:{width:a.right-t.right,height:a.height},bottom:{width:a.width,height:a.bottom-t.bottom},left:{width:t.left-a.left,height:a.height}},l=Object.keys(s).map(function(e){return Y({key:e},s[e],{area:ie(s[e])})}).sort(function(e,t){return t.area-e.area}),u=l.filter(function(e){var t=e.width,i=e.height;return t>=n.clientWidth&&i>=n.clientHeight}),c=u.length>0?u[0].key:l[0].key,f=e.split("-")[1];return c+(f?"-"+f:"")}function oe(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return J(n,i?te(t):M(t,L(n)),i)}function ae(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),n=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),i=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+i,height:e.offsetHeight+n}}function se(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function le(e,t,n){n=n.split("-")[0];var i=ae(e),r={width:i.width,height:i.height},o=-1!==["right","left"].indexOf(n),a=o?"top":"left",s=o?"left":"top",l=o?"height":"width",u=o?"width":"height";return r[a]=t[a]+t[l]/2-i[l]/2,r[s]=n===s?t[s]-i[u]:t[se(s)],r}function ue(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function ce(e,t,n){return(void 0===n?e:e.slice(0,function(e,t,n){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===n});var i=ue(e,function(e){return e[t]===n});return e.indexOf(i)}(e,"name",n))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var n=e.function||e.fn;e.enabled&&N(n)&&(t.offsets.popper=K(t.offsets.popper),t.offsets.reference=K(t.offsets.reference),t=n(t,e))}),t}function fe(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=oe(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=re(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=le(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=ce(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function de(e,t){return e.some(function(e){var n=e.name;return e.enabled&&n===t})}function he(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),i=0;i<t.length;i++){var r=t[i],o=r?""+r+n:e;if(void 0!==document.body.style[o])return o}return null}function pe(){return this.state.isDestroyed=!0,de(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[he("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function ge(e){var t=e.ownerDocument;return t?t.defaultView:window}function me(e,t,n,i){n.updateBound=i,ge(e).addEventListener("resize",n.updateBound,{passive:!0});var r=I(e);return function e(t,n,i,r){var o="BODY"===t.nodeName,a=o?t.ownerDocument.defaultView:t;a.addEventListener(n,i,{passive:!0}),o||e(I(a.parentNode),n,i,r),r.push(a)}(r,"scroll",n.updateBound,n.scrollParents),n.scrollElement=r,n.eventsEnabled=!0,n}function ve(){this.state.eventsEnabled||(this.state=me(this.reference,this.options,this.state,this.scheduleUpdate))}function ye(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,ge(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function be(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function _e(e,t){Object.keys(t).forEach(function(n){var i="";-1!==["width","height","top","right","bottom","left"].indexOf(n)&&be(t[n])&&(i="px"),e.style[n]=t[n]+i})}var we=k&&/Firefox/i.test(navigator.userAgent);function xe(e,t,n){var i=ue(e,function(e){return e.name===t}),r=!!i&&e.some(function(e){return e.name===n&&e.enabled&&e.order<i.order});if(!r){var o="`"+t+"`",a="`"+n+"`";console.warn(a+" modifier is required by "+o+" modifier in order to work, be sure to include it before "+o+"!")}return r}var Ee=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Te=Ee.slice(3);function Ce(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=Te.indexOf(e),i=Te.slice(n+1).concat(Te.slice(0,n));return t?i.reverse():i}var Se="flip",ke="clockwise",De="counterclockwise";function Ae(e,t,n,i){var r=[0,0],o=-1!==["right","left"].indexOf(i),a=e.split(/(\+|\-)/).map(function(e){return e.trim()}),s=a.indexOf(ue(a,function(e){return-1!==e.search(/,|\s/)}));a[s]&&-1===a[s].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/,u=-1!==s?[a.slice(0,s).concat([a[s].split(l)[0]]),[a[s].split(l)[1]].concat(a.slice(s+1))]:[a];return(u=u.map(function(e,i){var r=(1===i?!o:o)?"height":"width",a=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,a=!0,e):a?(e[e.length-1]+=t,a=!1,e):e.concat(t)},[]).map(function(e){return function(e,t,n,i){var r=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+r[1],a=r[2];if(!o)return e;if(0===a.indexOf("%")){return K("%p"===a?n:i)[t]/100*o}return"vh"===a||"vw"===a?("vh"===a?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*o:o}(e,r,t,n)})})).forEach(function(e,t){e.forEach(function(n,i){be(n)&&(r[t]+=n*("-"===e[i-1]?-1:1))})}),r}var Ne={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t=e.placement,n=t.split("-")[0],i=t.split("-")[1];if(i){var r=e.offsets,o=r.reference,a=r.popper,s=-1!==["bottom","top"].indexOf(n),l=s?"left":"top",u=s?"width":"height",c={start:X({},l,o[l]),end:X({},l,o[l]+o[u]-a[u])};e.offsets.popper=Y({},a,c[i])}return e}},offset:{order:200,enabled:!0,fn:function(e,t){var n=t.offset,i=e.placement,r=e.offsets,o=r.popper,a=r.reference,s=i.split("-")[0],l=void 0;return l=be(+n)?[+n,0]:Ae(n,o,a,s),"left"===s?(o.top+=l[0],o.left-=l[1]):"right"===s?(o.top+=l[0],o.left+=l[1]):"top"===s?(o.left+=l[0],o.top-=l[1]):"bottom"===s&&(o.left+=l[0],o.top+=l[1]),e.popper=o,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,t){var n=t.boundariesElement||R(e.instance.popper);e.instance.reference===n&&(n=R(n));var i=he("transform"),r=e.instance.popper.style,o=r.top,a=r.left,s=r[i];r.top="",r.left="",r[i]="";var l=ne(e.instance.popper,e.instance.reference,t.padding,n,e.positionFixed);r.top=o,r.left=a,r[i]=s,t.boundaries=l;var u=t.priority,c=e.offsets.popper,f={primary:function(e){var n=c[e];return c[e]<l[e]&&!t.escapeWithReference&&(n=Math.max(c[e],l[e])),X({},e,n)},secondary:function(e){var n="right"===e?"left":"top",i=c[n];return c[e]>l[e]&&!t.escapeWithReference&&(i=Math.min(c[n],l[e]-("right"===e?c.width:c.height))),X({},n,i)}};return u.forEach(function(e){var t=-1!==["left","top"].indexOf(e)?"primary":"secondary";c=Y({},c,f[t](e))}),e.offsets.popper=c,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,i=t.reference,r=e.placement.split("-")[0],o=Math.floor,a=-1!==["top","bottom"].indexOf(r),s=a?"right":"bottom",l=a?"left":"top",u=a?"width":"height";return n[s]<o(i[l])&&(e.offsets.popper[l]=o(i[l])-n[u]),n[l]>o(i[s])&&(e.offsets.popper[l]=o(i[s])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){var n;if(!xe(e.instance.modifiers,"arrow","keepTogether"))return e;var i=t.element;if("string"==typeof i){if(!(i=e.instance.popper.querySelector(i)))return e}else if(!e.instance.popper.contains(i))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var r=e.placement.split("-")[0],o=e.offsets,a=o.popper,s=o.reference,l=-1!==["left","right"].indexOf(r),u=l?"height":"width",c=l?"Top":"Left",f=c.toLowerCase(),d=l?"left":"top",h=l?"bottom":"right",p=ae(i)[u];s[h]-p<a[f]&&(e.offsets.popper[f]-=a[f]-(s[h]-p)),s[f]+p>a[h]&&(e.offsets.popper[f]+=s[f]+p-a[h]),e.offsets.popper=K(e.offsets.popper);var g=s[f]+s[u]/2-p/2,m=j(e.instance.popper),v=parseFloat(m["margin"+c]),y=parseFloat(m["border"+c+"Width"]),b=g-e.offsets.popper[f]-v-y;return b=Math.max(Math.min(a[u]-p,b),0),e.arrowElement=i,e.offsets.arrow=(X(n={},f,Math.round(b)),X(n,d,""),n),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(e,t){if(de(e.instance.modifiers,"inner"))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var n=ne(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),i=e.placement.split("-")[0],r=se(i),o=e.placement.split("-")[1]||"",a=[];switch(t.behavior){case Se:a=[i,r];break;case ke:a=Ce(i);break;case De:a=Ce(i,!0);break;default:a=t.behavior}return a.forEach(function(s,l){if(i!==s||a.length===l+1)return e;i=e.placement.split("-")[0],r=se(i);var u=e.offsets.popper,c=e.offsets.reference,f=Math.floor,d="left"===i&&f(u.right)>f(c.left)||"right"===i&&f(u.left)<f(c.right)||"top"===i&&f(u.bottom)>f(c.top)||"bottom"===i&&f(u.top)<f(c.bottom),h=f(u.left)<f(n.left),p=f(u.right)>f(n.right),g=f(u.top)<f(n.top),m=f(u.bottom)>f(n.bottom),v="left"===i&&h||"right"===i&&p||"top"===i&&g||"bottom"===i&&m,y=-1!==["top","bottom"].indexOf(i),b=!!t.flipVariations&&(y&&"start"===o&&h||y&&"end"===o&&p||!y&&"start"===o&&g||!y&&"end"===o&&m),_=!!t.flipVariationsByContent&&(y&&"start"===o&&p||y&&"end"===o&&h||!y&&"start"===o&&m||!y&&"end"===o&&g),w=b||_;(d||v||w)&&(e.flipped=!0,(d||v)&&(i=a[l+1]),w&&(o=function(e){return"end"===e?"start":"start"===e?"end":e}(o)),e.placement=i+(o?"-"+o:""),e.offsets.popper=Y({},e.offsets.popper,le(e.instance.popper,e.offsets.reference,e.placement)),e=ce(e.instance.modifiers,e,"flip"))}),e},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],i=e.offsets,r=i.popper,o=i.reference,a=-1!==["left","right"].indexOf(n),s=-1===["top","left"].indexOf(n);return r[a?"left":"top"]=o[n]-(s?r[a?"width":"height"]:0),e.placement=se(t),e.offsets.popper=K(r),e}},hide:{order:800,enabled:!0,fn:function(e){if(!xe(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=ue(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n=t.x,i=t.y,r=e.offsets.popper,o=ue(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration;void 0!==o&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var a=void 0!==o?o:t.gpuAcceleration,s=R(e.instance.popper),l=G(s),u={position:r.position},c=function(e,t){var n=e.offsets,i=n.popper,r=n.reference,o=Math.round,a=Math.floor,s=function(e){return e},l=o(r.width),u=o(i.width),c=-1!==["left","right"].indexOf(e.placement),f=-1!==e.placement.indexOf("-"),d=t?c||f||l%2==u%2?o:a:s,h=t?o:s;return{left:d(l%2==1&&u%2==1&&!f&&t?i.left-1:i.left),top:h(i.top),bottom:h(i.bottom),right:d(i.right)}}(e,window.devicePixelRatio<2||!we),f="bottom"===n?"top":"bottom",d="right"===i?"left":"right",h=he("transform"),p=void 0,g=void 0;if(g="bottom"===f?"HTML"===s.nodeName?-s.clientHeight+c.bottom:-l.height+c.bottom:c.top,p="right"===d?"HTML"===s.nodeName?-s.clientWidth+c.right:-l.width+c.right:c.left,a&&h)u[h]="translate3d("+p+"px, "+g+"px, 0)",u[f]=0,u[d]=0,u.willChange="transform";else{var m="bottom"===f?-1:1,v="right"===d?-1:1;u[f]=g*m,u[d]=p*v,u.willChange=f+", "+d}var y={"x-placement":e.placement};return e.attributes=Y({},y,e.attributes),e.styles=Y({},u,e.styles),e.arrowStyles=Y({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){var t,n;return _e(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach(function(e){!1!==n[e]?t.setAttribute(e,n[e]):t.removeAttribute(e)}),e.arrowElement&&Object.keys(e.arrowStyles).length&&_e(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,n,i,r){var o=oe(r,t,e,n.positionFixed),a=re(n.placement,o,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",a),_e(t,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},je=function(){function e(t,n){var i=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};V(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=A(this.update.bind(this)),this.options=Y({},e.Defaults,r),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=n&&n.jquery?n[0]:n,this.options.modifiers={},Object.keys(Y({},e.Defaults.modifiers,r.modifiers)).forEach(function(t){i.options.modifiers[t]=Y({},e.Defaults.modifiers[t]||{},r.modifiers?r.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return Y({name:e},i.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&N(e.onLoad)&&e.onLoad(i.reference,i.popper,i.options,e,i.state)}),this.update();var o=this.options.eventsEnabled;o&&this.enableEventListeners(),this.state.eventsEnabled=o}return z(e,[{key:"update",value:function(){return fe.call(this)}},{key:"destroy",value:function(){return pe.call(this)}},{key:"enableEventListeners",value:function(){return ve.call(this)}},{key:"disableEventListeners",value:function(){return ye.call(this)}}]),e}();je.Utils=("undefined"!=typeof window?window:n.g).PopperUtils,je.placements=Ee,je.Defaults=Ne;var Oe="dropdown",Ie=r.default.fn[Oe],Le=new RegExp("38|40|27"),Pe={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},qe={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},He=function(){function e(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var t=e.prototype;return t.toggle=function(){if(!this._element.disabled&&!r.default(this._element).hasClass("disabled")){var t=r.default(this._menu).hasClass("show");e._clearMenus(),t||this.show(!0)}},t.show=function(t){if(void 0===t&&(t=!1),!(this._element.disabled||r.default(this._element).hasClass("disabled")||r.default(this._menu).hasClass("show"))){var n={relatedTarget:this._element},i=r.default.Event("show.bs.dropdown",n),o=e._getParentFromElement(this._element);if(r.default(o).trigger(i),!i.isDefaultPrevented()){if(!this._inNavbar&&t){if(void 0===je)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");var a=this._element;"parent"===this._config.reference?a=o:u.isElement(this._config.reference)&&(a=this._config.reference,void 0!==this._config.reference.jquery&&(a=this._config.reference[0])),"scrollParent"!==this._config.boundary&&r.default(o).addClass("position-static"),this._popper=new je(a,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===r.default(o).closest(".navbar-nav").length&&r.default(document.body).children().on("mouseover",null,r.default.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),r.default(this._menu).toggleClass("show"),r.default(o).toggleClass("show").trigger(r.default.Event("shown.bs.dropdown",n))}}},t.hide=function(){if(!this._element.disabled&&!r.default(this._element).hasClass("disabled")&&r.default(this._menu).hasClass("show")){var t={relatedTarget:this._element},n=r.default.Event("hide.bs.dropdown",t),i=e._getParentFromElement(this._element);r.default(i).trigger(n),n.isDefaultPrevented()||(this._popper&&this._popper.destroy(),r.default(this._menu).toggleClass("show"),r.default(i).toggleClass("show").trigger(r.default.Event("hidden.bs.dropdown",t)))}},t.dispose=function(){r.default.removeData(this._element,"bs.dropdown"),r.default(this._element).off(".bs.dropdown"),this._element=null,this._menu=null,null!==this._popper&&(this._popper.destroy(),this._popper=null)},t.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},t._addEventListeners=function(){var e=this;r.default(this._element).on("click.bs.dropdown",function(t){t.preventDefault(),t.stopPropagation(),e.toggle()})},t._getConfig=function(e){return e=s({},this.constructor.Default,r.default(this._element).data(),e),u.typeCheckConfig(Oe,e,this.constructor.DefaultType),e},t._getMenuElement=function(){if(!this._menu){var t=e._getParentFromElement(this._element);t&&(this._menu=t.querySelector(".dropdown-menu"))}return this._menu},t._getPlacement=function(){var e=r.default(this._element.parentNode),t="bottom-start";return e.hasClass("dropup")?t=r.default(this._menu).hasClass("dropdown-menu-right")?"top-end":"top-start":e.hasClass("dropright")?t="right-start":e.hasClass("dropleft")?t="left-start":r.default(this._menu).hasClass("dropdown-menu-right")&&(t="bottom-end"),t},t._detectNavbar=function(){return r.default(this._element).closest(".navbar").length>0},t._getOffset=function(){var e=this,t={};return"function"==typeof this._config.offset?t.fn=function(t){return t.offsets=s({},t.offsets,e._config.offset(t.offsets,e._element)||{}),t}:t.offset=this._config.offset,t},t._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(e.modifiers.applyStyle={enabled:!1}),s({},e,this._config.popperConfig)},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this).data("bs.dropdown");if(n||(n=new e(this,"object"==typeof t?t:null),r.default(this).data("bs.dropdown",n)),"string"==typeof t){if(void 0===n[t])throw new TypeError('No method named "'+t+'"');n[t]()}})},e._clearMenus=function(t){if(!t||3!==t.which&&("keyup"!==t.type||9===t.which))for(var n=[].slice.call(document.querySelectorAll('[data-toggle="dropdown"]')),i=0,o=n.length;i<o;i++){var a=e._getParentFromElement(n[i]),s=r.default(n[i]).data("bs.dropdown"),l={relatedTarget:n[i]};if(t&&"click"===t.type&&(l.clickEvent=t),s){var u=s._menu;if(r.default(a).hasClass("show")&&!(t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&9===t.which)&&r.default.contains(a,t.target))){var c=r.default.Event("hide.bs.dropdown",l);r.default(a).trigger(c),c.isDefaultPrevented()||("ontouchstart"in document.documentElement&&r.default(document.body).children().off("mouseover",null,r.default.noop),n[i].setAttribute("aria-expanded","false"),s._popper&&s._popper.destroy(),r.default(u).removeClass("show"),r.default(a).removeClass("show").trigger(r.default.Event("hidden.bs.dropdown",l)))}}}},e._getParentFromElement=function(e){var t,n=u.getSelectorFromElement(e);return n&&(t=document.querySelector(n)),t||e.parentNode},e._dataApiKeydownHandler=function(t){if(!(/input|textarea/i.test(t.target.tagName)?32===t.which||27!==t.which&&(40!==t.which&&38!==t.which||r.default(t.target).closest(".dropdown-menu").length):!Le.test(t.which))&&!this.disabled&&!r.default(this).hasClass("disabled")){var n=e._getParentFromElement(this),i=r.default(n).hasClass("show");if(i||27!==t.which){if(t.preventDefault(),t.stopPropagation(),!i||27===t.which||32===t.which)return 27===t.which&&r.default(n.querySelector('[data-toggle="dropdown"]')).trigger("focus"),void r.default(this).trigger("click");var o=[].slice.call(n.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(e){return r.default(e).is(":visible")});if(0!==o.length){var a=o.indexOf(t.target);38===t.which&&a>0&&a--,40===t.which&&a<o.length-1&&a++,a<0&&(a=0),o[a].focus()}}}},a(e,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return Pe}},{key:"DefaultType",get:function(){return qe}}]),e}();r.default(document).on("keydown.bs.dropdown.data-api",'[data-toggle="dropdown"]',He._dataApiKeydownHandler).on("keydown.bs.dropdown.data-api",".dropdown-menu",He._dataApiKeydownHandler).on("click.bs.dropdown.data-api keyup.bs.dropdown.data-api",He._clearMenus).on("click.bs.dropdown.data-api",'[data-toggle="dropdown"]',function(e){e.preventDefault(),e.stopPropagation(),He._jQueryInterface.call(r.default(this),"toggle")}).on("click.bs.dropdown.data-api",".dropdown form",function(e){e.stopPropagation()}),r.default.fn[Oe]=He._jQueryInterface,r.default.fn[Oe].Constructor=He,r.default.fn[Oe].noConflict=function(){return r.default.fn[Oe]=Ie,He._jQueryInterface};var Re=r.default.fn.modal,Fe={backdrop:!0,keyboard:!0,focus:!0,show:!0},Me={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},Be=function(){function e(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var t=e.prototype;return t.toggle=function(e){return this._isShown?this.hide():this.show(e)},t.show=function(e){var t=this;if(!this._isShown&&!this._isTransitioning){r.default(this._element).hasClass("fade")&&(this._isTransitioning=!0);var n=r.default.Event("show.bs.modal",{relatedTarget:e});r.default(this._element).trigger(n),this._isShown||n.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),r.default(this._element).on("click.dismiss.bs.modal",'[data-dismiss="modal"]',function(e){return t.hide(e)}),r.default(this._dialog).on("mousedown.dismiss.bs.modal",function(){r.default(t._element).one("mouseup.dismiss.bs.modal",function(e){r.default(e.target).is(t._element)&&(t._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return t._showElement(e)}))}},t.hide=function(e){var t=this;if(e&&e.preventDefault(),this._isShown&&!this._isTransitioning){var n=r.default.Event("hide.bs.modal");if(r.default(this._element).trigger(n),this._isShown&&!n.isDefaultPrevented()){this._isShown=!1;var i=r.default(this._element).hasClass("fade");if(i&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),r.default(document).off("focusin.bs.modal"),r.default(this._element).removeClass("show"),r.default(this._element).off("click.dismiss.bs.modal"),r.default(this._dialog).off("mousedown.dismiss.bs.modal"),i){var o=u.getTransitionDurationFromElement(this._element);r.default(this._element).one(u.TRANSITION_END,function(e){return t._hideModal(e)}).emulateTransitionEnd(o)}else this._hideModal()}}},t.dispose=function(){[window,this._element,this._dialog].forEach(function(e){return r.default(e).off(".bs.modal")}),r.default(document).off("focusin.bs.modal"),r.default.removeData(this._element,"bs.modal"),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},t.handleUpdate=function(){this._adjustDialog()},t._getConfig=function(e){return e=s({},Fe,e),u.typeCheckConfig("modal",e,Me),e},t._triggerBackdropTransition=function(){var e=this;if("static"===this._config.backdrop){var t=r.default.Event("hidePrevented.bs.modal");if(r.default(this._element).trigger(t),t.isDefaultPrevented())return;var n=this._element.scrollHeight>document.documentElement.clientHeight;n||(this._element.style.overflowY="hidden"),this._element.classList.add("modal-static");var i=u.getTransitionDurationFromElement(this._dialog);r.default(this._element).off(u.TRANSITION_END),r.default(this._element).one(u.TRANSITION_END,function(){e._element.classList.remove("modal-static"),n||r.default(e._element).one(u.TRANSITION_END,function(){e._element.style.overflowY=""}).emulateTransitionEnd(e._element,i)}).emulateTransitionEnd(i),this._element.focus()}else this.hide()},t._showElement=function(e){var t=this,n=r.default(this._element).hasClass("fade"),i=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),r.default(this._dialog).hasClass("modal-dialog-scrollable")&&i?i.scrollTop=0:this._element.scrollTop=0,n&&u.reflow(this._element),r.default(this._element).addClass("show"),this._config.focus&&this._enforceFocus();var o=r.default.Event("shown.bs.modal",{relatedTarget:e}),a=function(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,r.default(t._element).trigger(o)};if(n){var s=u.getTransitionDurationFromElement(this._dialog);r.default(this._dialog).one(u.TRANSITION_END,a).emulateTransitionEnd(s)}else a()},t._enforceFocus=function(){var e=this;r.default(document).off("focusin.bs.modal").on("focusin.bs.modal",function(t){document!==t.target&&e._element!==t.target&&0===r.default(e._element).has(t.target).length&&e._element.focus()})},t._setEscapeEvent=function(){var e=this;this._isShown?r.default(this._element).on("keydown.dismiss.bs.modal",function(t){e._config.keyboard&&27===t.which?(t.preventDefault(),e.hide()):e._config.keyboard||27!==t.which||e._triggerBackdropTransition()}):this._isShown||r.default(this._element).off("keydown.dismiss.bs.modal")},t._setResizeEvent=function(){var e=this;this._isShown?r.default(window).on("resize.bs.modal",function(t){return e.handleUpdate(t)}):r.default(window).off("resize.bs.modal")},t._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(function(){r.default(document.body).removeClass("modal-open"),e._resetAdjustments(),e._resetScrollbar(),r.default(e._element).trigger("hidden.bs.modal")})},t._removeBackdrop=function(){this._backdrop&&(r.default(this._backdrop).remove(),this._backdrop=null)},t._showBackdrop=function(e){var t=this,n=r.default(this._element).hasClass("fade")?"fade":"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",n&&this._backdrop.classList.add(n),r.default(this._backdrop).appendTo(document.body),r.default(this._element).on("click.dismiss.bs.modal",function(e){t._ignoreBackdropClick?t._ignoreBackdropClick=!1:e.target===e.currentTarget&&t._triggerBackdropTransition()}),n&&u.reflow(this._backdrop),r.default(this._backdrop).addClass("show"),!e)return;if(!n)return void e();var i=u.getTransitionDurationFromElement(this._backdrop);r.default(this._backdrop).one(u.TRANSITION_END,e).emulateTransitionEnd(i)}else if(!this._isShown&&this._backdrop){r.default(this._backdrop).removeClass("show");var o=function(){t._removeBackdrop(),e&&e()};if(r.default(this._element).hasClass("fade")){var a=u.getTransitionDurationFromElement(this._backdrop);r.default(this._backdrop).one(u.TRANSITION_END,o).emulateTransitionEnd(a)}else o()}else e&&e()},t._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},t._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},t._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},t._setScrollbar=function(){var e=this;if(this._isBodyOverflowing){var t=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top")),n=[].slice.call(document.querySelectorAll(".sticky-top"));r.default(t).each(function(t,n){var i=n.style.paddingRight,o=r.default(n).css("padding-right");r.default(n).data("padding-right",i).css("padding-right",parseFloat(o)+e._scrollbarWidth+"px")}),r.default(n).each(function(t,n){var i=n.style.marginRight,o=r.default(n).css("margin-right");r.default(n).data("margin-right",i).css("margin-right",parseFloat(o)-e._scrollbarWidth+"px")});var i=document.body.style.paddingRight,o=r.default(document.body).css("padding-right");r.default(document.body).data("padding-right",i).css("padding-right",parseFloat(o)+this._scrollbarWidth+"px")}r.default(document.body).addClass("modal-open")},t._resetScrollbar=function(){var e=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top"));r.default(e).each(function(e,t){var n=r.default(t).data("padding-right");r.default(t).removeData("padding-right"),t.style.paddingRight=n||""});var t=[].slice.call(document.querySelectorAll(".sticky-top"));r.default(t).each(function(e,t){var n=r.default(t).data("margin-right");void 0!==n&&r.default(t).css("margin-right",n).removeData("margin-right")});var n=r.default(document.body).data("padding-right");r.default(document.body).removeData("padding-right"),document.body.style.paddingRight=n||""},t._getScrollbarWidth=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},e._jQueryInterface=function(t,n){return this.each(function(){var i=r.default(this).data("bs.modal"),o=s({},Fe,r.default(this).data(),"object"==typeof t&&t?t:{});if(i||(i=new e(this,o),r.default(this).data("bs.modal",i)),"string"==typeof t){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t](n)}else o.show&&i.show(n)})},a(e,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return Fe}}]),e}();r.default(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(e){var t,n=this,i=u.getSelectorFromElement(this);i&&(t=document.querySelector(i));var o=r.default(t).data("bs.modal")?"toggle":s({},r.default(t).data(),r.default(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var a=r.default(t).one("show.bs.modal",function(e){e.isDefaultPrevented()||a.one("hidden.bs.modal",function(){r.default(n).is(":visible")&&n.focus()})});Be._jQueryInterface.call(r.default(t),o,this)}),r.default.fn.modal=Be._jQueryInterface,r.default.fn.modal.Constructor=Be,r.default.fn.modal.noConflict=function(){return r.default.fn.modal=Re,Be._jQueryInterface};var We=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],Ue={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},$e=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,Qe=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function Ve(e,t,n){if(0===e.length)return e;if(n&&"function"==typeof n)return n(e);for(var i=(new window.DOMParser).parseFromString(e,"text/html"),r=Object.keys(t),o=[].slice.call(i.body.querySelectorAll("*")),a=function(e,n){var i=o[e],a=i.nodeName.toLowerCase();if(-1===r.indexOf(i.nodeName.toLowerCase()))return i.parentNode.removeChild(i),"continue";var s=[].slice.call(i.attributes),l=[].concat(t["*"]||[],t[a]||[]);s.forEach(function(e){(function(e,t){var n=e.nodeName.toLowerCase();if(-1!==t.indexOf(n))return-1===We.indexOf(n)||Boolean(e.nodeValue.match($e)||e.nodeValue.match(Qe));for(var i=t.filter(function(e){return e instanceof RegExp}),r=0,o=i.length;r<o;r++)if(n.match(i[r]))return!0;return!1})(e,l)||i.removeAttribute(e.nodeName)})},s=0,l=o.length;s<l;s++)a(s);return i.body.innerHTML}var ze="tooltip",Xe=r.default.fn[ze],Ye=new RegExp("(^|\\s)bs-tooltip\\S+","g"),Ke=["sanitize","whiteList","sanitizeFn"],Ge={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},Je={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Ze={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:Ue,popperConfig:null},et={HIDE:"hide.bs.tooltip",HIDDEN:"hidden.bs.tooltip",SHOW:"show.bs.tooltip",SHOWN:"shown.bs.tooltip",INSERTED:"inserted.bs.tooltip",CLICK:"click.bs.tooltip",FOCUSIN:"focusin.bs.tooltip",FOCUSOUT:"focusout.bs.tooltip",MOUSEENTER:"mouseenter.bs.tooltip",MOUSELEAVE:"mouseleave.bs.tooltip"},tt=function(){function e(e,t){if(void 0===je)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners()}var t=e.prototype;return t.enable=function(){this._isEnabled=!0},t.disable=function(){this._isEnabled=!1},t.toggleEnabled=function(){this._isEnabled=!this._isEnabled},t.toggle=function(e){if(this._isEnabled)if(e){var t=this.constructor.DATA_KEY,n=r.default(e.currentTarget).data(t);n||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),r.default(e.currentTarget).data(t,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(r.default(this.getTipElement()).hasClass("show"))return void this._leave(null,this);this._enter(null,this)}},t.dispose=function(){clearTimeout(this._timeout),r.default.removeData(this.element,this.constructor.DATA_KEY),r.default(this.element).off(this.constructor.EVENT_KEY),r.default(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&r.default(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},t.show=function(){var e=this;if("none"===r.default(this.element).css("display"))throw new Error("Please use show on visible elements");var t=r.default.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){r.default(this.element).trigger(t);var n=u.findShadowRoot(this.element),i=r.default.contains(null!==n?n:this.element.ownerDocument.documentElement,this.element);if(t.isDefaultPrevented()||!i)return;var o=this.getTipElement(),a=u.getUID(this.constructor.NAME);o.setAttribute("id",a),this.element.setAttribute("aria-describedby",a),this.setContent(),this.config.animation&&r.default(o).addClass("fade");var s="function"==typeof this.config.placement?this.config.placement.call(this,o,this.element):this.config.placement,l=this._getAttachment(s);this.addAttachmentClass(l);var c=this._getContainer();r.default(o).data(this.constructor.DATA_KEY,this),r.default.contains(this.element.ownerDocument.documentElement,this.tip)||r.default(o).appendTo(c),r.default(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new je(this.element,o,this._getPopperConfig(l)),r.default(o).addClass("show"),"ontouchstart"in document.documentElement&&r.default(document.body).children().on("mouseover",null,r.default.noop);var f=function(){e.config.animation&&e._fixTransition();var t=e._hoverState;e._hoverState=null,r.default(e.element).trigger(e.constructor.Event.SHOWN),"out"===t&&e._leave(null,e)};if(r.default(this.tip).hasClass("fade")){var d=u.getTransitionDurationFromElement(this.tip);r.default(this.tip).one(u.TRANSITION_END,f).emulateTransitionEnd(d)}else f()}},t.hide=function(e){var t=this,n=this.getTipElement(),i=r.default.Event(this.constructor.Event.HIDE),o=function(){"show"!==t._hoverState&&n.parentNode&&n.parentNode.removeChild(n),t._cleanTipClass(),t.element.removeAttribute("aria-describedby"),r.default(t.element).trigger(t.constructor.Event.HIDDEN),null!==t._popper&&t._popper.destroy(),e&&e()};if(r.default(this.element).trigger(i),!i.isDefaultPrevented()){if(r.default(n).removeClass("show"),"ontouchstart"in document.documentElement&&r.default(document.body).children().off("mouseover",null,r.default.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,r.default(this.tip).hasClass("fade")){var a=u.getTransitionDurationFromElement(n);r.default(n).one(u.TRANSITION_END,o).emulateTransitionEnd(a)}else o();this._hoverState=""}},t.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},t.isWithContent=function(){return Boolean(this.getTitle())},t.addAttachmentClass=function(e){r.default(this.getTipElement()).addClass("bs-tooltip-"+e)},t.getTipElement=function(){return this.tip=this.tip||r.default(this.config.template)[0],this.tip},t.setContent=function(){var e=this.getTipElement();this.setElementContent(r.default(e.querySelectorAll(".tooltip-inner")),this.getTitle()),r.default(e).removeClass("fade show")},t.setElementContent=function(e,t){"object"!=typeof t||!t.nodeType&&!t.jquery?this.config.html?(this.config.sanitize&&(t=Ve(t,this.config.whiteList,this.config.sanitizeFn)),e.html(t)):e.text(t):this.config.html?r.default(t).parent().is(e)||e.empty().append(t):e.text(r.default(t).text())},t.getTitle=function(){var e=this.element.getAttribute("data-original-title");return e||(e="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),e},t._getPopperConfig=function(e){var t=this;return s({},{placement:e,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){return t._handlePopperPlacementChange(e)}},this.config.popperConfig)},t._getOffset=function(){var e=this,t={};return"function"==typeof this.config.offset?t.fn=function(t){return t.offsets=s({},t.offsets,e.config.offset(t.offsets,e.element)||{}),t}:t.offset=this.config.offset,t},t._getContainer=function(){return!1===this.config.container?document.body:u.isElement(this.config.container)?r.default(this.config.container):r.default(document).find(this.config.container)},t._getAttachment=function(e){return Je[e.toUpperCase()]},t._setListeners=function(){var e=this;this.config.trigger.split(" ").forEach(function(t){if("click"===t)r.default(e.element).on(e.constructor.Event.CLICK,e.config.selector,function(t){return e.toggle(t)});else if("manual"!==t){var n="hover"===t?e.constructor.Event.MOUSEENTER:e.constructor.Event.FOCUSIN,i="hover"===t?e.constructor.Event.MOUSELEAVE:e.constructor.Event.FOCUSOUT;r.default(e.element).on(n,e.config.selector,function(t){return e._enter(t)}).on(i,e.config.selector,function(t){return e._leave(t)})}}),this._hideModalHandler=function(){e.element&&e.hide()},r.default(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=s({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},t._fixTitle=function(){var e=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==e)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},t._enter=function(e,t){var n=this.constructor.DATA_KEY;(t=t||r.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),r.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusin"===e.type?"focus":"hover"]=!0),r.default(t.getTipElement()).hasClass("show")||"show"===t._hoverState?t._hoverState="show":(clearTimeout(t._timeout),t._hoverState="show",t.config.delay&&t.config.delay.show?t._timeout=setTimeout(function(){"show"===t._hoverState&&t.show()},t.config.delay.show):t.show())},t._leave=function(e,t){var n=this.constructor.DATA_KEY;(t=t||r.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),r.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusout"===e.type?"focus":"hover"]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState="out",t.config.delay&&t.config.delay.hide?t._timeout=setTimeout(function(){"out"===t._hoverState&&t.hide()},t.config.delay.hide):t.hide())},t._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},t._getConfig=function(e){var t=r.default(this.element).data();return Object.keys(t).forEach(function(e){-1!==Ke.indexOf(e)&&delete t[e]}),"number"==typeof(e=s({},this.constructor.Default,t,"object"==typeof e&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),u.typeCheckConfig(ze,e,this.constructor.DefaultType),e.sanitize&&(e.template=Ve(e.template,e.whiteList,e.sanitizeFn)),e},t._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},t._cleanTipClass=function(){var e=r.default(this.getTipElement()),t=e.attr("class").match(Ye);null!==t&&t.length&&e.removeClass(t.join(""))},t._handlePopperPlacementChange=function(e){this.tip=e.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))},t._fixTransition=function(){var e=this.getTipElement(),t=this.config.animation;null===e.getAttribute("x-placement")&&(r.default(e).removeClass("fade"),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this),i=n.data("bs.tooltip"),o="object"==typeof t&&t;if((i||!/dispose|hide/.test(t))&&(i||(i=new e(this,o),n.data("bs.tooltip",i)),"string"==typeof t)){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t]()}})},a(e,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return Ze}},{key:"NAME",get:function(){return ze}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return et}},{key:"EVENT_KEY",get:function(){return".bs.tooltip"}},{key:"DefaultType",get:function(){return Ge}}]),e}();r.default.fn[ze]=tt._jQueryInterface,r.default.fn[ze].Constructor=tt,r.default.fn[ze].noConflict=function(){return r.default.fn[ze]=Xe,tt._jQueryInterface};var nt="popover",it=r.default.fn[nt],rt=new RegExp("(^|\\s)bs-popover\\S+","g"),ot=s({},tt.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),at=s({},tt.DefaultType,{content:"(string|element|function)"}),st={HIDE:"hide.bs.popover",HIDDEN:"hidden.bs.popover",SHOW:"show.bs.popover",SHOWN:"shown.bs.popover",INSERTED:"inserted.bs.popover",CLICK:"click.bs.popover",FOCUSIN:"focusin.bs.popover",FOCUSOUT:"focusout.bs.popover",MOUSEENTER:"mouseenter.bs.popover",MOUSELEAVE:"mouseleave.bs.popover"},lt=function(e){var t,n;function i(){return e.apply(this,arguments)||this}n=e,(t=i).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var o=i.prototype;return o.isWithContent=function(){return this.getTitle()||this._getContent()},o.addAttachmentClass=function(e){r.default(this.getTipElement()).addClass("bs-popover-"+e)},o.getTipElement=function(){return this.tip=this.tip||r.default(this.config.template)[0],this.tip},o.setContent=function(){var e=r.default(this.getTipElement());this.setElementContent(e.find(".popover-header"),this.getTitle());var t=this._getContent();"function"==typeof t&&(t=t.call(this.element)),this.setElementContent(e.find(".popover-body"),t),e.removeClass("fade show")},o._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},o._cleanTipClass=function(){var e=r.default(this.getTipElement()),t=e.attr("class").match(rt);null!==t&&t.length>0&&e.removeClass(t.join(""))},i._jQueryInterface=function(e){return this.each(function(){var t=r.default(this).data("bs.popover"),n="object"==typeof e?e:null;if((t||!/dispose|hide/.test(e))&&(t||(t=new i(this,n),r.default(this).data("bs.popover",t)),"string"==typeof e)){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},a(i,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return ot}},{key:"NAME",get:function(){return nt}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return st}},{key:"EVENT_KEY",get:function(){return".bs.popover"}},{key:"DefaultType",get:function(){return at}}]),i}(tt);r.default.fn[nt]=lt._jQueryInterface,r.default.fn[nt].Constructor=lt,r.default.fn[nt].noConflict=function(){return r.default.fn[nt]=it,lt._jQueryInterface};var ut="scrollspy",ct=r.default.fn[ut],ft={offset:10,method:"auto",target:""},dt={offset:"number",method:"string",target:"(string|element)"},ht=function(){function e(e,t){var n=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(t),this._selector=this._config.target+" .nav-link,"+this._config.target+" .list-group-item,"+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,r.default(this._scrollElement).on("scroll.bs.scrollspy",function(e){return n._process(e)}),this.refresh(),this._process()}var t=e.prototype;return t.refresh=function(){var e=this,t=this._scrollElement===this._scrollElement.window?"offset":"position",n="auto"===this._config.method?t:this._config.method,i="position"===n?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(e){var t,o=u.getSelectorFromElement(e);if(o&&(t=document.querySelector(o)),t){var a=t.getBoundingClientRect();if(a.width||a.height)return[r.default(t)[n]().top+i,o]}return null}).filter(function(e){return e}).sort(function(e,t){return e[0]-t[0]}).forEach(function(t){e._offsets.push(t[0]),e._targets.push(t[1])})},t.dispose=function(){r.default.removeData(this._element,"bs.scrollspy"),r.default(this._scrollElement).off(".bs.scrollspy"),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},t._getConfig=function(e){if("string"!=typeof(e=s({},ft,"object"==typeof e&&e?e:{})).target&&u.isElement(e.target)){var t=r.default(e.target).attr("id");t||(t=u.getUID(ut),r.default(e.target).attr("id",t)),e.target="#"+t}return u.typeCheckConfig(ut,e,dt),e},t._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},t._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},t._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},t._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),e>=n){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&e<this._offsets[0]&&this._offsets[0]>0)return this._activeTarget=null,void this._clear();for(var r=this._offsets.length;r--;)this._activeTarget!==this._targets[r]&&e>=this._offsets[r]&&(void 0===this._offsets[r+1]||e<this._offsets[r+1])&&this._activate(this._targets[r])}},t._activate=function(e){this._activeTarget=e,this._clear();var t=this._selector.split(",").map(function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'}),n=r.default([].slice.call(document.querySelectorAll(t.join(","))));n.hasClass("dropdown-item")?(n.closest(".dropdown").find(".dropdown-toggle").addClass("active"),n.addClass("active")):(n.addClass("active"),n.parents(".nav, .list-group").prev(".nav-link, .list-group-item").addClass("active"),n.parents(".nav, .list-group").prev(".nav-item").children(".nav-link").addClass("active")),r.default(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:e})},t._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(e){return e.classList.contains("active")}).forEach(function(e){return e.classList.remove("active")})},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this).data("bs.scrollspy");if(n||(n=new e(this,"object"==typeof t&&t),r.default(this).data("bs.scrollspy",n)),"string"==typeof t){if(void 0===n[t])throw new TypeError('No method named "'+t+'"');n[t]()}})},a(e,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return ft}}]),e}();r.default(window).on("load.bs.scrollspy.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),t=e.length;t--;){var n=r.default(e[t]);ht._jQueryInterface.call(n,n.data())}}),r.default.fn[ut]=ht._jQueryInterface,r.default.fn[ut].Constructor=ht,r.default.fn[ut].noConflict=function(){return r.default.fn[ut]=ct,ht._jQueryInterface};var pt=r.default.fn.tab,gt=function(){function e(e){this._element=e}var t=e.prototype;return t.show=function(){var e=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&r.default(this._element).hasClass("active")||r.default(this._element).hasClass("disabled"))){var t,n,i=r.default(this._element).closest(".nav, .list-group")[0],o=u.getSelectorFromElement(this._element);if(i){var a="UL"===i.nodeName||"OL"===i.nodeName?"> li > .active":".active";n=(n=r.default.makeArray(r.default(i).find(a)))[n.length-1]}var s=r.default.Event("hide.bs.tab",{relatedTarget:this._element}),l=r.default.Event("show.bs.tab",{relatedTarget:n});if(n&&r.default(n).trigger(s),r.default(this._element).trigger(l),!l.isDefaultPrevented()&&!s.isDefaultPrevented()){o&&(t=document.querySelector(o)),this._activate(this._element,i);var c=function(){var t=r.default.Event("hidden.bs.tab",{relatedTarget:e._element}),i=r.default.Event("shown.bs.tab",{relatedTarget:n});r.default(n).trigger(t),r.default(e._element).trigger(i)};t?this._activate(t,t.parentNode,c):c()}}},t.dispose=function(){r.default.removeData(this._element,"bs.tab"),this._element=null},t._activate=function(e,t,n){var i=this,o=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?r.default(t).children(".active"):r.default(t).find("> li > .active"))[0],a=n&&o&&r.default(o).hasClass("fade"),s=function(){return i._transitionComplete(e,o,n)};if(o&&a){var l=u.getTransitionDurationFromElement(o);r.default(o).removeClass("show").one(u.TRANSITION_END,s).emulateTransitionEnd(l)}else s()},t._transitionComplete=function(e,t,n){if(t){r.default(t).removeClass("active");var i=r.default(t.parentNode).find("> .dropdown-menu .active")[0];i&&r.default(i).removeClass("active"),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1)}if(r.default(e).addClass("active"),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),u.reflow(e),e.classList.contains("fade")&&e.classList.add("show"),e.parentNode&&r.default(e.parentNode).hasClass("dropdown-menu")){var o=r.default(e).closest(".dropdown")[0];if(o){var a=[].slice.call(o.querySelectorAll(".dropdown-toggle"));r.default(a).addClass("active")}e.setAttribute("aria-expanded",!0)}n&&n()},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this),i=n.data("bs.tab");if(i||(i=new e(this),n.data("bs.tab",i)),"string"==typeof t){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t]()}})},a(e,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),e}();r.default(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(e){e.preventDefault(),gt._jQueryInterface.call(r.default(this),"show")}),r.default.fn.tab=gt._jQueryInterface,r.default.fn.tab.Constructor=gt,r.default.fn.tab.noConflict=function(){return r.default.fn.tab=pt,gt._jQueryInterface};var mt=r.default.fn.toast,vt={animation:"boolean",autohide:"boolean",delay:"number"},yt={animation:!0,autohide:!0,delay:500},bt=function(){function e(e,t){this._element=e,this._config=this._getConfig(t),this._timeout=null,this._setListeners()}var t=e.prototype;return t.show=function(){var e=this,t=r.default.Event("show.bs.toast");if(r.default(this._element).trigger(t),!t.isDefaultPrevented()){this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");var n=function(){e._element.classList.remove("showing"),e._element.classList.add("show"),r.default(e._element).trigger("shown.bs.toast"),e._config.autohide&&(e._timeout=setTimeout(function(){e.hide()},e._config.delay))};if(this._element.classList.remove("hide"),u.reflow(this._element),this._element.classList.add("showing"),this._config.animation){var i=u.getTransitionDurationFromElement(this._element);r.default(this._element).one(u.TRANSITION_END,n).emulateTransitionEnd(i)}else n()}},t.hide=function(){if(this._element.classList.contains("show")){var e=r.default.Event("hide.bs.toast");r.default(this._element).trigger(e),e.isDefaultPrevented()||this._close()}},t.dispose=function(){this._clearTimeout(),this._element.classList.contains("show")&&this._element.classList.remove("show"),r.default(this._element).off("click.dismiss.bs.toast"),r.default.removeData(this._element,"bs.toast"),this._element=null,this._config=null},t._getConfig=function(e){return e=s({},yt,r.default(this._element).data(),"object"==typeof e&&e?e:{}),u.typeCheckConfig("toast",e,this.constructor.DefaultType),e},t._setListeners=function(){var e=this;r.default(this._element).on("click.dismiss.bs.toast",'[data-dismiss="toast"]',function(){return e.hide()})},t._close=function(){var e=this,t=function(){e._element.classList.add("hide"),r.default(e._element).trigger("hidden.bs.toast")};if(this._element.classList.remove("show"),this._config.animation){var n=u.getTransitionDurationFromElement(this._element);r.default(this._element).one(u.TRANSITION_END,t).emulateTransitionEnd(n)}else t()},t._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this),i=n.data("bs.toast");if(i||(i=new e(this,"object"==typeof t&&t),n.data("bs.toast",i)),"string"==typeof t){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t](this)}})},a(e,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"DefaultType",get:function(){return vt}},{key:"Default",get:function(){return yt}}]),e}();r.default.fn.toast=bt._jQueryInterface,r.default.fn.toast.Constructor=bt,r.default.fn.toast.noConflict=function(){return r.default.fn.toast=mt,bt._jQueryInterface},e.Alert=d,e.Button=p,e.Carousel=w,e.Collapse=S,e.Dropdown=He,e.Modal=Be,e.Popover=lt,e.Scrollspy=ht,e.Tab=gt,e.Toast=bt,e.Tooltip=tt,e.Util=u,Object.defineProperty(e,"__esModule",{value:!0})}(t,n(692))}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var o=t[i]={exports:{}};return e[i].call(o.exports,o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(692),t=n.n(e);window.$=t(),window.jQuery=t(),n(699),n(413)})()})();