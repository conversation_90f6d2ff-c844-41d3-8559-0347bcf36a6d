﻿// jQuery
import $ from "jquery";
window.$ = window.jQuery = $;

// Kendo (from npm)
// layout_entry.js
import "@progress/kendo-ui";                // loads all Kendo UI widgets
import kendoAspNetMvc from "@progress/kendo-ui/esm/kendo.aspnetmvc.js";  // ASP.NET MVC specific

// Ensure kendo is available globally
window.kendo = window.kendo || {};
// The aspnetmvc module should have added syncReady to kendo, but let's make sure it's available
if (window.kendo && !window.kendo.syncReady) {
    console.warn("kendo.syncReady not found, this may cause issues with server-side controls");
}

//import "/wwwroot/assets/kendo/kendo.all.min_old_notrequired";
//import "/wwwroot/assets/kendo/kendo.aspnetmvc.min";

// Extra libs from local assets
import "/wwwroot/assets/kendo/jszip.js";
import "/wwwroot/assets/kendo/toastify-js.js";
import "/wwwroot/assets/libs/bootstrap/js/bootstrap.bundle.min.js";
import "/wwwroot/assets/libs/metismenu/metisMenu.min.js";
import "/wwwroot/assets/libs/simplebar/simplebar.min.js";
import "/wwwroot/assets/libs/node-waves/waves.min.js";
import "/wwwroot/assets/js/lang/jquery.multiLanguage.js";
import "/wwwroot/assets/libs/sweetalert2/sweetalert2.all.min.js";
import "/wwwroot/assets/js/app.js";
